// Simple Sanity test
const { createClient } = require('@sanity/client');

const projectId = 'ymt3haei';
const dataset = 'production';

const sanityClient = createClient({
  projectId,
  dataset,
  useCdn: true,
  apiVersion: '2024-01-01',
});

async function testSanity() {
  try {
    console.log('Testing Sanity connection...');
    console.log('Project ID:', projectId);
    console.log('Dataset:', dataset);
    
    const products = await sanityClient.fetch('*[_type == "product"][0...5]');
    console.log('Products found:', products.length);
    console.log('First product:', products[0]);
    
    const categories = await sanityClient.fetch('*[_type == "category"][0...5]');
    console.log('Categories found:', categories.length);
    console.log('First category:', categories[0]);
    
  } catch (error) {
    console.error('Sanity test failed:', error);
  }
}

testSanity();
