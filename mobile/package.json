{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@clerk/clerk-expo": "^2.14.0", "@clerk/clerk-react": "^5.32.2", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@sanity/client": "^7.6.0", "expo": "~53.0.13", "expo-auth-session": "~6.2.0", "expo-barcode-scanner": "^13.0.1", "expo-camera": "^16.1.9", "expo-crypto": "~14.1.5", "expo-image-picker": "^16.1.4", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.5", "expo-secure-store": "^14.2.3", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}