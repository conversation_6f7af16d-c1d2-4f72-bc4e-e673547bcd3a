import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  RefreshControl,
  FlatList,
  Dimensions,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { ProductCard } from '../components/ProductCard';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { ErrorBoundary, ErrorDisplay, Loading } from '../components/ErrorBoundary';
import { Product, Category, RootStackParamList } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants';
import { apiService } from '../services/api';
import { sanityClient, queries } from '../lib/sanity';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Main'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const { width: screenWidth } = Dimensions.get('window');

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const [featuredProducts, setFeaturedProducts] = useState<Product[]>([]);
  const [recentProducts, setRecentProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use Sanity directly for better performance and real-time data
      const [featuredProductsData, recentProductsData, categoriesData] = await Promise.all([
        sanityClient.fetch(queries.featuredProducts),
        sanityClient.fetch(queries.products + '[0...10]'), // Get first 10 recent products
        sanityClient.fetch(queries.categories),
      ]);

      console.log('Featured products:', featuredProductsData);
      console.log('Recent products:', recentProductsData);
      console.log('Categories:', categoriesData);

      setFeaturedProducts(featuredProductsData || []);
      setRecentProducts(recentProductsData || []);
      setCategories((categoriesData || []).slice(0, 6)); // Show first 6 categories

    } catch (error) {
      console.error('Error loading home data:', error);
      setError('Veriler yüklenirken bir hata oluştu. Lütfen internet bağlantınızı kontrol edin.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const handleProductPress = (product: Product) => {
    navigation.navigate('ProductDetail', { productId: product._id });
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      // Navigate to search results or categories screen with search query
      navigation.navigate('Categories');
    }
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity
      style={styles.categoryItem}
      onPress={() => navigation.navigate('Categories')}
    >
      <View style={styles.categoryIcon}>
        <Ionicons name="grid-outline" size={24} color={Colors.primary} />
      </View>
      <Text style={styles.categoryText} numberOfLines={2}>
        {item.name}
      </Text>
    </TouchableOpacity>
  );

  const renderProductItem = ({ item }: { item: Product }) => (
    <ProductCard
      product={item}
      onPress={handleProductPress}
      width={(screenWidth - Spacing.lg * 3) / 2}
    />
  );

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <Loading />
      </SafeAreaView>
    );
  }

  if (error && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <ErrorDisplay error={error} onRetry={loadHomeData} />
      </SafeAreaView>
    );
  }

  return (
    <ErrorBoundary>
      <SafeAreaView style={styles.container}>
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        >
        {/* Header with Search */}
        <View style={styles.header}>
          <Text style={styles.welcomeText}>Hoş Geldiniz!</Text>
          <Text style={styles.subtitle}>Ne arıyorsunuz?</Text>
          
          <View style={styles.searchContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Ürün, kategori ara..."
              value={searchQuery}
              onChangeText={setSearchQuery}
              onSubmitEditing={handleSearch}
            />
            <TouchableOpacity style={styles.searchButton} onPress={handleSearch}>
              <Ionicons name="search" size={20} color={Colors.surface} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Categories */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Kategoriler</Text>
            <TouchableOpacity onPress={() => navigation.navigate('Categories')}>
              <Text style={styles.seeAllText}>Tümünü Gör</Text>
            </TouchableOpacity>
          </View>
          
          <FlatList
            data={categories}
            renderItem={renderCategoryItem}
            keyExtractor={(item) => item._id}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.categoriesList}
          />
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <View style={styles.quickActions}>
            <Card style={styles.quickActionCard} onPress={() => navigation.navigate('Auctions')}>
              <Ionicons name="hammer" size={32} color={Colors.primary} />
              <Text style={styles.quickActionTitle}>Müzayedeler</Text>
              <Text style={styles.quickActionSubtitle}>Canlı teklifler</Text>
            </Card>
            
            <Card style={styles.quickActionCard} onPress={() => navigation.navigate('Giveaways')}>
              <Ionicons name="gift" size={32} color={Colors.secondary} />
              <Text style={styles.quickActionTitle}>Çekilişler</Text>
              <Text style={styles.quickActionSubtitle}>Ücretsiz ürünler</Text>
            </Card>
          </View>
        </View>

        {/* Featured Products */}
        {featuredProducts.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Öne Çıkan Ürünler</Text>
            </View>
            
            <FlatList
              data={featuredProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item._id}
              numColumns={2}
              columnWrapperStyle={styles.productRow}
              scrollEnabled={false}
            />
          </View>
        )}

        {/* Recent Products */}
        {recentProducts.length > 0 && (
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Yeni Ürünler</Text>
            </View>
            
            <FlatList
              data={recentProducts}
              renderItem={renderProductItem}
              keyExtractor={(item) => item._id}
              numColumns={2}
              columnWrapperStyle={styles.productRow}
              scrollEnabled={false}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: Spacing.lg,
    backgroundColor: Colors.surface,
  },
  welcomeText: {
    fontSize: Typography.fontSizes['2xl'],
    fontWeight: Typography.fontWeights.bold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  subtitle: {
    fontSize: Typography.fontSizes.base,
    color: Colors.textSecondary,
    marginBottom: Spacing.lg,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  searchInput: {
    flex: 1,
    backgroundColor: Colors.background,
    borderRadius: BorderRadius.lg,
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    fontSize: Typography.fontSizes.base,
    marginRight: Spacing.sm,
  },
  searchButton: {
    backgroundColor: Colors.primary,
    borderRadius: BorderRadius.lg,
    padding: Spacing.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    marginBottom: Spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.lg,
    marginBottom: Spacing.md,
  },
  sectionTitle: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.text,
  },
  seeAllText: {
    fontSize: Typography.fontSizes.sm,
    color: Colors.primary,
    fontWeight: Typography.fontWeights.medium,
  },
  categoriesList: {
    paddingHorizontal: Spacing.lg,
  },
  categoryItem: {
    alignItems: 'center',
    marginRight: Spacing.md,
    width: 80,
  },
  categoryIcon: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.xl,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  categoryText: {
    fontSize: Typography.fontSizes.xs,
    color: Colors.text,
    textAlign: 'center',
    lineHeight: Typography.fontSizes.xs * 1.2,
  },
  quickActions: {
    flexDirection: 'row',
    paddingHorizontal: Spacing.lg,
    gap: Spacing.md,
  },
  quickActionCard: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: Spacing.lg,
  },
  quickActionTitle: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.text,
    marginTop: Spacing.sm,
  },
  quickActionSubtitle: {
    fontSize: Typography.fontSizes.sm,
    color: Colors.textSecondary,
    marginTop: Spacing.xs,
  },
  productRow: {
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.lg,
  },
});

export default HomeScreen;
