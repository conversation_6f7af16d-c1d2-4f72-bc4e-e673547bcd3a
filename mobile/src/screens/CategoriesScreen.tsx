import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  RefreshControl,
  TouchableOpacity,
  Image,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';

import { Card } from '../components/Card';
import { ErrorBoundary, ErrorDisplay, Loading } from '../components/ErrorBoundary';
import { Category } from '../types';
import { Colors, Typography, Spacing, BorderRadius } from '../constants';
import { sanityClient, queries } from '../lib/sanity';

const CategoriesScreen: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use Sanity directly for real-time data
      const categoriesData = await sanityClient.fetch(queries.categories);

      console.log('Categories data:', categoriesData);
      setCategories(categoriesData || []);

    } catch (error) {
      console.error('Error loading categories:', error);
      setError('Kategoriler yüklenirken bir hata oluştu. Lütfen internet bağlantınızı kontrol edin.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadCategories();
    setRefreshing(false);
  };

  const handleCategoryPress = (category: Category) => {
    // Navigate to products in this category
    console.log('Category pressed:', category.name);
  };

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <Card
      style={styles.categoryCard}
      onPress={() => handleCategoryPress(item)}
    >
      <View style={styles.categoryContent}>
        <View style={styles.categoryImageContainer}>
          {item.image?.asset?.url ? (
            <Image
              source={{ uri: item.image.asset.url }}
              style={styles.categoryImage}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.categoryPlaceholder}>
              <Ionicons name="grid-outline" size={32} color={Colors.primary} />
            </View>
          )}
        </View>
        
        <View style={styles.categoryInfo}>
          <Text style={styles.categoryName}>{item.name}</Text>
          {item.description && (
            <Text style={styles.categoryDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
        </View>
        
        <View style={styles.categoryArrow}>
          <Ionicons name="chevron-forward" size={20} color={Colors.textSecondary} />
        </View>
      </View>
    </Card>
  );

  if (loading && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <Loading />
      </SafeAreaView>
    );
  }

  if (error && !refreshing) {
    return (
      <SafeAreaView style={styles.container}>
        <ErrorDisplay error={error} onRetry={loadCategories} />
      </SafeAreaView>
    );
  }

  return (
    <ErrorBoundary>
      <SafeAreaView style={styles.container}>
        <FlatList
          data={categories}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item._id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      </SafeAreaView>
    </ErrorBoundary>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  listContainer: {
    padding: Spacing.lg,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: Typography.fontSizes.base,
    color: Colors.textSecondary,
  },
  categoryCard: {
    marginBottom: Spacing.md,
  },
  categoryContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryImageContainer: {
    marginRight: Spacing.md,
  },
  categoryImage: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.lg,
  },
  categoryPlaceholder: {
    width: 60,
    height: 60,
    borderRadius: BorderRadius.lg,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  categoryInfo: {
    flex: 1,
  },
  categoryName: {
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
  },
  categoryDescription: {
    fontSize: Typography.fontSizes.sm,
    color: Colors.textSecondary,
    lineHeight: Typography.fontSizes.sm * 1.3,
  },
  categoryArrow: {
    marginLeft: Spacing.sm,
  },
});

export default CategoriesScreen;
