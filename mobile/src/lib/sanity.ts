import { createClient } from '@sanity/client';

const projectId = process.env.EXPO_PUBLIC_SANITY_PROJECT_ID;
const dataset = process.env.EXPO_PUBLIC_SANITY_DATASET;
const token = process.env.EXPO_PUBLIC_SANITY_API_TOKEN;

console.log('Sanity Config:', { projectId, dataset, hasToken: !!token });

if (!projectId || !dataset) {
  throw new Error('Missing Sanity configuration. Please check your environment variables.');
}

export const sanityClient = createClient({
  projectId,
  dataset,
  useCdn: false, // Use CDN false when using token for better performance
  apiVersion: '2024-01-01', // Use current date or latest API version
  token, // Add token for authenticated requests
});

// GROQ queries for mobile app
export const queries = {
  // Get all products with category information
  products: `*[_type == "product" && !(_id in path("drafts.**"))] {
    _id,
    name,
    description,
    price,
    discountPrice,
    images[] {
      image {
        asset-> {
          _id,
          url
        }
      },
      alt
    },
    category-> {
      _id,
      name,
      slug
    },
    vendor-> {
      _id,
      name
    },
    stock,
    isActive,
    isFeatured,
    tags,
    specifications,
    _createdAt,
    _updatedAt
  } | order(_createdAt desc)`,

  // Get featured products
  featuredProducts: `*[_type == "product" && isFeatured == true && isActive == true && !(_id in path("drafts.**"))] {
    _id,
    name,
    description,
    price,
    discountPrice,
    images[] {
      image {
        asset-> {
          _id,
          url
        }
      },
      alt
    },
    category-> {
      _id,
      name,
      slug
    },
    vendor-> {
      _id,
      name
    },
    stock,
    _createdAt
  } | order(_createdAt desc)[0...10]`,

  // Get all categories
  categories: `*[_type == "category" && !(_id in path("drafts.**"))] {
    _id,
    name,
    description,
    slug,
    image {
      asset-> {
        _id,
        url
      },
      alt
    },
    isActive,
    _createdAt
  } | order(name asc)`,

  // Get products by category
  productsByCategory: (categoryId: string) => `*[_type == "product" && category._ref == "${categoryId}" && isActive == true && !(_id in path("drafts.**"))] {
    _id,
    name,
    description,
    price,
    discountPrice,
    images[] {
      image {
        asset-> {
          _id,
          url
        }
      },
      alt
    },
    category-> {
      _id,
      name,
      slug
    },
    vendor-> {
      _id,
      name
    },
    stock,
    _createdAt
  } | order(_createdAt desc)`,

  // Get single product by ID
  productById: (productId: string) => `*[_type == "product" && _id == "${productId}" && !(_id in path("drafts.**"))][0] {
    _id,
    name,
    description,
    price,
    discountPrice,
    images[] {
      image {
        asset-> {
          _id,
          url
        }
      },
      alt
    },
    category-> {
      _id,
      name,
      slug
    },
    vendor-> {
      _id,
      name
    },
    stock,
    isActive,
    isFeatured,
    tags,
    specifications,
    _createdAt,
    _updatedAt
  }`,

  // Get auctions
  auctions: `*[_type == "auction" && !(_id in path("drafts.**"))] {
    _id,
    title,
    description,
    startingBid,
    currentBid,
    startDate,
    endDate,
    isActive,
    product-> {
      _id,
      name,
      images[] {
        asset-> {
          _id,
          url
        },
        alt
      }
    },
    _createdAt
  } | order(endDate asc)`,

  // Get giveaways
  giveaways: `*[_type == "giveaway" && !(_id in path("drafts.**"))] {
    _id,
    title,
    description,
    startDate,
    endDate,
    maxParticipants,
    currentParticipants,
    isActive,
    prize-> {
      _id,
      name,
      images[] {
        asset-> {
          _id,
          url
        },
        alt
      }
    },
    _createdAt
  } | order(endDate asc)`,
};
