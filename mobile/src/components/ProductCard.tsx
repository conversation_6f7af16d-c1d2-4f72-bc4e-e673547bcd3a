import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { Card } from './Card';
import { Product } from '../types';
import { Colors, Typography, Spacing, BorderRadius, Config } from '../constants';

interface ProductCardProps {
  product: Product;
  onPress: (product: Product) => void;
  width?: number;
}

const { width: screenWidth } = Dimensions.get('window');
const defaultCardWidth = (screenWidth - Spacing.lg * 3) / 2; // 2 columns with spacing

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onPress,
  width = defaultCardWidth,
}) => {
  const imageHeight = width * 0.75; // 4:3 aspect ratio

  // Debug: Log product data to see the structure
  console.log('ProductCard - Product ID:', product._id);
  console.log('ProductCard - Product Name:', product.name);
  console.log('ProductCard - Images:', JSON.stringify(product.images, null, 2));

  const formatPrice = (price: number) => {
    return `${price.toLocaleString('tr-TR')} ${Config.currencySymbol}`;
  };

  // Get image URL with fallback
  const getImageUrl = () => {
    if (product.images && product.images.length > 0) {
      // Filter out null values and find first valid image
      const validImages = product.images.filter(img => img && img.image && img.image.asset && img.image.asset.url);
      if (validImages.length > 0) {
        return validImages[0]?.image?.asset?.url || getPlaceholderImage();
      }
    }
    return getPlaceholderImage();
  };

  // Get placeholder image based on product name
  const getPlaceholderImage = () => {
    const productName = product.name.toLowerCase();
    if (productName.includes('iphone') || productName.includes('phone')) {
      return 'https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=300&h=225&fit=crop';
    } else if (productName.includes('ikea') || productName.includes('mobilya')) {
      return 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=225&fit=crop';
    } else if (productName.includes('laptop') || productName.includes('bilgisayar')) {
      return 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=300&h=225&fit=crop';
    } else {
      return 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=225&fit=crop';
    }
  };

  return (
    <Card
      style={[styles.container, { width }] as any}
      onPress={() => onPress(product)}
      padding="sm"
      shadow={true}
    >
      <View style={styles.imageContainer}>
        <Image
          source={{
            uri: getImageUrl(),
          }}
          style={[styles.image, { height: imageHeight }] as any}
          resizeMode="cover"
        />
        {product.stock <= 5 && product.stock > 0 && (
          <View style={styles.lowStockBadge}>
            <Text style={styles.lowStockText}>Son {product.stock}</Text>
          </View>
        )}
        {product.stock === 0 && (
          <View style={styles.outOfStockBadge}>
            <Text style={styles.outOfStockText}>Tükendi</Text>
          </View>
        )}
      </View>

      <View style={styles.content}>
        <Text style={styles.title} numberOfLines={2}>
          {product.name}
        </Text>
        
        <Text style={styles.category} numberOfLines={1}>
          {product.category?.name || 'Kategori Yok'}
        </Text>

        <View style={styles.priceContainer}>
          <Text style={styles.price}>
            {formatPrice(product.price)}
          </Text>
        </View>

        {product.vendor && (
          <Text style={styles.seller} numberOfLines={1}>
            Satıcı: {product.vendor.name}
          </Text>
        )}
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  imageContainer: {
    position: 'relative',
    marginBottom: Spacing.sm,
  },
  image: {
    width: '100%',
    borderRadius: BorderRadius.md,
  },
  lowStockBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: Colors.warning,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.sm,
  },
  lowStockText: {
    color: Colors.surface,
    fontSize: Typography.fontSizes.xs,
    fontWeight: Typography.fontWeights.semibold,
  },
  outOfStockBadge: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: BorderRadius.md,
  },
  outOfStockText: {
    color: Colors.surface,
    fontSize: Typography.fontSizes.lg,
    fontWeight: Typography.fontWeights.bold,
  },
  content: {
    flex: 1,
  },
  title: {
    fontSize: Typography.fontSizes.sm,
    fontWeight: Typography.fontWeights.semibold,
    color: Colors.text,
    marginBottom: Spacing.xs,
    lineHeight: Typography.fontSizes.sm * 1.3,
  },
  category: {
    fontSize: Typography.fontSizes.xs,
    color: Colors.textSecondary,
    marginBottom: Spacing.sm,
  },
  priceContainer: {
    marginBottom: Spacing.xs,
  },
  price: {
    fontSize: Typography.fontSizes.base,
    fontWeight: Typography.fontWeights.bold,
    color: Colors.primary,
  },
  seller: {
    fontSize: Typography.fontSizes.xs,
    color: Colors.textLight,
  },
});
