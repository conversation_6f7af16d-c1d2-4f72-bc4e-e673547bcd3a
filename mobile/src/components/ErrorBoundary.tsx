import React, { Component, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Colors, Typography } from '../constants';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: any) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.props.onError?.(error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <View style={styles.container}>
          <View style={styles.content}>
            <Text style={styles.title}>Bir Hata Oluştu</Text>
            <Text style={styles.message}>
              Uygulama beklenmedik bir hatayla karşılaştı. Lütfen tekrar deneyin.
            </Text>
            {__DEV__ && this.state.error && (
              <Text style={styles.errorDetails}>
                {this.state.error.toString()}
              </Text>
            )}
            <TouchableOpacity style={styles.retryButton} onPress={this.handleRetry}>
              <Text style={styles.retryButtonText}>Tekrar Dene</Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  content: {
    alignItems: 'center',
    maxWidth: 300,
  },
  title: {
    ...Typography.h2,
    color: Colors.error,
    marginBottom: 16,
    textAlign: 'center',
  },
  message: {
    ...Typography.body,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  errorDetails: {
    ...Typography.caption,
    color: Colors.error,
    backgroundColor: '#FEF2F2',
    padding: 12,
    borderRadius: 8,
    marginBottom: 24,
    fontFamily: 'monospace',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    ...Typography.button,
    color: Colors.surface,
  },
});

// Simple error display component
interface ErrorDisplayProps {
  error: string;
  onRetry?: () => void;
  style?: any;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({ 
  error, 
  onRetry, 
  style 
}) => {
  return (
    <View style={[styles.errorContainer, style]}>
      <Text style={styles.errorText}>{error}</Text>
      {onRetry && (
        <TouchableOpacity style={styles.retryButtonSmall} onPress={onRetry}>
          <Text style={styles.retryButtonTextSmall}>Tekrar Dene</Text>
        </TouchableOpacity>
      )}
    </View>
  );
};

const errorDisplayStyles = StyleSheet.create({
  errorContainer: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    margin: 16,
    alignItems: 'center',
  },
  errorText: {
    ...Typography.body,
    color: Colors.error,
    textAlign: 'center',
    marginBottom: 12,
  },
  retryButtonSmall: {
    backgroundColor: Colors.error,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  retryButtonTextSmall: {
    ...Typography.caption,
    color: Colors.surface,
    fontWeight: '600',
  },
});

// Merge styles
Object.assign(styles, errorDisplayStyles);

// Loading component
interface LoadingProps {
  size?: 'small' | 'large';
  color?: string;
  style?: any;
}

export const Loading: React.FC<LoadingProps> = ({ 
  size = 'large', 
  color = Colors.primary,
  style 
}) => {
  return (
    <View style={[styles.loadingContainer, style]}>
      <View 
        style={[
          styles.loadingSpinner,
          { 
            width: size === 'large' ? 40 : 24,
            height: size === 'large' ? 40 : 24,
            borderColor: `${color}20`,
            borderTopColor: color,
          }
        ]} 
      />
      <Text style={[styles.loadingText, { color }]}>Yükleniyor...</Text>
    </View>
  );
};

const loadingStyles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingSpinner: {
    borderWidth: 3,
    borderRadius: 20,
    marginBottom: 12,
  },
  loadingText: {
    ...Typography.body,
    textAlign: 'center',
  },
});

// Merge loading styles
Object.assign(styles, loadingStyles);
