import { API_BASE_URL } from '../constants';
import {
  Product,
  Category,
  Auction,
  Giveaway,
  User,
  ApiResponse,
  PaginatedResponse
} from '../types';

// Backend API response format
interface BackendResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    code: string;
    statusCode: number;
    field?: string;
    value?: any;
  };
}

class ApiService {
  private baseURL: string;
  private getToken: (() => Promise<string | null>) | null = null;

  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Set token getter function
  setTokenGetter(getToken: () => Promise<string | null>) {
    this.getToken = getToken;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;

      // Get fresh token for each request
      const token = this.getToken ? await this.getToken() : null;

      const config: RequestInit = {
        headers: {
          'Content-Type': 'application/json',
          ...(token && { 'Authorization': `Bearer ${token}` }),
          ...options.headers,
        },
        ...options,
      };

      console.log(`API Request: ${options.method || 'GET'} ${url}`);

      const response = await fetch(url, config);
      const data: BackendResponse<T> = await response.json();

      if (!response.ok) {
        const errorMessage = data.error?.message || data.message || 'API request failed';
        console.error('API Error:', {
          status: response.status,
          statusText: response.statusText,
          error: data.error,
          url,
        });

        return {
          success: false,
          error: errorMessage,
          statusCode: response.status,
        };
      }

      console.log(`API Success: ${url}`, data.data ? 'Data received' : 'No data');

      return {
        success: true,
        data: data.data as T,
        message: data.message,
      };
    } catch (error) {
      console.error('API Network Error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error occurred',
        statusCode: 0,
      };
    }
  }

  // Products API
  async getProducts(page = 1, limit = 20, filters?: {
    search?: string;
    categoryId?: string;
    vendorId?: string;
    minPrice?: number;
    maxPrice?: number;
    sortBy?: string;
    sortDirection?: 'asc' | 'desc';
    featured?: boolean;
    inStock?: boolean;
  }): Promise<ApiResponse<{ products: Product[]; pagination: any }>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      if (filters.search) params.append('search', filters.search);
      if (filters.categoryId) params.append('categoryId', filters.categoryId);
      if (filters.vendorId) params.append('vendorId', filters.vendorId);
      if (filters.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString());
      if (filters.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString());
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.sortDirection) params.append('sortDirection', filters.sortDirection);
      if (filters.featured) params.append('featured', 'true');
      if (filters.inStock) params.append('inStock', 'true');
    }

    return this.request(`/products?${params.toString()}`);
  }

  async getProduct(id: string): Promise<ApiResponse<Product>> {
    return this.request(`/products/${id}`);
  }

  async getFeaturedProducts(limit = 10): Promise<ApiResponse<Product[]>> {
    return this.request(`/products?featured=true&limit=${limit}`);
  }

  async getRecentProducts(limit = 10): Promise<ApiResponse<Product[]>> {
    return this.request(`/products?sortBy=_createdAt&sortDirection=desc&limit=${limit}`);
  }

  async searchProducts(query: string, page = 1, limit = 20): Promise<ApiResponse<{ products: Product[]; pagination: any }>> {
    return this.request(`/products?search=${encodeURIComponent(query)}&page=${page}&limit=${limit}`);
  }

  // Categories API
  async getCategories(): Promise<ApiResponse<Category[]>> {
    return this.request('/categories');
  }

  async getCategory(id: string): Promise<ApiResponse<Category>> {
    return this.request(`/categories/${id}`);
  }

  // Auctions API
  async getAuctions(page = 1, limit = 20, filters?: {
    status?: 'upcoming' | 'live' | 'ended' | 'cancelled';
    vendorId?: string;
  }): Promise<ApiResponse<{ auctions: Auction[]; pagination: any }>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      if (filters.status) params.append('status', filters.status);
      if (filters.vendorId) params.append('vendorId', filters.vendorId);
    }

    return this.request(`/auctions?${params.toString()}`);
  }

  async getAuction(id: string): Promise<ApiResponse<Auction>> {
    return this.request(`/auctions/${id}`);
  }

  async getActiveAuctions(limit = 10): Promise<ApiResponse<Auction[]>> {
    return this.request(`/auctions?status=live&limit=${limit}`);
  }

  async getAuctionBids(auctionId: string): Promise<ApiResponse<any[]>> {
    return this.request(`/auctions/${auctionId}/bids`);
  }

  async placeBid(auctionId: string, bidData: { amount: number }): Promise<ApiResponse<any>> {
    return this.request(`/auctions/${auctionId}/bids`, {
      method: 'POST',
      body: JSON.stringify(bidData),
    });
  }

  // Giveaways API
  async getGiveaways(page = 1, limit = 20, filters?: {
    status?: 'upcoming' | 'active' | 'ended' | 'cancelled';
    vendorId?: string;
  }): Promise<ApiResponse<{ giveaways: Giveaway[]; pagination: any }>> {
    const params = new URLSearchParams({
      page: page.toString(),
      limit: limit.toString(),
    });

    if (filters) {
      if (filters.status) params.append('status', filters.status);
      if (filters.vendorId) params.append('vendorId', filters.vendorId);
    }

    return this.request(`/giveaways?${params.toString()}`);
  }

  async getGiveaway(id: string): Promise<ApiResponse<Giveaway>> {
    return this.request(`/giveaways/${id}`);
  }

  async getActiveGiveaways(limit = 10): Promise<ApiResponse<Giveaway[]>> {
    return this.request(`/giveaways?status=active&limit=${limit}`);
  }

  async joinGiveaway(giveawayId: string): Promise<ApiResponse<any>> {
    return this.request(`/giveaways/${giveawayId}/join`, {
      method: 'POST',
    });
  }

  // User API
  async getUsers(page = 1, limit = 20): Promise<ApiResponse<{ users: User[]; pagination: any }>> {
    return this.request(`/users?page=${page}&limit=${limit}`);
  }

  async getUser(id: string): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}`);
  }

  async updateUser(id: string, data: Partial<User>): Promise<ApiResponse<User>> {
    return this.request(`/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteUser(id: string): Promise<ApiResponse<any>> {
    return this.request(`/users/${id}`, {
      method: 'DELETE',
    });
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<{ status: string; timestamp: string }>> {
    return this.request('/health');
  }

  // Error handling helper
  handleApiError(error: any): string {
    if (error?.statusCode === 401) {
      return 'Oturum süreniz dolmuş. Lütfen tekrar giriş yapın.';
    }
    if (error?.statusCode === 403) {
      return 'Bu işlem için yetkiniz bulunmuyor.';
    }
    if (error?.statusCode === 404) {
      return 'Aradığınız içerik bulunamadı.';
    }
    if (error?.statusCode === 429) {
      return 'Çok fazla istek gönderdiniz. Lütfen biraz bekleyin.';
    }
    if (error?.statusCode === 0) {
      return 'İnternet bağlantınızı kontrol edin.';
    }
    return error?.error || 'Bir hata oluştu. Lütfen tekrar deneyin.';
  }
}

export const apiService = new ApiService();
export default apiService;
