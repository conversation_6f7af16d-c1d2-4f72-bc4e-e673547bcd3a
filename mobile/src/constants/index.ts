// API Configuration
export const API_BASE_URL = __DEV__ 
  ? 'http://localhost:3000/api' 
  : 'https://your-production-domain.com/api';

// Colors
export const Colors = {
  primary: '#3B82F6',
  primaryDark: '#2563EB',
  secondary: '#10B981',
  accent: '#F59E0B',
  background: '#F9FAFB',
  surface: '#FFFFFF',
  text: '#111827',
  textSecondary: '#6B7280',
  textLight: '#9CA3AF',
  border: '#E5E7EB',
  error: '#EF4444',
  warning: '#F59E0B',
  success: '#10B981',
  info: '#3B82F6',
};

// Typography
export const Typography = {
  fontSizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  fontWeights: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
};

// Spacing
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
};

// Border Radius
export const BorderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  full: 9999,
};

// Screen Dimensions
export const Layout = {
  window: {
    width: 375, // Default iPhone width
    height: 812, // Default iPhone height
  },
  isSmallDevice: false, // Will be set dynamically
};

// Animation Durations
export const Animations = {
  fast: 150,
  normal: 300,
  slow: 500,
};

// App Configuration
export const Config = {
  itemsPerPage: 20,
  maxImageSize: 5 * 1024 * 1024, // 5MB
  supportedImageTypes: ['image/jpeg', 'image/png', 'image/webp'],
  currency: 'TL',
  currencySymbol: '₺',
};

// Status Bar Heights (for different devices)
export const StatusBarHeight = {
  default: 20,
  iphoneX: 44,
};

// Tab Bar Height
export const TabBarHeight = 80;

// Header Height
export const HeaderHeight = 56;
