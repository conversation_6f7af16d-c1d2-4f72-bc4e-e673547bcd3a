import React, { createContext, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import { User } from '../types/auth';

// Platform-specific Clerk hooks
let useAuth: any, useUser: any;
if (Platform.OS === 'web') {
  const clerkReact = require('@clerk/clerk-react');
  useAuth = clerkReact.useAuth;
  useUser = clerkReact.useUser;
} else {
  const clerkExpo = require('@clerk/clerk-expo');
  useAuth = clerkExpo.useAuth;
  useUser = clerkExpo.useUser;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isSignedIn: boolean;
  signOut: () => Promise<void>;
  getToken: () => Promise<string | null>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const clerkAuth = useAuth();
  const clerkUserData = useUser();
  const [user, setUser] = useState<User | null>(null);

  const { isLoaded, isSignedIn, signOut: clerkSignOut, getToken } = clerkAuth;
  const { user: clerkUser } = clerkUserData;

  useEffect(() => {
    console.log('AuthContext - Auth state changed:', {
      isLoaded,
      isSignedIn,
      hasClerkUser: !!clerkUser,
      clerkUserId: clerkUser?.id
    });

    if (isLoaded && isSignedIn && clerkUser) {
      const userData: User = {
        id: clerkUser.id,
        email: clerkUser.primaryEmailAddress?.emailAddress || '',
        firstName: clerkUser.firstName || '',
        lastName: clerkUser.lastName || '',
        imageUrl: clerkUser.imageUrl,
        role: clerkUser.publicMetadata?.role as string || 'user',
        createdAt: clerkUser.createdAt?.toISOString() || new Date().toISOString(),
        updatedAt: clerkUser.updatedAt?.toISOString() || new Date().toISOString(),
      };
      console.log('AuthContext - Setting user data:', userData);
      setUser(userData);
    } else {
      console.log('AuthContext - Clearing user data');
      setUser(null);
    }
  }, [isLoaded, isSignedIn, clerkUser]);

  const signOut = async () => {
    await clerkSignOut();
    setUser(null);
  };

  const value: AuthContextType = {
    user,
    isLoading: !isLoaded,
    isSignedIn: isSignedIn || false,
    signOut,
    getToken,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
};
