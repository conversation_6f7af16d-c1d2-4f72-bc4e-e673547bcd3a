import { useState, useEffect, useCallback } from 'react';
import { ApiResponse } from '../types';

interface UseApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  reset: () => void;
}

export function useApi<T>(
  apiCall: () => Promise<ApiResponse<T>>,
  immediate = true
): UseApiState<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(immediate);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall();
      
      if (response.success && response.data) {
        setData(response.data);
      } else {
        setError(response.error || 'Bir hata oluştu');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '<PERSON><PERSON><PERSON><PERSON>yen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  const reset = useCallback(() => {
    setData(null);
    setError(null);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    data,
    loading,
    error,
    refetch: execute,
    reset,
  };
}

// Specialized hook for paginated data
interface UsePaginatedApiState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  } | null;
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  reset: () => void;
}

export function usePaginatedApi<T>(
  apiCall: (page: number, limit: number) => Promise<ApiResponse<{ data?: T[]; pagination?: any }>>,
  limit = 20
): UsePaginatedApiState<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);

  const loadPage = useCallback(async (page: number, append = false) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall(page, limit);
      
      if (response.success && response.data) {
        const responseData = response.data as any;
        const items = responseData.data || responseData.products || responseData.auctions || responseData.giveaways || [];
        
        if (append) {
          setData(prev => [...prev, ...items]);
        } else {
          setData(items);
        }
        
        setPagination(responseData.pagination);
        setCurrentPage(page);
      } else {
        setError(response.error || 'Bir hata oluştu');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [apiCall, limit]);

  const loadMore = useCallback(async () => {
    if (pagination?.hasNext && !loading) {
      await loadPage(currentPage + 1, true);
    }
  }, [pagination, loading, currentPage, loadPage]);

  const refresh = useCallback(async () => {
    setCurrentPage(1);
    await loadPage(1, false);
  }, [loadPage]);

  const reset = useCallback(() => {
    setData([]);
    setError(null);
    setLoading(false);
    setPagination(null);
    setCurrentPage(1);
  }, []);

  useEffect(() => {
    loadPage(1, false);
  }, [loadPage]);

  return {
    data,
    loading,
    error,
    pagination,
    loadMore,
    refresh,
    reset,
  };
}

// Hook for async operations (POST, PUT, DELETE)
interface UseAsyncOperationState {
  loading: boolean;
  error: string | null;
  execute: <T>(operation: () => Promise<ApiResponse<T>>) => Promise<T | null>;
  reset: () => void;
}

export function useAsyncOperation(): UseAsyncOperationState {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const execute = useCallback(async <T>(
    operation: () => Promise<ApiResponse<T>>
  ): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await operation();
      
      if (response.success && response.data) {
        return response.data;
      } else {
        setError(response.error || 'İşlem başarısız oldu');
        return null;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Bilinmeyen bir hata oluştu';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  const reset = useCallback(() => {
    setError(null);
    setLoading(false);
  }, []);

  return {
    loading,
    error,
    execute,
    reset,
  };
}
