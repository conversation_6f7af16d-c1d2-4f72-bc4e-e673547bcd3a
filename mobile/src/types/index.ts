// Product Types
export interface Product {
  _id: string;
  name: string;
  description: string;
  price: number;
  discountPrice?: number;
  images?: {
    image?: {
      asset?: {
        _id: string;
        url: string;
      };
    };
    alt?: string;
  }[];
  category?: {
    _id: string;
    name: string;
    slug: string;
  };
  vendor?: {
    _id: string;
    name: string;
  };
  stock: number;
  isActive: boolean;
  isFeatured?: boolean;
  tags?: string[];
  specifications?: any;
  _createdAt: string;
  _updatedAt?: string;
}

// User Types
export interface User {
  _id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'user' | 'seller' | 'admin';
  createdAt: string;
}

// Category Types
export interface Category {
  _id: string;
  name: string;
  description?: string;
  image?: string;
  isActive: boolean;
}

// Auction Types
export interface Auction {
  _id: string;
  title: string;
  description: string;
  product: Product;
  startingPrice: number;
  currentPrice: number;
  startDate: string;
  endDate: string;
  isActive: boolean;
  bids: Bid[];
  winner?: User;
}

export interface Bid {
  _id: string;
  user: User;
  amount: number;
  timestamp: string;
}

// Giveaway Types
export interface Giveaway {
  _id: string;
  title: string;
  description: string;
  product: Product;
  startDate: string;
  endDate: string;
  isActive: boolean;
  participants: User[];
  winner?: User;
  maxParticipants?: number;
}

// Cart Types
export interface CartItem {
  product: Product;
  quantity: number;
}

export interface Cart {
  items: CartItem[];
  total: number;
}

// Order Types
export interface Order {
  _id: string;
  user: User;
  items: CartItem[];
  total: number;
  status: 'pending' | 'confirmed' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: Address;
  paymentMethod: string;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

// Navigation Types
export type RootStackParamList = {
  Main: undefined;
  ProductDetail: { productId: string };
  AuctionDetail: { auctionId: string };
  GiveawayDetail: { giveawayId: string };
  Cart: undefined;
  Checkout: undefined;
  Profile: undefined;
  Login: undefined;
  Register: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Auctions: undefined;
  Giveaways: undefined;
  Profile: undefined;
};

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  statusCode?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
