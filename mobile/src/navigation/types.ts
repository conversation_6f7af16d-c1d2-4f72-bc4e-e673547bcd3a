import { StackNavigationProp } from '@react-navigation/stack';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';
import { CompositeNavigationProp } from '@react-navigation/native';

// Auth Stack
export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
};

export type AuthStackNavigationProp = StackNavigationProp<AuthStackParamList>;

// Main Tab Navigator
export type MainTabParamList = {
  Home: undefined;
  Categories: undefined;
  Search: undefined;
  Profile: undefined;
};

export type MainTabNavigationProp = BottomTabNavigationProp<MainTabParamList>;

// Main Stack Navigator
export type MainStackParamList = {
  MainTabs: undefined;
  ProductDetail: { productId: string };
  AuctionDetail: { auctionId: string };
  GiveawayDetail: { giveawayId: string };
  Cart: undefined;
  Checkout: undefined;
};

export type MainStackNavigationProp = StackNavigationProp<MainStackParamList>;

// Root Navigator
export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
};

export type RootStackNavigationProp = StackNavigationProp<RootStackParamList>;

// Combined navigation prop for screens that need access to multiple navigators
export type CombinedNavigationProp = CompositeNavigationProp<
  MainTabNavigationProp,
  CompositeNavigationProp<MainStackNavigationProp, RootStackNavigationProp>
>;
