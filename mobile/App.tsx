import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Platform, SafeAreaView } from 'react-native';
import { AppNavigator } from './src/navigation/AppNavigator';
import { AuthProvider } from './src/contexts/AuthContext';

// Platform-specific Clerk imports
let ClerkProvider: any;
if (Platform.OS === 'web') {
  ClerkProvider = require('@clerk/clerk-react').ClerkProvider;
} else {
  ClerkProvider = require('@clerk/clerk-expo').ClerkProvider;
}

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  );
}

export default function App() {
  // Web'de Clerk için ek props gerekebilir
  const clerkProps = Platform.OS === 'web'
    ? {
        publishableKey: publishableKey,
        // Web'de frontendApi gerekebilir
        frontendApi: publishableKey.replace('pk_test_', '').replace('pk_live_', '').split('.')[0] + '.clerk.accounts.dev'
      }
    : { publishableKey: publishableKey };

  return (
    <ClerkProvider {...clerkProps}>
      <AuthProvider>
        <SafeAreaView style={{ flex: 1 }}>
          <AppNavigator />
          <StatusBar style="auto" />
        </SafeAreaView>
      </AuthProvider>
    </ClerkProvider>
  );
}
