import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { View, Platform, SafeAreaView } from 'react-native';
import { AppNavigator } from './src/navigation/AppNavigator';
import { AuthProvider } from './src/contexts/AuthContext';

// Platform-specific Clerk imports
let ClerkProvider: any;
if (Platform.OS === 'web') {
  ClerkProvider = require('@clerk/clerk-react').ClerkProvider;
} else {
  ClerkProvider = require('@clerk/clerk-expo').ClerkProvider;
}

const publishableKey = process.env.EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY!;

if (!publishableKey) {
  throw new Error(
    'Missing Publishable Key. Please set EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY in your .env'
  );
}

export default function App() {
  // Platform-specific Clerk props
  const clerkProps = { publishableKey: publishableKey };

  return (
    <ClerkProvider {...clerkProps}>
      <AuthProvider>
        <SafeAreaView style={{ flex: 1 }}>
          <AppNavigator />
          <StatusBar style="auto" />
          {/* Web'de Clerk CAPTCHA için gerekli element */}
          {Platform.OS === 'web' && (
            <View
              // @ts-ignore - Web-specific prop
              id="clerk-captcha"
              style={{ display: 'none', position: 'absolute' }}
            />
          )}
        </SafeAreaView>
      </AuthProvider>
    </ClerkProvider>
  );
}
