import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProductFilters } from '@/types';

interface SearchStore {
  searchQuery: string;
  filters: ProductFilters;
  recentSearches: string[];
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<ProductFilters>) => void;
  clearFilters: () => void;
  addRecentSearch: (query: string) => void;
  clearRecentSearches: () => void;
}

const defaultFilters: ProductFilters = {
  category: 'All',
  brand: 'All',
  sortBy: 'newest',
};

export const useSearchStore = create<SearchStore>()(
  persist(
    (set, get) => ({
      searchQuery: '',
      filters: defaultFilters,
      recentSearches: [],

      setSearchQuery: (query) => {
        set({ searchQuery: query });
        if (query.trim() && !get().recentSearches.includes(query.trim())) {
          get().addRecentSearch(query.trim());
        }
      },

      setFilters: (newFilters) => {
        set((state) => ({
          filters: { ...state.filters, ...newFilters }
        }));
      },

      clearFilters: () => {
        set({ filters: defaultFilters });
      },

      addRecentSearch: (query) => {
        set((state) => ({
          recentSearches: [
            query,
            ...state.recentSearches.filter(q => q !== query)
          ].slice(0, 5) // Keep only last 5 searches
        }));
      },

      clearRecentSearches: () => {
        set({ recentSearches: [] });
      },
    }),
    {
      name: 'search-storage',
    }
  )
);
