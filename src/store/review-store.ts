import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { Review, ReviewStats } from '@/types';

interface ReviewStore {
  reviews: Record<string, Review[]>; // productId -> reviews
  reviewStats: Record<string, ReviewStats>; // productId -> stats
  userReviews: Record<string, Review[]>; // userId -> reviews
  
  // Actions
  addReview: (review: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateReview: (reviewId: string, updates: Partial<Review>) => void;
  deleteReview: (reviewId: string, productId: string) => void;
  getProductReviews: (productId: string) => Review[];
  getProductStats: (productId: string) => ReviewStats;
  getUserReviews: (userId: string) => Review[];
  markHelpful: (reviewId: string, productId: string) => void;
}

// Mock review data
const mockReviews: Review[] = [
  {
    id: '1',
    productId: '1',
    userId: 'user1',
    userName: '<PERSON>',
    userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face',
    rating: 5,
    title: 'Excellent product!',
    comment: 'Amazing quality and fast shipping. Exactly as described and works perfectly. Highly recommended!',
    verified: true,
    helpful: 12,
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: '2',
    productId: '1',
    userId: 'user2',
    userName: 'Sarah M.',
    userAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=40&h=40&fit=crop&crop=face',
    rating: 4,
    title: 'Good quality',
    comment: 'Good quality product, though delivery took a bit longer than expected. Overall satisfied with the purchase.',
    verified: true,
    helpful: 8,
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: '3',
    productId: '1',
    userId: 'user3',
    userName: 'Mike R.',
    userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',
    rating: 5,
    title: 'Outstanding value',
    comment: 'Outstanding value for money. The build quality is impressive and it works exactly as advertised.',
    verified: true,
    helpful: 15,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: '4',
    productId: '2',
    userId: 'user4',
    userName: 'Emily K.',
    userAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=40&h=40&fit=crop&crop=face',
    rating: 4,
    title: 'Great fitness tracker',
    comment: 'Love the fitness tracking features. Battery life is excellent and the display is clear.',
    verified: true,
    helpful: 6,
    createdAt: new Date('2024-01-12'),
    updatedAt: new Date('2024-01-12')
  }
];

const calculateStats = (reviews: Review[]): ReviewStats => {
  if (reviews.length === 0) {
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
    };
  }

  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  const averageRating = totalRating / reviews.length;
  
  const ratingDistribution = reviews.reduce((dist, review) => {
    dist[review.rating as keyof typeof dist]++;
    return dist;
  }, { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 });

  return {
    averageRating: Math.round(averageRating * 10) / 10,
    totalReviews: reviews.length,
    ratingDistribution
  };
};

export const useReviewStore = create<ReviewStore>()(
  persist(
    (set, get) => {
      // Initialize with mock data
      const initialReviews: Record<string, Review[]> = {};
      const initialStats: Record<string, ReviewStats> = {};
      const initialUserReviews: Record<string, Review[]> = {};

      mockReviews.forEach(review => {
        if (!initialReviews[review.productId]) {
          initialReviews[review.productId] = [];
        }
        initialReviews[review.productId].push(review);

        if (!initialUserReviews[review.userId]) {
          initialUserReviews[review.userId] = [];
        }
        initialUserReviews[review.userId].push(review);
      });

      Object.keys(initialReviews).forEach(productId => {
        initialStats[productId] = calculateStats(initialReviews[productId]);
      });

      return {
        reviews: initialReviews,
        reviewStats: initialStats,
        userReviews: initialUserReviews,

        addReview: (reviewData) => {
          const newReview: Review = {
            ...reviewData,
            id: Date.now().toString(),
            createdAt: new Date(),
            updatedAt: new Date(),
            helpful: 0
          };

          set((state) => {
            const productReviews = state.reviews[reviewData.productId] || [];
            const updatedReviews = [...productReviews, newReview];
            
            const userReviews = state.userReviews[reviewData.userId] || [];
            const updatedUserReviews = [...userReviews, newReview];

            return {
              reviews: {
                ...state.reviews,
                [reviewData.productId]: updatedReviews
              },
              reviewStats: {
                ...state.reviewStats,
                [reviewData.productId]: calculateStats(updatedReviews)
              },
              userReviews: {
                ...state.userReviews,
                [reviewData.userId]: updatedUserReviews
              }
            };
          });
        },

        updateReview: (reviewId, updates) => {
          set((state) => {
            const newReviews = { ...state.reviews };
            const newUserReviews = { ...state.userReviews };
            let productId = '';

            // Find and update the review
            Object.keys(newReviews).forEach(pid => {
              const reviewIndex = newReviews[pid].findIndex(r => r.id === reviewId);
              if (reviewIndex !== -1) {
                productId = pid;
                newReviews[pid][reviewIndex] = {
                  ...newReviews[pid][reviewIndex],
                  ...updates,
                  updatedAt: new Date()
                };

                // Update user reviews too
                const userId = newReviews[pid][reviewIndex].userId;
                const userReviewIndex = newUserReviews[userId]?.findIndex(r => r.id === reviewId);
                if (userReviewIndex !== -1) {
                  newUserReviews[userId][userReviewIndex] = newReviews[pid][reviewIndex];
                }
              }
            });

            return {
              reviews: newReviews,
              reviewStats: productId ? {
                ...state.reviewStats,
                [productId]: calculateStats(newReviews[productId])
              } : state.reviewStats,
              userReviews: newUserReviews
            };
          });
        },

        deleteReview: (reviewId, productId) => {
          set((state) => {
            const productReviews = state.reviews[productId] || [];
            const reviewToDelete = productReviews.find(r => r.id === reviewId);
            
            if (!reviewToDelete) return state;

            const updatedReviews = productReviews.filter(r => r.id !== reviewId);
            const userReviews = state.userReviews[reviewToDelete.userId] || [];
            const updatedUserReviews = userReviews.filter(r => r.id !== reviewId);

            return {
              reviews: {
                ...state.reviews,
                [productId]: updatedReviews
              },
              reviewStats: {
                ...state.reviewStats,
                [productId]: calculateStats(updatedReviews)
              },
              userReviews: {
                ...state.userReviews,
                [reviewToDelete.userId]: updatedUserReviews
              }
            };
          });
        },

        getProductReviews: (productId) => {
          return get().reviews[productId] || [];
        },

        getProductStats: (productId) => {
          return get().reviewStats[productId] || {
            averageRating: 0,
            totalReviews: 0,
            ratingDistribution: { 5: 0, 4: 0, 3: 0, 2: 0, 1: 0 }
          };
        },

        getUserReviews: (userId) => {
          return get().userReviews[userId] || [];
        },

        markHelpful: (reviewId, productId) => {
          set((state) => {
            const newReviews = { ...state.reviews };
            const productReviews = newReviews[productId] || [];
            const reviewIndex = productReviews.findIndex(r => r.id === reviewId);
            
            if (reviewIndex !== -1) {
              newReviews[productId][reviewIndex].helpful += 1;
            }

            return {
              ...state,
              reviews: newReviews
            };
          });
        }
      };
    },
    {
      name: 'review-storage',
    }
  )
);
