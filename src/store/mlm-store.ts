import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// MLM User Interface
export interface MLMUser {
  id: string;
  clerkId: string;
  name: string;
  email: string;
  imageUrl?: string;
  phone?: string;
  walletBalance: number;
  mlmLevel: 'bronze' | 'silver' | 'gold' | 'diamond' | 'platinum' | 'sapphire';
  sponsor?: MLMUser;
  sponsorCode: string;
  leftChild?: MLMUser;
  rightChild?: MLMUser;
  totalCommissionEarned: number;
  monthlyCommissionEarned: number;
  totalSales: number;
  totalTeamSales: number;
  directReferrals: number;
  totalTeamMembers: number;
  membershipStartDate: Date;
  lastLevelUpgrade?: Date;
  isActive: boolean;
  nextPaymentDate?: Date;
  giftVoucherBalance: number;
}

// MLM Bonus Interface
export interface MLMBonus {
  id: string;
  recipient: MLMUser;
  bonusType: string;
  amount: number;
  paymentType: 'cash' | 'gift_voucher' | 'product_credit' | 'points';
  status: 'pending' | 'approved' | 'paid' | 'cancelled' | 'rejected';
  criteria?: {
    requiredReferrals?: number;
    requiredSalesVolume?: number;
    requiredTeamVolume?: number;
    requiredLevel?: string;
    timeFrame?: number;
  };
  achievedCriteria?: {
    actualReferrals?: number;
    actualSalesVolume?: number;
    actualTeamVolume?: number;
    currentLevel?: string;
    achievedDate?: Date;
  };
  relatedSource?: {
    sourceType: string;
    sourceId: string;
    sourceUser?: MLMUser;
  };
  calculationDetails?: {
    baseAmount?: number;
    percentage?: number;
    multiplier?: number;
    formula?: string;
  };
  expiryDate?: Date;
  createdAt: Date;
  paidAt?: Date;
  description?: string;
  notes?: string;
  transactionId?: string;
}

// MLM Commission Interface
export interface MLMCommission {
  id: string;
  recipient: MLMUser;
  source: MLMUser;
  commissionType: string;
  amount: number;
  percentage?: number;
  level?: number;
  baseAmount?: number;
  status: 'pending' | 'approved' | 'paid' | 'cancelled' | 'rejected';
  relatedOrder?: string;
  relatedSale?: string;
  calculationDetails?: {
    formula?: string;
    variables?: {
      salesVolume?: number;
      teamVolume?: number;
      levelMultiplier?: number;
    };
  };
  paymentMethod: 'wallet' | 'gift_voucher' | 'bank_transfer' | 'product_credit';
  paidAt?: Date;
  expiryDate?: Date;
  createdAt: Date;
  description?: string;
  notes?: string;
  transactionId?: string;
}

// MLM Store Interface
interface MLMStore {
  // State
  currentUser: MLMUser | null;
  users: MLMUser[];
  bonuses: MLMBonus[];
  commissions: MLMCommission[];
  
  // User Actions
  setCurrentUser: (user: MLMUser) => void;
  updateUser: (userId: string, updates: Partial<MLMUser>) => void;
  addUser: (user: Omit<MLMUser, 'id'>) => string;
  getUserById: (userId: string) => MLMUser | undefined;
  getUserByClerkId: (clerkId: string) => MLMUser | undefined;
  getUserBySponsorCode: (sponsorCode: string) => MLMUser | undefined;
  
  // Referral Actions
  addReferral: (sponsorId: string, newUserId: string, position?: 'left' | 'right') => boolean;
  getDirectReferrals: (userId: string) => MLMUser[];
  getTeamMembers: (userId: string) => MLMUser[];
  calculateTeamStats: (userId: string) => {
    totalMembers: number;
    totalSales: number;
    leftTeamSize: number;
    rightTeamSize: number;
    leftTeamSales: number;
    rightTeamSales: number;
  };
  
  // Bonus Actions
  addBonus: (bonus: Omit<MLMBonus, 'id' | 'createdAt'>) => string;
  updateBonus: (bonusId: string, updates: Partial<MLMBonus>) => void;
  getUserBonuses: (userId: string) => MLMBonus[];
  getPendingBonuses: () => MLMBonus[];
  
  // Commission Actions
  addCommission: (commission: Omit<MLMCommission, 'id' | 'createdAt'>) => string;
  updateCommission: (commissionId: string, updates: Partial<MLMCommission>) => void;
  getUserCommissions: (userId: string) => MLMCommission[];
  getPendingCommissions: () => MLMCommission[];
  
  // Wallet Actions
  updateWalletBalance: (userId: string, amount: number, type: 'add' | 'subtract') => boolean;
  transferFunds: (fromUserId: string, toUserId: string, amount: number) => boolean;
  
  // Level Actions
  checkLevelUpgrade: (userId: string) => string | null;
  upgradeMemberLevel: (userId: string, newLevel: MLMUser['mlmLevel']) => boolean;
  
  // Statistics
  getMLMStats: () => {
    totalUsers: number;
    activeUsers: number;
    totalCommissions: number;
    totalBonuses: number;
    monthlyCommissions: number;
    levelDistribution: Record<string, number>;
  };
}

// Mock data for development
const createMockMLMData = () => {
  const baseDate = new Date('2024-06-20T12:00:00Z');
  
  const mockUsers: MLMUser[] = [
    {
      id: '1',
      clerkId: 'user_1',
      name: 'John Doe',
      email: '<EMAIL>',
      imageUrl: '/placeholder-avatar.jpg',
      phone: '+90 ************',
      walletBalance: 1500.00,
      mlmLevel: 'gold',
      sponsorCode: 'JOHN2024',
      totalCommissionEarned: 5000.00,
      monthlyCommissionEarned: 800.00,
      totalSales: 12000.00,
      totalTeamSales: 45000.00,
      directReferrals: 8,
      totalTeamMembers: 25,
      membershipStartDate: new Date(baseDate.getTime() - 180 * 24 * 60 * 60 * 1000),
      lastLevelUpgrade: new Date(baseDate.getTime() - 30 * 24 * 60 * 60 * 1000),
      isActive: true,
      nextPaymentDate: new Date(baseDate.getTime() + 30 * 24 * 60 * 60 * 1000),
      giftVoucherBalance: 200.00,
    },
    {
      id: '2',
      clerkId: 'user_2',
      name: 'Jane Smith',
      email: '<EMAIL>',
      imageUrl: '/placeholder-avatar.jpg',
      phone: '+90 ************',
      walletBalance: 750.00,
      mlmLevel: 'silver',
      sponsorCode: 'JANE2024',
      totalCommissionEarned: 2500.00,
      monthlyCommissionEarned: 400.00,
      totalSales: 6000.00,
      totalTeamSales: 18000.00,
      directReferrals: 5,
      totalTeamMembers: 12,
      membershipStartDate: new Date(baseDate.getTime() - 120 * 24 * 60 * 60 * 1000),
      lastLevelUpgrade: new Date(baseDate.getTime() - 60 * 24 * 60 * 60 * 1000),
      isActive: true,
      nextPaymentDate: new Date(baseDate.getTime() + 15 * 24 * 60 * 60 * 1000),
      giftVoucherBalance: 100.00,
    }
  ];

  // Set up referral relationships
  mockUsers[1].sponsor = mockUsers[0];
  mockUsers[0].leftChild = mockUsers[1];

  return { mockUsers };
};

export const useMLMStore = create<MLMStore>()(
  persist(
    (set, get) => {
      const { mockUsers } = createMockMLMData();
      
      return {
        // Initial State
        currentUser: null,
        users: mockUsers,
        bonuses: [],
        commissions: [],

        // User Actions
        setCurrentUser: (user) => {
          set({ currentUser: user });
        },

        updateUser: (userId, updates) => {
          set((state) => ({
            users: state.users.map(user => 
              user.id === userId ? { ...user, ...updates } : user
            ),
            currentUser: state.currentUser?.id === userId 
              ? { ...state.currentUser, ...updates }
              : state.currentUser
          }));
        },

        addUser: (userData) => {
          const newId = `user_${Date.now()}`;
          const newUser: MLMUser = {
            ...userData,
            id: newId,
          };
          
          set((state) => ({
            users: [...state.users, newUser]
          }));
          
          return newId;
        },

        getUserById: (userId) => {
          return get().users.find(user => user.id === userId);
        },

        getUserByClerkId: (clerkId) => {
          return get().users.find(user => user.clerkId === clerkId);
        },

        getUserBySponsorCode: (sponsorCode) => {
          return get().users.find(user => user.sponsorCode === sponsorCode);
        },

        // Referral Actions
        addReferral: (sponsorId, newUserId, position = 'left') => {
          const sponsor = get().getUserById(sponsorId);
          const newUser = get().getUserById(newUserId);
          
          if (!sponsor || !newUser) return false;
          
          // Check if position is available
          if (position === 'left' && sponsor.leftChild) return false;
          if (position === 'right' && sponsor.rightChild) return false;
          
          // Update relationships
          get().updateUser(sponsorId, {
            [position === 'left' ? 'leftChild' : 'rightChild']: newUser,
            directReferrals: sponsor.directReferrals + 1
          });
          
          get().updateUser(newUserId, {
            sponsor: sponsor
          });
          
          return true;
        },

        getDirectReferrals: (userId) => {
          const user = get().getUserById(userId);
          if (!user) return [];
          
          const referrals: MLMUser[] = [];
          if (user.leftChild) referrals.push(user.leftChild);
          if (user.rightChild) referrals.push(user.rightChild);
          
          return referrals;
        },

        getTeamMembers: (userId) => {
          const user = get().getUserById(userId);
          if (!user) return [];
          
          const teamMembers: MLMUser[] = [];
          const visited = new Set<string>();
          
          const traverse = (currentUser: MLMUser) => {
            if (visited.has(currentUser.id)) return;
            visited.add(currentUser.id);
            
            if (currentUser.id !== userId) {
              teamMembers.push(currentUser);
            }
            
            if (currentUser.leftChild) traverse(currentUser.leftChild);
            if (currentUser.rightChild) traverse(currentUser.rightChild);
          };
          
          traverse(user);
          return teamMembers;
        },

        calculateTeamStats: (userId) => {
          const teamMembers = get().getTeamMembers(userId);
          const user = get().getUserById(userId);
          
          if (!user) {
            return {
              totalMembers: 0,
              totalSales: 0,
              leftTeamSize: 0,
              rightTeamSize: 0,
              leftTeamSales: 0,
              rightTeamSales: 0,
            };
          }
          
          const leftTeamMembers = user.leftChild ? get().getTeamMembers(user.leftChild.id) : [];
          const rightTeamMembers = user.rightChild ? get().getTeamMembers(user.rightChild.id) : [];
          
          const leftTeamSales = leftTeamMembers.reduce((sum, member) => sum + member.totalSales, 0);
          const rightTeamSales = rightTeamMembers.reduce((sum, member) => sum + member.totalSales, 0);
          const totalSales = teamMembers.reduce((sum, member) => sum + member.totalSales, 0);
          
          return {
            totalMembers: teamMembers.length,
            totalSales,
            leftTeamSize: leftTeamMembers.length,
            rightTeamSize: rightTeamMembers.length,
            leftTeamSales,
            rightTeamSales,
          };
        },

        // Bonus Actions
        addBonus: (bonusData) => {
          const newId = `bonus_${Date.now()}`;
          const newBonus: MLMBonus = {
            ...bonusData,
            id: newId,
            createdAt: new Date(),
          };
          
          set((state) => ({
            bonuses: [...state.bonuses, newBonus]
          }));
          
          return newId;
        },

        updateBonus: (bonusId, updates) => {
          set((state) => ({
            bonuses: state.bonuses.map(bonus => 
              bonus.id === bonusId ? { ...bonus, ...updates } : bonus
            )
          }));
        },

        getUserBonuses: (userId) => {
          return get().bonuses.filter(bonus => bonus.recipient.id === userId);
        },

        getPendingBonuses: () => {
          return get().bonuses.filter(bonus => bonus.status === 'pending');
        },

        // Commission Actions
        addCommission: (commissionData) => {
          const newId = `commission_${Date.now()}`;
          const newCommission: MLMCommission = {
            ...commissionData,
            id: newId,
            createdAt: new Date(),
          };
          
          set((state) => ({
            commissions: [...state.commissions, newCommission]
          }));
          
          return newId;
        },

        updateCommission: (commissionId, updates) => {
          set((state) => ({
            commissions: state.commissions.map(commission => 
              commission.id === commissionId ? { ...commission, ...updates } : commission
            )
          }));
        },

        getUserCommissions: (userId) => {
          return get().commissions.filter(commission => commission.recipient.id === userId);
        },

        getPendingCommissions: () => {
          return get().commissions.filter(commission => commission.status === 'pending');
        },

        // Wallet Actions
        updateWalletBalance: (userId, amount, type) => {
          const user = get().getUserById(userId);
          if (!user) return false;
          
          const newBalance = type === 'add' 
            ? user.walletBalance + amount 
            : user.walletBalance - amount;
          
          if (newBalance < 0) return false;
          
          get().updateUser(userId, { walletBalance: newBalance });
          return true;
        },

        transferFunds: (fromUserId, toUserId, amount) => {
          const fromUser = get().getUserById(fromUserId);
          const toUser = get().getUserById(toUserId);
          
          if (!fromUser || !toUser || fromUser.walletBalance < amount) {
            return false;
          }
          
          get().updateUser(fromUserId, { 
            walletBalance: fromUser.walletBalance - amount 
          });
          get().updateUser(toUserId, { 
            walletBalance: toUser.walletBalance + amount 
          });
          
          return true;
        },

        // Level Actions
        checkLevelUpgrade: (userId) => {
          const user = get().getUserById(userId);
          if (!user) return null;
          
          const stats = get().calculateTeamStats(userId);
          
          // Level upgrade criteria (simplified)
          if (user.mlmLevel === 'bronze' && user.totalSales >= 5000 && user.directReferrals >= 3) {
            return 'silver';
          }
          if (user.mlmLevel === 'silver' && user.totalSales >= 15000 && user.directReferrals >= 5) {
            return 'gold';
          }
          if (user.mlmLevel === 'gold' && user.totalSales >= 30000 && user.directReferrals >= 8) {
            return 'diamond';
          }
          if (user.mlmLevel === 'diamond' && user.totalSales >= 50000 && user.directReferrals >= 12) {
            return 'platinum';
          }
          if (user.mlmLevel === 'platinum' && user.totalSales >= 100000 && user.directReferrals >= 20) {
            return 'sapphire';
          }
          
          return null;
        },

        upgradeMemberLevel: (userId, newLevel) => {
          const user = get().getUserById(userId);
          if (!user) return false;
          
          get().updateUser(userId, { 
            mlmLevel: newLevel,
            lastLevelUpgrade: new Date()
          });
          
          return true;
        },

        // Statistics
        getMLMStats: () => {
          const users = get().users;
          const commissions = get().commissions;
          const bonuses = get().bonuses;
          
          const totalUsers = users.length;
          const activeUsers = users.filter(user => user.isActive).length;
          const totalCommissions = commissions.reduce((sum, c) => sum + c.amount, 0);
          const totalBonuses = bonuses.reduce((sum, b) => sum + b.amount, 0);
          
          const currentMonth = new Date().getMonth();
          const monthlyCommissions = commissions
            .filter(c => new Date(c.createdAt).getMonth() === currentMonth)
            .reduce((sum, c) => sum + c.amount, 0);
          
          const levelDistribution = users.reduce((acc, user) => {
            acc[user.mlmLevel] = (acc[user.mlmLevel] || 0) + 1;
            return acc;
          }, {} as Record<string, number>);
          
          return {
            totalUsers,
            activeUsers,
            totalCommissions,
            totalBonuses,
            monthlyCommissions,
            levelDistribution,
          };
        },
      };
    },
    {
      name: 'mlm-storage',
      skipHydration: true,
    }
  )
);
