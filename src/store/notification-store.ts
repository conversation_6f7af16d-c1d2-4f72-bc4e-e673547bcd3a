import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  read: boolean;
  createdAt: Date;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
}

export enum NotificationType {
  ORDER_CONFIRMED = 'order_confirmed',
  ORDER_SHIPPED = 'order_shipped',
  ORDER_DELIVERED = 'order_delivered',
  ORDER_CANCELLED = 'order_cancelled',
  PAYMENT_SUCCESS = 'payment_success',
  PAYMENT_FAILED = 'payment_failed',
  REVIEW_REQUEST = 'review_request',
  PRICE_DROP = 'price_drop',
  BACK_IN_STOCK = 'back_in_stock',
  PROMOTION = 'promotion',
  SYSTEM = 'system'
}

export interface NotificationPreferences {
  email: {
    orderUpdates: boolean;
    promotions: boolean;
    priceDrops: boolean;
    backInStock: boolean;
    reviews: boolean;
    newsletter: boolean;
  };
  push: {
    orderUpdates: boolean;
    promotions: boolean;
    priceDrops: boolean;
    backInStock: boolean;
  };
  sms: {
    orderUpdates: boolean;
    deliveryUpdates: boolean;
  };
}

interface NotificationStore {
  notifications: Notification[];
  preferences: NotificationPreferences;
  unreadCount: number;
  
  // Actions
  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (notificationId: string) => void;
  clearAllNotifications: () => void;
  updatePreferences: (preferences: Partial<NotificationPreferences>) => void;
  getNotificationsByType: (type: NotificationType) => Notification[];
  getUnreadNotifications: () => Notification[];
}

const defaultPreferences: NotificationPreferences = {
  email: {
    orderUpdates: true,
    promotions: true,
    priceDrops: true,
    backInStock: true,
    reviews: true,
    newsletter: false
  },
  push: {
    orderUpdates: true,
    promotions: false,
    priceDrops: true,
    backInStock: true
  },
  sms: {
    orderUpdates: false,
    deliveryUpdates: false
  }
};

// Mock notifications with fixed dates to avoid hydration mismatch
const createMockNotifications = (): Notification[] => {
  // Use fixed dates instead of Date.now() to avoid hydration mismatch
  const baseDate = new Date('2024-06-20T12:00:00Z');

  return [
    {
      id: '1',
      type: NotificationType.ORDER_DELIVERED,
      title: 'Order Delivered',
      message: 'Your order #ORD-2024-001 has been delivered successfully!',
      read: false,
      createdAt: new Date(baseDate.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
      actionUrl: '/orders/1',
      actionLabel: 'View Order'
    },
    {
      id: '2',
      type: NotificationType.REVIEW_REQUEST,
      title: 'Review Your Purchase',
      message: 'How was your experience with iPhone 15 Pro Max? Leave a review!',
      read: false,
      createdAt: new Date(baseDate.getTime() - 6 * 60 * 60 * 1000), // 6 hours ago
      actionUrl: '/products/1/review',
      actionLabel: 'Write Review'
    },
    {
      id: '3',
      type: NotificationType.PRICE_DROP,
      title: 'Price Drop Alert',
      message: 'Samsung Galaxy Watch 6 is now 20% off! Limited time offer.',
      read: true,
      createdAt: new Date(baseDate.getTime() - 24 * 60 * 60 * 1000), // 1 day ago
      actionUrl: '/products/2',
      actionLabel: 'View Product'
    },
    {
      id: '4',
      type: NotificationType.PROMOTION,
      title: 'Special Offer',
      message: 'Get free shipping on orders over $50. Use code FREESHIP50',
      read: true,
      createdAt: new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      actionUrl: '/products',
      actionLabel: 'Shop Now'
    }
  ];
};

export const useNotificationStore = create<NotificationStore>()(
  persist(
    (set, get) => {
      // Initialize with mock data
      const initialNotifications = createMockNotifications();

      return {
        notifications: initialNotifications,
        preferences: defaultPreferences,
        unreadCount: initialNotifications.filter(n => !n.read).length,

        addNotification: (notificationData) => {
        const newNotification: Notification = {
          ...notificationData,
          id: `notification_${Date.now()}`,
          createdAt: new Date()
        };

        set((state) => ({
          notifications: [newNotification, ...state.notifications],
          unreadCount: state.unreadCount + 1
        }));
      },

      markAsRead: (notificationId) => {
        set((state) => {
          const notifications = state.notifications.map(notification => {
            if (notification.id === notificationId && !notification.read) {
              return { ...notification, read: true };
            }
            return notification;
          });

          const unreadCount = notifications.filter(n => !n.read).length;

          return { notifications, unreadCount };
        });
      },

      markAllAsRead: () => {
        set((state) => ({
          notifications: state.notifications.map(notification => ({
            ...notification,
            read: true
          })),
          unreadCount: 0
        }));
      },

      deleteNotification: (notificationId) => {
        set((state) => {
          const notification = state.notifications.find(n => n.id === notificationId);
          const notifications = state.notifications.filter(n => n.id !== notificationId);
          const unreadCount = notification && !notification.read 
            ? state.unreadCount - 1 
            : state.unreadCount;

          return { notifications, unreadCount };
        });
      },

      clearAllNotifications: () => {
        set({ notifications: [], unreadCount: 0 });
      },

      updatePreferences: (newPreferences) => {
        set((state) => ({
          preferences: {
            ...state.preferences,
            ...newPreferences,
            email: { ...state.preferences.email, ...newPreferences.email },
            push: { ...state.preferences.push, ...newPreferences.push },
            sms: { ...state.preferences.sms, ...newPreferences.sms }
          }
        }));
      },

      getNotificationsByType: (type) => {
        return get().notifications.filter(notification => notification.type === type);
      },

      getUnreadNotifications: () => {
        return get().notifications.filter(notification => !notification.read);
      }
    };
  },
    {
      name: 'notification-storage',
      skipHydration: true, // Prevent hydration mismatch
    }
  )
);

// Utility functions for creating notifications
export const createOrderNotification = (
  type: NotificationType.ORDER_CONFIRMED | NotificationType.ORDER_SHIPPED | NotificationType.ORDER_DELIVERED | NotificationType.ORDER_CANCELLED,
  orderNumber: string,
  orderId: string
): Omit<Notification, 'id' | 'createdAt'> => {
  const messages = {
    [NotificationType.ORDER_CONFIRMED]: {
      title: 'Order Confirmed',
      message: `Your order ${orderNumber} has been confirmed and is being processed.`
    },
    [NotificationType.ORDER_SHIPPED]: {
      title: 'Order Shipped',
      message: `Your order ${orderNumber} has been shipped and is on its way!`
    },
    [NotificationType.ORDER_DELIVERED]: {
      title: 'Order Delivered',
      message: `Your order ${orderNumber} has been delivered successfully!`
    },
    [NotificationType.ORDER_CANCELLED]: {
      title: 'Order Cancelled',
      message: `Your order ${orderNumber} has been cancelled.`
    }
  };

  return {
    type,
    title: messages[type].title,
    message: messages[type].message,
    read: false,
    actionUrl: `/orders/${orderId}`,
    actionLabel: 'View Order',
    metadata: { orderNumber, orderId }
  };
};

export const createPriceDropNotification = (
  productName: string,
  discount: number,
  productId: string
): Omit<Notification, 'id' | 'createdAt'> => ({
  type: NotificationType.PRICE_DROP,
  title: 'Price Drop Alert',
  message: `${productName} is now ${discount}% off! Limited time offer.`,
  read: false,
  actionUrl: `/products/${productId}`,
  actionLabel: 'View Product',
  metadata: { productName, discount, productId }
});

export const createBackInStockNotification = (
  productName: string,
  productId: string
): Omit<Notification, 'id' | 'createdAt'> => ({
  type: NotificationType.BACK_IN_STOCK,
  title: 'Back in Stock',
  message: `${productName} is back in stock! Get it before it sells out again.`,
  read: false,
  actionUrl: `/products/${productId}`,
  actionLabel: 'Shop Now',
  metadata: { productName, productId }
});
