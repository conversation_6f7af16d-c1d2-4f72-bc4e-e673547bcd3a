import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface WishlistItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  brand: string;
  category: string;
  rating: number;
  inStock: boolean;
  addedAt: Date;
}

interface WishlistStore {
  items: WishlistItem[];

  // Actions
  addItem: (item: Omit<WishlistItem, 'addedAt'>) => void;
  removeItem: (productId: string) => void;
  clearWishlist: () => void;
  isInWishlist: (productId: string) => boolean;
  getItemCount: () => number;
  moveToCart?: (productId: string) => void; // Optional integration with cart
  shareWishlist: () => string;
  getWishlistByCategory: () => Record<string, WishlistItem[]>;
  sortWishlist: (sortBy: 'name' | 'price' | 'date' | 'category') => void;
}

export const useWishlistStore = create<WishlistStore>()(
  persist(
    (set, get) => ({
      items: [],

      addItem: (itemData) => {
        const existingItem = get().items.find(item => item.productId === itemData.productId);
        
        if (existingItem) {
          // Item already in wishlist, don't add again
          return;
        }

        const newItem: WishlistItem = {
          ...itemData,
          addedAt: new Date()
        };

        set((state) => ({
          items: [...state.items, newItem]
        }));
      },

      removeItem: (productId) => {
        set((state) => ({
          items: state.items.filter(item => item.productId !== productId)
        }));
      },

      clearWishlist: () => {
        set({ items: [] });
      },

      isInWishlist: (productId) => {
        return get().items.some(item => item.productId === productId);
      },

      getItemCount: () => {
        return get().items.length;
      },

      moveToCart: (productId) => {
        // This would integrate with cart store
        // For now, just remove from wishlist
        get().removeItem(productId);
      },

      shareWishlist: () => {
        const items = get().items;
        const wishlistData = {
          items: items.map(item => ({
            name: item.name,
            price: item.price,
            category: item.category
          })),
          totalItems: items.length,
          createdAt: new Date().toISOString()
        };

        // In a real app, this would generate a shareable link
        return `${window.location.origin}/wishlist/shared/${btoa(JSON.stringify(wishlistData))}`;
      },

      getWishlistByCategory: () => {
        const items = get().items;
        return items.reduce((acc, item) => {
          if (!acc[item.category]) {
            acc[item.category] = [];
          }
          acc[item.category].push(item);
          return acc;
        }, {} as Record<string, WishlistItem[]>);
      },

      sortWishlist: (sortBy) => {
        set((state) => {
          const sortedItems = [...state.items].sort((a, b) => {
            switch (sortBy) {
              case 'name':
                return a.name.localeCompare(b.name);
              case 'price':
                return a.price - b.price;
              case 'date':
                return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
              case 'category':
                return a.category.localeCompare(b.category);
              default:
                return 0;
            }
          });

          return { items: sortedItems };
        });
      }
    }),
    {
      name: 'wishlist-storage',
      skipHydration: true, // Prevent hydration mismatch
    }
  )
);
