import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variant?: {
    size?: string;
    color?: string;
  };
}

export interface ShippingAddress {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
}

export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  tax: number;
  total: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  shippingAddress: ShippingAddress;
  billingAddress?: ShippingAddress;
  paymentMethod: string;
  trackingNumber?: string;
  estimatedDelivery?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  statusHistory: OrderStatusUpdate[];
}

export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  OUT_FOR_DELIVERY = 'out_for_delivery',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  RETURNED = 'returned',
  REFUNDED = 'refunded'
}

export enum PaymentStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded'
}

export interface OrderStatusUpdate {
  status: OrderStatus;
  timestamp: Date;
  note?: string;
  location?: string;
}

interface OrderStore {
  orders: Order[];
  currentOrder: Order | null;
  
  // Actions
  createOrder: (orderData: Omit<Order, 'id' | 'orderNumber' | 'createdAt' | 'updatedAt' | 'statusHistory'>) => string;
  updateOrderStatus: (orderId: string, status: OrderStatus, note?: string, location?: string) => void;
  updatePaymentStatus: (orderId: string, status: PaymentStatus) => void;
  addTrackingNumber: (orderId: string, trackingNumber: string) => void;
  cancelOrder: (orderId: string, reason?: string) => void;
  getOrder: (orderId: string) => Order | undefined;
  getUserOrders: (userId: string) => Order[];
  getOrdersByStatus: (status: OrderStatus) => Order[];
  searchOrders: (query: string) => Order[];
}

// Mock order data with fixed dates to avoid hydration mismatch
const createMockOrders = (): Order[] => {
  // Use fixed dates instead of Date.now() to avoid hydration mismatch
  const baseDate = new Date('2024-06-20T12:00:00Z');
  const fiveDaysAgo = new Date(baseDate.getTime() - 5 * 24 * 60 * 60 * 1000);
  const fourDaysAgo = new Date(baseDate.getTime() - 4 * 24 * 60 * 60 * 1000);
  const threeDaysAgo = new Date(baseDate.getTime() - 3 * 24 * 60 * 60 * 1000);
  const twoDaysAgo = new Date(baseDate.getTime() - 2 * 24 * 60 * 60 * 1000);
  const oneDayAgo = new Date(baseDate.getTime() - 1 * 24 * 60 * 60 * 1000);

  return [
    {
      id: '1',
      orderNumber: 'ORD-2024-001',
      userId: 'user1',
      items: [
        {
          id: '1',
          productId: '1',
          name: 'iPhone 15 Pro Max',
          price: 1199,
          quantity: 1,
          image: '/placeholder-product.jpg'
        }
      ],
      subtotal: 1199,
      shipping: 0,
      tax: 119.90,
      total: 1318.90,
      status: OrderStatus.DELIVERED,
      paymentStatus: PaymentStatus.PAID,
      shippingAddress: {
        id: '1',
        name: 'John Doe',
        street: '123 Main St',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        country: 'USA',
        phone: '******-0123'
      },
      paymentMethod: 'Credit Card (**** 1234)',
      trackingNumber: 'TRK123456789',
      estimatedDelivery: threeDaysAgo,
      createdAt: fiveDaysAgo,
      updatedAt: oneDayAgo,
      statusHistory: [
        {
          status: OrderStatus.PENDING,
          timestamp: new Date(fiveDaysAgo.getTime() + 10 * 60 * 1000), // 10 minutes after creation
          note: 'Order placed'
        },
        {
          status: OrderStatus.CONFIRMED,
          timestamp: new Date(fiveDaysAgo.getTime() + 60 * 60 * 1000), // 1 hour after creation
          note: 'Payment confirmed'
        },
        {
          status: OrderStatus.PROCESSING,
          timestamp: new Date(fourDaysAgo.getTime() + 9 * 60 * 60 * 1000), // Next day morning
          note: 'Order is being prepared'
        },
        {
          status: OrderStatus.SHIPPED,
          timestamp: new Date(threeDaysAgo.getTime() + 14 * 60 * 60 * 1000), // Day after, afternoon
          note: 'Package shipped from warehouse',
          location: 'New York, NY'
        },
        {
          status: OrderStatus.DELIVERED,
          timestamp: new Date(oneDayAgo.getTime() + 16 * 60 * 60 * 1000 + 30 * 60 * 1000), // Yesterday evening
          note: 'Package delivered successfully',
          location: 'New York, NY'
        }
      ]
    }
  ];
};

export const useOrderStore = create<OrderStore>()(
  persist(
    (set, get) => ({
      orders: createMockOrders(),
      currentOrder: null,

      createOrder: (orderData) => {
        const orderId = `order_${Date.now()}`;
        const orderNumber = `ORD-${new Date().getFullYear()}-${String(get().orders.length + 1).padStart(3, '0')}`;
        
        const newOrder: Order = {
          ...orderData,
          id: orderId,
          orderNumber,
          createdAt: new Date(),
          updatedAt: new Date(),
          statusHistory: [
            {
              status: orderData.status,
              timestamp: new Date(),
              note: 'Order created'
            }
          ]
        };

        set((state) => ({
          orders: [...state.orders, newOrder],
          currentOrder: newOrder
        }));

        return orderId;
      },

      updateOrderStatus: (orderId, status, note, location) => {
        set((state) => {
          const orders = state.orders.map(order => {
            if (order.id === orderId) {
              const statusUpdate: OrderStatusUpdate = {
                status,
                timestamp: new Date(),
                note,
                location
              };

              return {
                ...order,
                status,
                updatedAt: new Date(),
                statusHistory: [...order.statusHistory, statusUpdate]
              };
            }
            return order;
          });

          return { orders };
        });
      },

      updatePaymentStatus: (orderId, status) => {
        set((state) => {
          const orders = state.orders.map(order => {
            if (order.id === orderId) {
              return {
                ...order,
                paymentStatus: status,
                updatedAt: new Date()
              };
            }
            return order;
          });

          return { orders };
        });
      },

      addTrackingNumber: (orderId, trackingNumber) => {
        set((state) => {
          const orders = state.orders.map(order => {
            if (order.id === orderId) {
              return {
                ...order,
                trackingNumber,
                updatedAt: new Date()
              };
            }
            return order;
          });

          return { orders };
        });
      },

      cancelOrder: (orderId, reason) => {
        get().updateOrderStatus(orderId, OrderStatus.CANCELLED, reason);
      },

      getOrder: (orderId) => {
        return get().orders.find(order => order.id === orderId);
      },

      getUserOrders: (userId) => {
        return get().orders.filter(order => order.userId === userId);
      },

      getOrdersByStatus: (status) => {
        return get().orders.filter(order => order.status === status);
      },

      searchOrders: (query) => {
        const lowercaseQuery = query.toLowerCase();
        return get().orders.filter(order => 
          order.orderNumber.toLowerCase().includes(lowercaseQuery) ||
          order.items.some(item => item.name.toLowerCase().includes(lowercaseQuery)) ||
          order.shippingAddress.name.toLowerCase().includes(lowercaseQuery)
        );
      }
    }),
    {
      name: 'order-storage',
      skipHydration: true, // Prevent hydration mismatch
    }
  )
);
