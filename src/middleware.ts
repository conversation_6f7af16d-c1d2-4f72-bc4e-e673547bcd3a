import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';

// Define public routes (routes that don't require authentication)
const isPublicRoute = createRouteMatcher([
  '/',
  '/products(.*)',
  '/cart',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/api/checkout',
  '/studio(.*)', // Sanity Studio sayfası
  '/auctions(.*)', // Açık artırma sayfaları
  '/giveaways(.*)', // Çekiliş sayfaları
]);

export default clerkMiddleware();

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
