export const commission = {
  name: 'commission',
  title: 'MLM Commission',
  type: 'document',
  fields: [
    {
      name: 'memberId',
      title: 'Member ID',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'member',
      title: 'Member',
      type: 'reference',
      to: [{ type: 'member' }],
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'fromMember',
      title: 'From Member',
      type: 'reference',
      to: [{ type: 'member' }],
      description: 'The member who generated this commission',
    },
    {
      name: 'order',
      title: 'Related Order',
      type: 'reference',
      to: [{ type: 'order' }],
      description: 'The order that generated this commission',
    },
    {
      name: 'amount',
      title: 'Commission Amount',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'type',
      title: 'Commission Type',
      type: 'string',
      options: {
        list: [
          { title: 'Direct Referral', value: 'direct' },
          { title: 'Binary Bonus', value: 'binary' },
          { title: 'Level Bonus', value: 'level' },
          { title: 'Rank Bonus', value: 'rank' },
          { title: 'Leadership Bonus', value: 'leadership' },
          { title: 'Special Bonus', value: 'special' },
        ],
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'level',
      title: 'Commission Level',
      type: 'number',
      description: 'The level in the MLM structure (1 = direct, 2 = second level, etc.)',
      validation: (Rule: any) => Rule.min(1),
    },
    {
      name: 'percentage',
      title: 'Commission Percentage',
      type: 'number',
      description: 'The percentage rate used to calculate this commission',
      validation: (Rule: any) => Rule.min(0).max(100),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Approved', value: 'approved' },
          { title: 'Paid', value: 'paid' },
          { title: 'Cancelled', value: 'cancelled' },
          { title: 'On Hold', value: 'on_hold' },
        ],
      },
      initialValue: 'pending',
    },
    {
      name: 'paidDate',
      title: 'Paid Date',
      type: 'datetime',
      description: 'When the commission was paid out',
    },
    {
      name: 'paymentMethod',
      title: 'Payment Method',
      type: 'string',
      options: {
        list: [
          { title: 'Wallet Credit', value: 'wallet' },
          { title: 'Bank Transfer', value: 'bank_transfer' },
          { title: 'PayPal', value: 'paypal' },
          { title: 'Check', value: 'check' },
        ],
      },
    },
    {
      name: 'paymentReference',
      title: 'Payment Reference',
      type: 'string',
      description: 'Transaction ID or reference number for the payment',
    },
    {
      name: 'notes',
      title: 'Notes',
      type: 'text',
      rows: 3,
    },
    {
      name: 'calculatedAt',
      title: 'Calculated At',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
    {
      name: 'period',
      title: 'Commission Period',
      type: 'string',
      description: 'The period this commission belongs to (e.g., 2023-12)',
    },
  ],
  preview: {
    select: {
      title: 'amount',
      subtitle: 'type',
      description: 'status',
    },
    prepare(selection: any) {
      const { title, subtitle, description } = selection;
      return {
        title: `$${title}`,
        subtitle: subtitle?.toUpperCase(),
        description: description?.toUpperCase(),
      };
    },
  },
};
