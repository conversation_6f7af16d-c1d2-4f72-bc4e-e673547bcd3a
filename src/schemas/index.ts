import { product } from './product';
import { auction } from './auction';
import { member } from './member';
import { vendor } from './vendor';
import { order } from './order';
import { bid } from './bid';
import { commission } from './commission';
import walletTransaction from './walletTransaction';
import walletTopUpRequest from './walletTopUpRequest';

export const schemaTypes = [
  product,
  auction,
  member,
  vendor,
  order,
  bid,
  commission,
  walletTransaction,
  walletTopUpRequest,
];

export {
  product,
  auction,
  member,
  vendor,
  order,
  bid,
  commission,
};
