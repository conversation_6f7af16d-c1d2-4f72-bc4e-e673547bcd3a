export const bid = {
  name: 'bid',
  title: 'Auction Bid',
  type: 'document',
  fields: [
    {
      name: 'auction',
      title: 'Auction',
      type: 'reference',
      to: [{ type: 'auction' }],
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'userId',
      title: 'Bidder User ID',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'userEmail',
      title: 'Bidder Email',
      type: 'string',
      validation: (Rule: any) => Rule.required().email(),
    },
    {
      name: 'amount',
      title: 'Bid Amount',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'status',
      title: 'Bid Status',
      type: 'string',
      options: {
        list: [
          { title: 'Active', value: 'active' },
          { title: 'Outbid', value: 'outbid' },
          { title: 'Winning', value: 'winning' },
          { title: 'Won', value: 'won' },
          { title: 'Lost', value: 'lost' },
        ],
      },
      initialValue: 'active',
    },
    {
      name: 'isAutoBid',
      title: 'Is Auto Bid',
      type: 'boolean',
      initialValue: false,
    },
    {
      name: 'maxAutoBid',
      title: 'Maximum Auto Bid Amount',
      type: 'number',
      description: 'For automatic bidding feature',
    },
    {
      name: 'bidTime',
      title: 'Bid Time',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
    {
      name: 'ipAddress',
      title: 'IP Address',
      type: 'string',
    },
    {
      name: 'userAgent',
      title: 'User Agent',
      type: 'string',
    },
  ],
  preview: {
    select: {
      title: 'amount',
      subtitle: 'userEmail',
      description: 'status',
    },
    prepare(selection: any) {
      const { title, subtitle, description } = selection;
      return {
        title: `$${title}`,
        subtitle,
        description,
      };
    },
  },
};
