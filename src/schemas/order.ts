export const order = {
  name: 'order',
  title: 'Order',
  type: 'document',
  fields: [
    {
      name: 'orderNumber',
      title: 'Order Number',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'userId',
      title: 'User ID (from Clerk)',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'userEmail',
      title: 'User Email',
      type: 'string',
      validation: (Rule: any) => Rule.required().email(),
    },
    {
      name: 'status',
      title: 'Order Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Processing', value: 'processing' },
          { title: 'Shipped', value: 'shipped' },
          { title: 'Delivered', value: 'delivered' },
          { title: 'Cancelled', value: 'cancelled' },
          { title: 'Refunded', value: 'refunded' },
        ],
      },
      initialValue: 'pending',
    },
    {
      name: 'items',
      title: 'Order Items',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'product',
              title: 'Product',
              type: 'reference',
              to: [{ type: 'product' }],
              validation: (Rule: any) => Rule.required(),
            },
            {
              name: 'quantity',
              title: 'Quantity',
              type: 'number',
              validation: (Rule: any) => Rule.required().min(1),
            },
            {
              name: 'price',
              title: 'Unit Price',
              type: 'number',
              validation: (Rule: any) => Rule.required().min(0),
            },
            {
              name: 'totalPrice',
              title: 'Total Price',
              type: 'number',
              validation: (Rule: any) => Rule.required().min(0),
            },
          ],
        },
      ],
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'subtotal',
      title: 'Subtotal',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'tax',
      title: 'Tax Amount',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.min(0),
    },
    {
      name: 'shipping',
      title: 'Shipping Cost',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.min(0),
    },
    {
      name: 'discount',
      title: 'Discount Amount',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.min(0),
    },
    {
      name: 'totalAmount',
      title: 'Total Amount',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'shippingAddress',
      title: 'Shipping Address',
      type: 'object',
      fields: [
        {
          name: 'firstName',
          title: 'First Name',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'lastName',
          title: 'Last Name',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'street',
          title: 'Street Address',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'city',
          title: 'City',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'state',
          title: 'State/Province',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'zipCode',
          title: 'ZIP/Postal Code',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'country',
          title: 'Country',
          type: 'string',
          validation: (Rule: any) => Rule.required(),
        },
        {
          name: 'phone',
          title: 'Phone Number',
          type: 'string',
        },
      ],
    },
    {
      name: 'paymentMethod',
      title: 'Payment Method',
      type: 'string',
      options: {
        list: [
          { title: 'Credit Card', value: 'credit_card' },
          { title: 'PayPal', value: 'paypal' },
          { title: 'Bank Transfer', value: 'bank_transfer' },
          { title: 'Wallet', value: 'wallet' },
        ],
      },
    },
    {
      name: 'paymentStatus',
      title: 'Payment Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Paid', value: 'paid' },
          { title: 'Failed', value: 'failed' },
          { title: 'Refunded', value: 'refunded' },
        ],
      },
      initialValue: 'pending',
    },
    {
      name: 'paymentId',
      title: 'Payment Transaction ID',
      type: 'string',
    },
    {
      name: 'trackingNumber',
      title: 'Tracking Number',
      type: 'string',
    },
    {
      name: 'notes',
      title: 'Order Notes',
      type: 'text',
      rows: 3,
    },
    {
      name: 'mlmProcessed',
      title: 'MLM Commission Processed',
      type: 'boolean',
      initialValue: false,
    },
  ],
  preview: {
    select: {
      title: 'orderNumber',
      subtitle: 'userEmail',
      description: 'status',
    },
  },
};
