export const member = {
  name: 'member',
  title: 'MLM Member',
  type: 'document',
  fields: [
    {
      name: 'userId',
      title: 'User ID (from Clerk)',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'email',
      title: 'Email',
      type: 'string',
      validation: (Rule: any) => Rule.required().email(),
    },
    {
      name: 'firstName',
      title: 'First Name',
      type: 'string',
    },
    {
      name: 'lastName',
      title: 'Last Name',
      type: 'string',
    },
    {
      name: 'level',
      title: 'MLM Level',
      type: 'number',
      initialValue: 1,
      validation: (Rule: any) => Rule.required().min(1),
    },
    {
      name: 'walletBalance',
      title: 'Wallet Balance',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'totalEarnings',
      title: 'Total Lifetime Earnings',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Active', value: 'active' },
          { title: 'Inactive', value: 'inactive' },
          { title: 'Suspended', value: 'suspended' },
        ],
      },
      initialValue: 'active',
    },
    {
      name: 'parent',
      title: 'Sponsor (Parent)',
      type: 'reference',
      to: [{ type: 'member' }],
      description: 'The member who referred this person',
    },
    {
      name: 'leftChild',
      title: 'Left Child',
      type: 'reference',
      to: [{ type: 'member' }],
      description: 'Left side of binary tree',
    },
    {
      name: 'rightChild',
      title: 'Right Child',
      type: 'reference',
      to: [{ type: 'member' }],
      description: 'Right side of binary tree',
    },
    {
      name: 'leftVolume',
      title: 'Left Team Volume',
      type: 'number',
      initialValue: 0,
      description: 'Total sales volume from left team',
    },
    {
      name: 'rightVolume',
      title: 'Right Team Volume',
      type: 'number',
      initialValue: 0,
      description: 'Total sales volume from right team',
    },
    {
      name: 'personalVolume',
      title: 'Personal Volume',
      type: 'number',
      initialValue: 0,
      description: 'Personal sales volume',
    },
    {
      name: 'rank',
      title: 'MLM Rank',
      type: 'string',
      options: {
        list: [
          { title: 'Associate', value: 'associate' },
          { title: 'Senior Associate', value: 'senior_associate' },
          { title: 'Team Leader', value: 'team_leader' },
          { title: 'Senior Team Leader', value: 'senior_team_leader' },
          { title: 'Manager', value: 'manager' },
          { title: 'Senior Manager', value: 'senior_manager' },
          { title: 'Director', value: 'director' },
          { title: 'Senior Director', value: 'senior_director' },
          { title: 'Executive', value: 'executive' },
        ],
      },
      initialValue: 'associate',
    },
    {
      name: 'joinDate',
      title: 'Join Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
    {
      name: 'lastActiveDate',
      title: 'Last Active Date',
      type: 'datetime',
    },
    {
      name: 'referralCode',
      title: 'Referral Code',
      type: 'string',
      description: 'Unique code for referring new members',
    },
    {
      name: 'bankDetails',
      title: 'Bank Details',
      type: 'object',
      fields: [
        {
          name: 'accountNumber',
          title: 'Account Number',
          type: 'string',
        },
        {
          name: 'routingNumber',
          title: 'Routing Number',
          type: 'string',
        },
        {
          name: 'bankName',
          title: 'Bank Name',
          type: 'string',
        },
        {
          name: 'accountHolderName',
          title: 'Account Holder Name',
          type: 'string',
        },
      ],
    },
  ],
  preview: {
    select: {
      title: 'userId',
      subtitle: 'email',
      description: 'rank',
    },
  },
};
