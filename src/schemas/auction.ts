export const auction = {
  name: 'auction',
  title: 'Auction',
  type: 'document',
  fields: [
    {
      name: 'title',
      title: 'Auction Title',
      type: 'string',
      validation: (Rule: any) => Rule.required().min(5).max(100),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'title',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
    },
    {
      name: 'product',
      title: 'Product',
      type: 'reference',
      to: [{ type: 'product' }],
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'vendor',
      title: 'Vendor',
      type: 'reference',
      to: [{ type: 'vendor' }],
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'startingPrice',
      title: 'Starting Price',
      type: 'number',
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'currentBid',
      title: 'Current Highest Bid',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'startDate',
      title: 'Start Date',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'endDate',
      title: 'End Date',
      type: 'datetime',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Scheduled', value: 'scheduled' },
          { title: 'Active', value: 'active' },
          { title: 'Ended', value: 'ended' },
          { title: 'Cancelled', value: 'cancelled' },
        ],
      },
      initialValue: 'scheduled',
    },
    {
      name: 'minBidIncrement',
      title: 'Minimum Bid Increment',
      type: 'number',
      initialValue: 1,
      validation: (Rule: any) => Rule.required().min(0.01),
    },
    {
      name: 'reservePrice',
      title: 'Reserve Price (Optional)',
      type: 'number',
      description: 'Minimum price for the auction to be successful',
    },
    {
      name: 'buyNowPrice',
      title: 'Buy Now Price (Optional)',
      type: 'number',
      description: 'Price at which the auction ends immediately',
    },
    {
      name: 'winnerId',
      title: 'Winner User ID',
      type: 'string',
      description: 'User ID of the auction winner',
    },
    {
      name: 'winningBid',
      title: 'Winning Bid Amount',
      type: 'number',
      description: 'Final winning bid amount',
    },
    {
      name: 'totalBids',
      title: 'Total Number of Bids',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'featured',
      title: 'Featured Auction',
      type: 'boolean',
      initialValue: false,
    },
  ],
  preview: {
    select: {
      title: 'title',
      media: 'product.images.0',
      subtitle: 'status',
    },
  },
};
