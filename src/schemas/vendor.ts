export const vendor = {
  name: 'vendor',
  title: 'Vendor',
  type: 'document',
  fields: [
    {
      name: 'name',
      title: 'Vendor Name',
      type: 'string',
      validation: (Rule: any) => Rule.required().min(2).max(100),
    },
    {
      name: 'slug',
      title: 'Slug',
      type: 'slug',
      options: {
        source: 'name',
        maxLength: 96,
      },
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'userId',
      title: 'User ID (from Clerk)',
      type: 'string',
      validation: (Rule: any) => Rule.required(),
    },
    {
      name: 'email',
      title: 'Email',
      type: 'string',
      validation: (Rule: any) => Rule.required().email(),
    },
    {
      name: 'description',
      title: 'Description',
      type: 'text',
      rows: 4,
    },
    {
      name: 'logo',
      title: 'Logo',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'banner',
      title: 'Banner Image',
      type: 'image',
      options: {
        hotspot: true,
      },
    },
    {
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Active', value: 'active' },
          { title: 'Inactive', value: 'inactive' },
          { title: 'Suspended', value: 'suspended' },
        ],
      },
      initialValue: 'pending',
    },
    {
      name: 'contactInfo',
      title: 'Contact Information',
      type: 'object',
      fields: [
        {
          name: 'phone',
          title: 'Phone Number',
          type: 'string',
        },
        {
          name: 'website',
          title: 'Website',
          type: 'url',
        },
        {
          name: 'address',
          title: 'Address',
          type: 'object',
          fields: [
            {
              name: 'street',
              title: 'Street Address',
              type: 'string',
            },
            {
              name: 'city',
              title: 'City',
              type: 'string',
            },
            {
              name: 'state',
              title: 'State/Province',
              type: 'string',
            },
            {
              name: 'zipCode',
              title: 'ZIP/Postal Code',
              type: 'string',
            },
            {
              name: 'country',
              title: 'Country',
              type: 'string',
            },
          ],
        },
      ],
    },
    {
      name: 'businessInfo',
      title: 'Business Information',
      type: 'object',
      fields: [
        {
          name: 'businessName',
          title: 'Legal Business Name',
          type: 'string',
        },
        {
          name: 'taxId',
          title: 'Tax ID/EIN',
          type: 'string',
        },
        {
          name: 'businessType',
          title: 'Business Type',
          type: 'string',
          options: {
            list: [
              { title: 'Individual', value: 'individual' },
              { title: 'LLC', value: 'llc' },
              { title: 'Corporation', value: 'corporation' },
              { title: 'Partnership', value: 'partnership' },
            ],
          },
        },
      ],
    },
    {
      name: 'paymentInfo',
      title: 'Payment Information',
      type: 'object',
      fields: [
        {
          name: 'accountNumber',
          title: 'Bank Account Number',
          type: 'string',
        },
        {
          name: 'routingNumber',
          title: 'Routing Number',
          type: 'string',
        },
        {
          name: 'bankName',
          title: 'Bank Name',
          type: 'string',
        },
        {
          name: 'paypalEmail',
          title: 'PayPal Email',
          type: 'string',
        },
      ],
    },
    {
      name: 'totalSales',
      title: 'Total Sales',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.required().min(0),
    },
    {
      name: 'commissionRate',
      title: 'Commission Rate (%)',
      type: 'number',
      initialValue: 10,
      validation: (Rule: any) => Rule.required().min(0).max(100),
    },
    {
      name: 'rating',
      title: 'Average Rating',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.min(0).max(5),
    },
    {
      name: 'totalReviews',
      title: 'Total Reviews',
      type: 'number',
      initialValue: 0,
      validation: (Rule: any) => Rule.min(0),
    },
    {
      name: 'joinDate',
      title: 'Join Date',
      type: 'datetime',
      initialValue: () => new Date().toISOString(),
    },
  ],
  preview: {
    select: {
      title: 'name',
      media: 'logo',
      subtitle: 'status',
    },
  },
};
