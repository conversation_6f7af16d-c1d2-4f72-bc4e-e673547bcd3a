import { defineType, defineField } from 'sanity';

export default defineType({
  name: 'walletTransaction',
  title: 'Wallet Transaction',
  type: 'document',
  fields: [
    defineField({
      name: 'user',
      title: 'User',
      type: 'reference',
      to: [{ type: 'user' }],
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'type',
      title: 'Transaction Type',
      type: 'string',
      options: {
        list: [
          { title: 'Deposit', value: 'deposit' },
          { title: 'Withdrawal', value: 'withdrawal' },
          { title: 'Commission', value: 'commission' },
          { title: 'Purchase', value: 'purchase' },
          { title: 'Refund', value: 'refund' },
        ],
      },
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'amount',
      title: 'Amount',
      type: 'number',
      validation: (Rule) => Rule.required().min(0),
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          { title: 'Pending', value: 'pending' },
          { title: 'Completed', value: 'completed' },
          { title: 'Failed', value: 'failed' },
          { title: 'Cancelled', value: 'cancelled' },
        ],
      },
      initialValue: 'pending',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'stripeSessionId',
      title: 'Stripe Session ID',
      type: 'string',
    }),
    defineField({
      name: 'reference',
      title: 'Reference',
      type: 'string',
      description: 'Order ID, Commission ID, etc.',
    }),
  ],
  preview: {
    select: {
      title: 'description',
      subtitle: 'amount',
      media: 'user.name',
    },
    prepare(selection) {
      const { title, subtitle } = selection;
      return {
        title,
        subtitle: `₺${subtitle}`,
      };
    },
  },
});
