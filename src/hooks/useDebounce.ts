import { useState, useEffect, useRef, useCallback } from 'react';

// Generic debounce hook
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Debounced callback hook
export function useDebouncedCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const callbackRef = useRef(callback);
  const timeoutRef = useRef<NodeJS.Timeout>();

  // Update callback ref when callback changes
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        callbackRef.current(...args);
      }, delay);
    },
    [delay]
  ) as T;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return debouncedCallback;
}

// Advanced debounce hook with immediate execution option
export function useAdvancedDebounce<T>(
  value: T,
  delay: number,
  options: {
    immediate?: boolean; // Execute immediately on first call
    maxWait?: number;    // Maximum time to wait before executing
  } = {}
): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const maxTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTimeRef = useRef<number>(0);
  const isFirstCallRef = useRef<boolean>(true);

  useEffect(() => {
    const now = Date.now();
    
    // Immediate execution on first call
    if (options.immediate && isFirstCallRef.current) {
      setDebouncedValue(value);
      isFirstCallRef.current = false;
      lastCallTimeRef.current = now;
      return;
    }

    // Clear existing timeouts
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set up debounced execution
    timeoutRef.current = setTimeout(() => {
      setDebouncedValue(value);
      lastCallTimeRef.current = Date.now();
      
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
      }
    }, delay);

    // Set up max wait timeout
    if (options.maxWait && !maxTimeoutRef.current) {
      const timeSinceLastCall = now - lastCallTimeRef.current;
      const remainingMaxWait = options.maxWait - timeSinceLastCall;

      if (remainingMaxWait > 0) {
        maxTimeoutRef.current = setTimeout(() => {
          setDebouncedValue(value);
          lastCallTimeRef.current = Date.now();
          maxTimeoutRef.current = undefined;
          
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
          }
        }, remainingMaxWait);
      }
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
        maxTimeoutRef.current = undefined;
      }
    };
  }, [value, delay, options.immediate, options.maxWait]);

  return debouncedValue;
}

// Search-specific debounce hook
export function useSearchDebounce(
  searchTerm: string,
  delay: number = 300
): {
  debouncedSearchTerm: string;
  isSearching: boolean;
  cancelSearch: () => void;
} {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
  const [isSearching, setIsSearching] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const cancelSearch = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      setIsSearching(false);
    }
  }, []);

  useEffect(() => {
    // Don't search for empty strings
    if (!searchTerm.trim()) {
      setDebouncedSearchTerm('');
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    timeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setIsSearching(false);
    }, delay);

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [searchTerm, delay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    debouncedSearchTerm,
    isSearching,
    cancelSearch,
  };
}
