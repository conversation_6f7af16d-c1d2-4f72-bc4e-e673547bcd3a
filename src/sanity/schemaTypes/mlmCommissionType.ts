import { defineType } from "sanity";

export const mlmCommissionType = defineType({
  name: "mlmCommission",
  title: "MLM Komisyon",
  type: "document",
  fields: [
    {
      name: "recipient",
      title: "<PERSON><PERSON><PERSON><PERSON>",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
      description: "Komisyonu alan kullanıcı",
    },
    {
      name: "source",
      title: "Kaynak",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
      description: "Komisyonu oluşturan kullanıcı (satış yapan)",
    },
    {
      name: "commissionType",
      title: "Komisyon Türü",
      type: "string",
      options: {
        list: [
          { title: "Doğrudan Referans", value: "direct_referral" },
          { title: "2. Se<PERSON>ye Referans", value: "second_level" },
          { title: "3. Se<PERSON>ye Referans", value: "third_level" },
          { title: "4. <PERSON><PERSON><PERSON>fer<PERSON>", value: "fourth_level" },
          { title: "Sat<PERSON>ş Komisyonu", value: "sales_commission" },
          { title: "Takım Bonusu", value: "team_bonus" },
          { title: "Seviye Yükseltme Bonusu", value: "level_upgrade_bonus" },
          { title: "Aktivite Bonusu", value: "activity_bonus" },
          { title: "Liderlik Bonusu", value: "leadership_bonus" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "amount",
      title: "Komisyon Tutarı (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: "percentage",
      title: "Komisyon Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.min(0).max(100),
      description: "Uygulanan komisyon oranı",
    },
    {
      name: "baseAmount",
      title: "Temel Tutar (TL)",
      type: "number",
      validation: (Rule) => Rule.min(0),
      description: "Komisyonun hesaplandığı temel tutar",
    },
    {
      name: "relatedOrder",
      title: "İlgili Sipariş",
      type: "reference",
      to: [{ type: "order" }],
      description: "Bu komisyonu oluşturan sipariş",
    },
    {
      name: "relatedMembership",
      title: "İlgili Üyelik",
      type: "reference",
      to: [{ type: "membership" }],
      description: "Bu komisyonu oluşturan üyelik",
    },
    {
      name: "level",
      title: "Seviye",
      type: "number",
      validation: (Rule) => Rule.min(1).max(10),
      description: "Komisyonun hangi seviyeden geldiği (1=doğrudan, 2=ikinci seviye, vb.)",
    },
    {
      name: "status",
      title: "Durum",
      type: "string",
      options: {
        list: [
          { title: "Bekliyor", value: "pending" },
          { title: "Onaylandı", value: "approved" },
          { title: "Ödendi", value: "paid" },
          { title: "İptal Edildi", value: "cancelled" },
          { title: "Reddedildi", value: "rejected" },
        ],
      },
      initialValue: "pending",
    },
    {
      name: "paymentMethod",
      title: "Ödeme Yöntemi",
      type: "string",
      options: {
        list: [
          { title: "Cüzdan", value: "wallet" },
          { title: "Banka Transferi", value: "bank_transfer" },
          { title: "Hediye Çeki", value: "gift_voucher" },
        ],
      },
      description: "Komisyonun nasıl ödendiği",
    },
    {
      name: "createdAt",
      title: "Oluşturulma Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
    },
    {
      name: "paidAt",
      title: "Ödenme Tarihi",
      type: "datetime",
      description: "Komisyonun ödendiği tarih",
    },
    {
      name: "description",
      title: "Açıklama",
      type: "text",
      description: "Komisyon hakkında ek bilgiler",
    },
    {
      name: "transactionId",
      title: "İşlem ID",
      type: "string",
      description: "Ödeme işleminin benzersiz ID'si",
    },
  ],
  orderings: [
    {
      title: "Tarih (Yeni → Eski)",
      name: "createdAtDesc",
      by: [{ field: "createdAt", direction: "desc" }],
    },
    {
      title: "Tutar (Yüksek → Düşük)",
      name: "amountDesc",
      by: [{ field: "amount", direction: "desc" }],
    },
  ],
});
