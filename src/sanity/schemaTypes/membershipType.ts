import { defineType } from "sanity";

export const membershipType = defineType({
  name: "membership",
  title: "Üyelik",
  type: "document",
  fields: [
    {
      name: "user",
      title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
      description: "Üyelik sahibi kullanıcı",
    },
    {
      name: "level",
      title: "Seviye",
      type: "string",
      options: {
        list: [
          { title: "Bronz", value: "bronze" },
          { title: "<PERSON><PERSON><PERSON>ü<PERSON>", value: "silver" },
          { title: "<PERSON><PERSON><PERSON>", value: "gold" },
          { title: "Elmas", value: "diamond" },
          { title: "<PERSON><PERSON>rlanta", value: "platinum" },
          { title: "Safir", value: "sapphire" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "membershipNumber",
      title: "Üyelik Numarası",
      type: "string",
      validation: (Rule) => Rule.required(),
      description: "Benzersiz üyelik numarası",
    },
    {
      name: "paymentType",
      title: "Ödeme Türü",
      type: "string",
      options: {
        list: [
          { title: "Tek Seferlik", value: "one_time" },
          { title: "Aylı<PERSON>", value: "monthly" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "amount",
      title: "Ödenen Tutar (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: "status",
      title: "Durum",
      type: "string",
      options: {
        list: [
          { title: "Aktif", value: "active" },
          { title: "Bekliyor", value: "pending" },
          { title: "Süresi Dolmuş", value: "expired" },
          { title: "İptal Edildi", value: "cancelled" },
          { title: "Askıya Alındı", value: "suspended" },
        ],
      },
      initialValue: "pending",
    },
    {
      name: "startDate",
      title: "Başlangıç Tarihi",
      type: "datetime",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "endDate",
      title: "Bitiş Tarihi",
      type: "datetime",
      description: "Üyeliğin sona ereceği tarih (aylık ödemeler için)",
    },
    {
      name: "nextPaymentDate",
      title: "Sonraki Ödeme Tarihi",
      type: "datetime",
      description: "Aylık ödemeler için sonraki ödeme tarihi",
    },
    {
      name: "paymentMethod",
      title: "Ödeme Yöntemi",
      type: "string",
      options: {
        list: [
          { title: "Kredi Kartı", value: "credit_card" },
          { title: "Banka Transferi", value: "bank_transfer" },
          { title: "Cüzdan", value: "wallet" },
          { title: "Hediye Çeki", value: "gift_voucher" },
        ],
      },
    },
    {
      name: "transactionId",
      title: "İşlem ID",
      type: "string",
      description: "Ödeme işleminin benzersiz ID'si",
    },
    {
      name: "stripeSubscriptionId",
      title: "Stripe Abonelik ID",
      type: "string",
      description: "Aylık ödemeler için Stripe abonelik ID'si",
    },
    {
      name: "productPackage",
      title: "Ürün Paketi",
      type: "object",
      fields: [
        {
          name: "value",
          title: "Paket Değeri (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "products",
          title: "Ürünler",
          type: "array",
          of: [
            {
              type: "reference",
              to: [{ type: "product" }],
            },
          ],
        },
        {
          name: "delivered",
          title: "Teslim Edildi",
          type: "boolean",
          initialValue: false,
        },
        {
          name: "deliveryDate",
          title: "Teslimat Tarihi",
          type: "datetime",
        },
      ],
    },
    {
      name: "giftVoucher",
      title: "Hediye Çeki",
      type: "object",
      fields: [
        {
          name: "amount",
          title: "Tutar (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "code",
          title: "Kod",
          type: "string",
        },
        {
          name: "issued",
          title: "Verildi",
          type: "boolean",
          initialValue: false,
        },
        {
          name: "issuedDate",
          title: "Verilme Tarihi",
          type: "datetime",
        },
      ],
    },
    {
      name: "bonuses",
      title: "Bonuslar",
      type: "object",
      fields: [
        {
          name: "walletBonus",
          title: "Cüzdan Bonusu (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "monthlyWalletBonus",
          title: "Aylık Cüzdan Bonusu (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "specialBonuses",
          title: "Özel Bonuslar",
          type: "array",
          of: [
            {
              type: "object",
              fields: [
                {
                  name: "type",
                  title: "Bonus Türü",
                  type: "string",
                },
                {
                  name: "amount",
                  title: "Tutar (TL)",
                  type: "number",
                },
                {
                  name: "description",
                  title: "Açıklama",
                  type: "string",
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: "upgradeHistory",
      title: "Yükseltme Geçmişi",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "fromLevel",
              title: "Önceki Seviye",
              type: "string",
            },
            {
              name: "toLevel",
              title: "Yeni Seviye",
              type: "string",
            },
            {
              name: "upgradeDate",
              title: "Yükseltme Tarihi",
              type: "datetime",
            },
            {
              name: "upgradeFee",
              title: "Yükseltme Ücreti (TL)",
              type: "number",
            },
            {
              name: "reason",
              title: "Sebep",
              type: "string",
              options: {
                list: [
                  { title: "Şartları Sağladı", value: "requirements_met" },
                  { title: "Manuel Ödeme", value: "manual_payment" },
                  { title: "Promosyon", value: "promotion" },
                ],
              },
            },
          ],
        },
      ],
    },
    {
      name: "notes",
      title: "Notlar",
      type: "text",
      description: "Üyelik hakkında ek notlar",
    },
    {
      name: "createdAt",
      title: "Oluşturulma Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
    },
    {
      name: "updatedAt",
      title: "Güncellenme Tarihi",
      type: "datetime",
    },
  ],
  orderings: [
    {
      title: "Tarih (Yeni → Eski)",
      name: "createdAtDesc",
      by: [{ field: "createdAt", direction: "desc" }],
    },
    {
      title: "Seviye",
      name: "levelAsc",
      by: [{ field: "level", direction: "asc" }],
    },
    {
      title: "Aktif Üyelikler",
      name: "activeFirst",
      by: [
        { field: "status", direction: "desc" },
        { field: "createdAt", direction: "desc" },
      ],
    },
  ],
});
