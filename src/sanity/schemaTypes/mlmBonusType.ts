import { defineType } from "sanity";

export const mlmBonusType = defineType({
  name: "mlmBonus",
  title: "MLM Bonusu",
  type: "document",
  fields: [
    {
      name: "recipient",
      title: "<PERSON><PERSON><PERSON><PERSON>",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
      description: "Bonusu alan kullan<PERSON>",
    },
    {
      name: "bonusType",
      title: "Bonus Türü",
      type: "string",
      options: {
        list: [
          { title: "Hoş Geldin Bonusu", value: "welcome_bonus" },
          { title: "Aktivite Bonusu", value: "activity_bonus" },
          { title: "Ciro Bonusu", value: "volume_bonus" },
          { title: "Liderlik Bonusu", value: "leadership_bonus" },
          { title: "Seviye Yükseltme Bonusu", value: "level_upgrade_bonus" },
          { title: "Takım Performans Bonusu", value: "team_performance_bonus" },
          { title: "Aylık Bonus", value: "monthly_bonus" },
          { title: "<PERSON><PERSON><PERSON><PERSON>k Bonus", value: "yearly_bonus" },
          { title: "Özel Etkinlik Bonusu", value: "special_event_bonus" },
          { title: "Sadakat Bonusu", value: "loyalty_bonus" },
          { title: "<PERSON><PERSON>zlı Başlangıç Bonusu", value: "fast_start_bonus" },
          { title: "Binary Bonus", value: "binary_bonus" },
          { title: "Unilevel Bonus", value: "unilevel_bonus" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "amount",
      title: "Bonus Tutarı (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: "paymentType",
      title: "Ödeme Türü",
      type: "string",
      options: {
        list: [
          { title: "Nakit (Cüzdan)", value: "cash" },
          { title: "Hediye Çeki", value: "gift_voucher" },
          { title: "Ürün Kredisi", value: "product_credit" },
          { title: "Puan", value: "points" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "status",
      title: "Durum",
      type: "string",
      options: {
        list: [
          { title: "Bekliyor", value: "pending" },
          { title: "Onaylandı", value: "approved" },
          { title: "Ödendi", value: "paid" },
          { title: "İptal Edildi", value: "cancelled" },
          { title: "Reddedildi", value: "rejected" },
        ],
      },
      initialValue: "pending",
    },
    {
      name: "criteria",
      title: "Bonus Kriterleri",
      type: "object",
      fields: [
        {
          name: "requiredReferrals",
          title: "Gerekli Referans Sayısı",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "requiredSalesVolume",
          title: "Gerekli Satış Hacmi (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "requiredTeamVolume",
          title: "Gerekli Takım Hacmi (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "requiredLevel",
          title: "Gerekli Seviye",
          type: "string",
          options: {
            list: [
              { title: "Bronz", value: "bronze" },
              { title: "Gümüş", value: "silver" },
              { title: "Altın", value: "gold" },
              { title: "Elmas", value: "diamond" },
              { title: "Pırlanta", value: "platinum" },
              { title: "Safir", value: "sapphire" },
            ],
          },
        },
        {
          name: "timeFrame",
          title: "Zaman Dilimi (Gün)",
          type: "number",
          validation: (Rule) => Rule.min(1),
          description: "Kriterlerin sağlanması gereken süre",
        },
      ],
    },
    {
      name: "achievedCriteria",
      title: "Sağlanan Kriterler",
      type: "object",
      fields: [
        {
          name: "actualReferrals",
          title: "Gerçek Referans Sayısı",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "actualSalesVolume",
          title: "Gerçek Satış Hacmi (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "actualTeamVolume",
          title: "Gerçek Takım Hacmi (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "currentLevel",
          title: "Mevcut Seviye",
          type: "string",
        },
        {
          name: "achievedDate",
          title: "Sağlanma Tarihi",
          type: "datetime",
        },
      ],
    },
    {
      name: "relatedSource",
      title: "İlgili Kaynak",
      type: "object",
      fields: [
        {
          name: "sourceType",
          title: "Kaynak Türü",
          type: "string",
          options: {
            list: [
              { title: "Sipariş", value: "order" },
              { title: "Üyelik", value: "membership" },
              { title: "Referans", value: "referral" },
              { title: "Seviye Yükseltme", value: "level_upgrade" },
              { title: "Takım Performansı", value: "team_performance" },
            ],
          },
        },
        {
          name: "sourceId",
          title: "Kaynak ID",
          type: "string",
          description: "İlgili kaynağın ID'si",
        },
        {
          name: "sourceUser",
          title: "Kaynak Kullanıcı",
          type: "reference",
          to: [{ type: "user" }],
          description: "Bonusu tetikleyen kullanıcı",
        },
      ],
    },
    {
      name: "calculationDetails",
      title: "Hesaplama Detayları",
      type: "object",
      fields: [
        {
          name: "baseAmount",
          title: "Temel Tutar (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "percentage",
          title: "Bonus Oranı (%)",
          type: "number",
          validation: (Rule) => Rule.min(0).max(100),
        },
        {
          name: "multiplier",
          title: "Çarpan",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "formula",
          title: "Hesaplama Formülü",
          type: "string",
          description: "Bonus hesaplama formülü",
        },
      ],
    },
    {
      name: "expiryDate",
      title: "Son Kullanma Tarihi",
      type: "datetime",
      description: "Bonusun geçerlilik süresi",
    },
    {
      name: "createdAt",
      title: "Oluşturulma Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
    },
    {
      name: "paidAt",
      title: "Ödenme Tarihi",
      type: "datetime",
      description: "Bonusun ödendiği tarih",
    },
    {
      name: "description",
      title: "Açıklama",
      type: "text",
      description: "Bonus hakkında detaylı açıklama",
    },
    {
      name: "notes",
      title: "Notlar",
      type: "text",
      description: "Bonus hakkında ek notlar",
    },
    {
      name: "transactionId",
      title: "İşlem ID",
      type: "string",
      description: "Ödeme işleminin benzersiz ID'si",
    },
  ],
  orderings: [
    {
      title: "Tarih (Yeni → Eski)",
      name: "createdAtDesc",
      by: [{ field: "createdAt", direction: "desc" }],
    },
    {
      title: "Tutar (Yüksek → Düşük)",
      name: "amountDesc",
      by: [{ field: "amount", direction: "desc" }],
    },
    {
      title: "Durum",
      name: "statusAsc",
      by: [
        { field: "status", direction: "asc" },
        { field: "createdAt", direction: "desc" },
      ],
    },
  ],
});
