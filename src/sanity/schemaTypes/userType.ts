// schemas/user.js
import { defineType } from "sanity";

export const userType = defineType({
  name: "user",
  title: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  type: "document",
  fields: [
    {
      name: "clerkId",
      title: "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>",
      type: "string",
      validation: (Rule) => Rule.required(),
      description: "Clerk'ten gelen benzersiz kullanıcı ID'si",
    },
    {
      name: "name",
      title: "<PERSON>si<PERSON>",
      type: "string",
    },
    {
      name: "email",
      title: "E-posta",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "imageUrl",
      title: "Profil Resmi URL",
      type: "string",
    },
    {
      name: "phone",
      title: "Telefon",
      type: "string",
      description: "Kullanıcının telefon numarası",
    },
    {
      name: "isAdminApproved",
      title: "Yönetici Onaylı",
      type: "boolean",
      initialValue: false,
      description: "<PERSON><PERSON> kullanıcı açık artırmalara teklif verebilir mi?",
    },
    {
      name: "walletBalance",
      title: "Cüzdan Bakiyesi (TL)",
      type: "number",
      initialValue: 0,
      description: "Kullanıcının cüzdan bakiyesi. Bilet alırken buradan düşülür.",
    },
    {
      name: "isAdmin",
      title: "Yönetici (Admin)",
      type: "boolean",
      initialValue: false,
      description: "Bu kullanıcıya admin yetkisi verildi mi? Çekiliş paneli ve diğer admin işlemleri için."
    },
    {
      name: "isSeller",
      title: "Satıcı",
      type: "boolean",
      initialValue: false,
      description: "Bu kullanıcı açık artırma oluşturabilir mi?"
    },
    // MLM Alanları
    {
      name: "mlmLevel",
      title: "MLM Seviyesi",
      type: "string",
      options: {
        list: [
          { title: "Bronz", value: "bronze" },
          { title: "Gümüş", value: "silver" },
          { title: "Altın", value: "gold" },
          { title: "Elmas", value: "diamond" },
          { title: "Pırlanta", value: "platinum" },
          { title: "Safir", value: "sapphire" },
        ],
      },
      initialValue: "bronze",
      description: "Kullanıcının MLM üyelik seviyesi",
    },
    {
      name: "sponsor",
      title: "Sponsor",
      type: "reference",
      to: [{ type: "user" }],
      description: "Bu kullanıcıyı sisteme davet eden sponsor",
    },
    {
      name: "sponsorCode",
      title: "Sponsor Kodu",
      type: "string",
      description: "Bu kullanıcının benzersiz sponsor kodu",
    },
    {
      name: "leftChild",
      title: "Sol Çocuk",
      type: "reference",
      to: [{ type: "user" }],
      description: "Binary ağaçta sol taraftaki çocuk",
    },
    {
      name: "rightChild",
      title: "Sağ Çocuk",
      type: "reference",
      to: [{ type: "user" }],
      description: "Binary ağaçta sağ taraftaki çocuk",
    },
    {
      name: "totalCommissionEarned",
      title: "Toplam Kazanılan Komisyon (TL)",
      type: "number",
      initialValue: 0,
      description: "Kullanıcının şimdiye kadar kazandığı toplam komisyon",
    },
    {
      name: "monthlyCommissionEarned",
      title: "Aylık Komisyon (TL)",
      type: "number",
      initialValue: 0,
      description: "Bu ay kazanılan komisyon",
    },
    {
      name: "totalSales",
      title: "Toplam Satış (TL)",
      type: "number",
      initialValue: 0,
      description: "Kullanıcının gerçekleştirdiği toplam satış tutarı",
    },
    {
      name: "totalTeamSales",
      title: "Takım Satışları (TL)",
      type: "number",
      initialValue: 0,
      description: "Alt ağaçtaki tüm satışların toplamı",
    },
    {
      name: "directReferrals",
      title: "Doğrudan Referanslar",
      type: "number",
      initialValue: 0,
      description: "Doğrudan davet ettiği üye sayısı",
    },
    {
      name: "totalTeamMembers",
      title: "Toplam Takım Üyesi",
      type: "number",
      initialValue: 0,
      description: "Alt ağaçtaki toplam üye sayısı",
    },
    {
      name: "membershipStartDate",
      title: "Üyelik Başlangıç Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
      description: "MLM üyeliğinin başladığı tarih",
    },
    {
      name: "lastLevelUpgrade",
      title: "Son Seviye Yükseltme",
      type: "datetime",
      description: "Son seviye yükseltmesinin yapıldığı tarih",
    },
    {
      name: "isActive",
      title: "Aktif Üye",
      type: "boolean",
      initialValue: true,
      description: "Üyelik aktif mi? (Aidat ödemesi durumu)",
    },
    {
      name: "nextPaymentDate",
      title: "Sonraki Ödeme Tarihi",
      type: "datetime",
      description: "Bir sonraki aidat ödeme tarihi",
    },
    {
      name: "giftVoucherBalance",
      title: "Hediye Çeki Bakiyesi (TL)",
      type: "number",
      initialValue: 0,
      description: "Kullanıcının hediye çeki bakiyesi",
    },
  ],
});

export const walletTopUpRequestType = defineType({
  name: "walletTopUpRequest",
  title: "Cüzdan Yükleme Talebi",
  type: "document",
  fields: [
    {
      name: "user",
      title: "Kullanıcı",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
    },
    {
      name: "amount",
      title: "Yüklenecek Tutar (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(1),
    },
    {
      name: "status",
      title: "Durum",
      type: "string",
      options: {
        list: [
          { title: "Bekliyor", value: "pending" },
          { title: "Onaylandı", value: "approved" },
          { title: "Reddedildi", value: "rejected" },
        ],
      },
      initialValue: "pending",
    },
    {
      name: "createdAt",
      title: "Talep Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
    },
    {
      name: "approvedAt",
      title: "Onay Tarihi",
      type: "datetime",
    },
  ],
});
