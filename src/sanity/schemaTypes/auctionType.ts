import { defineType } from "sanity";

export const auctionType = defineType({
  name: 'auction',
  title: 'Açık Artırma',
  type: 'document',
  fields: [
    {
      name: 'id',
      title: 'Açık Artırma ID',
      type: 'string',
      validation: (Rule) => Rule.required(),
      description: 'Benzersiz açık artırma kimliği',
    },
    {
      name: 'name',
      title: '<PERSON>rün <PERSON>',
      type: 'string',
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'description',
      title: '<PERSON><PERSON><PERSON><PERSON>çıklamas<PERSON>',
      type: 'text',
    },
    {
      name: 'image',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'image',
      options: { hotspot: true },
    },
    {
      name: 'images',
      title: '<PERSON><PERSON><PERSON><PERSON>',
      type: 'array',
      of: [
        {
          type: 'image',
          options: { hotspot: true },
        },
      ],
      description: 'Ürünün birden fazla görse<PERSON>',
    },
    {
      name: 'currentBid',
      title: 'Mevcut Teklif',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'startingBid',
      title: '<PERSON>şlangı<PERSON> Teklifi',
      type: 'number',
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: 'bidIncrementAmount',
      title: 'Minimum Teklif Artış Miktarı',
      type: 'number',
      initialValue: 1,
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: 'reservePrice',
      title: 'Rezerv Fiyat',
      type: 'number',
      description: 'Minimum satış fiyatı (opsiyonel)',
    },
    {
      name: 'buyNowPrice',
      title: 'Hemen Al Fiyatı',
      type: 'number',
      description: 'Açık artırmayı sonlandıran fiyat (opsiyonel)',
    },
    {
      name: 'startTime',
      title: 'Başlangıç Zamanı',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'endTime',
      title: 'Bitiş Zamanı',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
    },
    {
      name: 'bidders',
      title: 'Teklif Verenler',
      type: 'array',
      of: [{ type: 'reference', to: [{ type: 'user' }] }],
      description: 'Açık artırmaya katılan kullanıcılar',
    },
    {
      name: 'bidHistory',
      title: 'Teklif Geçmişi',
      type: 'array',
      of: [
        {
          type: 'object',
          fields: [
            {
              name: 'bidAmount',
              title: 'Teklif Miktarı',
              type: 'number',
              validation: (Rule) => Rule.required().min(0),
            },
            {
              name: 'bidder',
              title: 'Teklif Veren',
              type: 'reference',
              to: [{ type: 'user' }],
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'bidTime',
              title: 'Teklif Zamanı',
              type: 'datetime',
              validation: (Rule) => Rule.required(),
            },
            {
              name: 'isAutoBid',
              title: 'Otomatik Teklif',
              type: 'boolean',
              initialValue: false,
              description: 'Bu teklif otomatik teklif sistemi tarafından mı verildi?',
            },
          ],
          preview: {
            select: {
              title: 'bidder.name',
              subtitle: 'bidAmount',
              date: 'bidTime',
            },
            prepare({ title, subtitle, date }) {
              return {
                title: `Teklif Veren: ${title || 'Bilinmiyor'}`,
                subtitle: `Miktar: ₺${subtitle} - Zaman: ${new Date(date).toLocaleString()}`,
              };
            },
          },
        },
      ],
    },
    {
      name: 'winner',
      title: 'Kazanan',
      type: 'reference',
      to: [{ type: 'user' }],
      description: 'Açık artırmayı kazanan kullanıcı',
    },
    {
      name: 'winningBid',
      title: 'Kazanan Teklif',
      type: 'number',
      description: 'Kazanan teklifin miktarı',
    },
    {
      name: 'status',
      title: 'Durum',
      type: 'string',
      options: {
        list: [
          { title: 'Beklemede', value: 'pending' },
          { title: 'Aktif', value: 'active' },
          { title: 'Tamamlandı', value: 'completed' },
          { title: 'İptal', value: 'cancelled' },
          { title: 'Ödeme Bekliyor', value: 'payment_pending' },
          { title: 'Teslim Edildi', value: 'delivered' },
        ],
      },
      initialValue: 'pending',
    },
    {
      name: 'category',
      title: 'Kategori',
      type: 'string',
      options: {
        list: [
          { title: 'Elektronik', value: 'electronics' },
          { title: 'Giyim', value: 'clothing' },
          { title: 'Ev & Yaşam', value: 'home-living' },
          { title: 'Spor', value: 'sports' },
          { title: 'Kitap', value: 'books' },
          { title: 'Sanat', value: 'art' },
          { title: 'Koleksiyon', value: 'collectibles' },
          { title: 'Otomotiv', value: 'automotive' },
          { title: 'Diğer', value: 'other' },
        ],
      },
    },
    {
      name: 'condition',
      title: 'Durum',
      type: 'string',
      options: {
        list: [
          { title: 'Yeni', value: 'new' },
          { title: 'Çok İyi', value: 'excellent' },
          { title: 'İyi', value: 'good' },
          { title: 'Orta', value: 'fair' },
          { title: 'Kötü', value: 'poor' },
        ],
      },
      description: 'Ürünün fiziksel durumu',
    },
    {
      name: 'seller',
      title: 'Satıcı',
      type: 'reference',
      to: [{ type: 'user' }],
      validation: (Rule) => Rule.required(),
      description: 'Açık artırmayı başlatan kullanıcı',
    },
    {
      name: 'featured',
      title: 'Öne Çıkan',
      type: 'boolean',
      initialValue: false,
      description: 'Ana sayfada öne çıkarılsın mı?',
    },
    {
      name: 'autoExtend',
      title: 'Otomatik Uzatma',
      type: 'boolean',
      initialValue: true,
      description: 'Son dakikalarda teklif gelirse süre uzatılsın mı?',
    },
    {
      name: 'extendMinutes',
      title: 'Uzatma Süresi (Dakika)',
      type: 'number',
      initialValue: 5,
      description: 'Otomatik uzatma durumunda kaç dakika uzatılacak',
    },
    {
      name: 'shippingInfo',
      title: 'Kargo Bilgileri',
      type: 'object',
      fields: [
        {
          name: 'shippingCost',
          title: 'Kargo Ücreti',
          type: 'number',
          initialValue: 0,
        },
        {
          name: 'freeShippingThreshold',
          title: 'Ücretsiz Kargo Eşiği',
          type: 'number',
          description: 'Bu tutarın üzerinde ücretsiz kargo',
        },
        {
          name: 'estimatedDelivery',
          title: 'Tahmini Teslimat (Gün)',
          type: 'number',
          initialValue: 3,
        },
      ],
    },
    {
      name: 'tags',
      title: 'Etiketler',
      type: 'array',
      of: [{ type: 'string' }],
      description: 'Arama için etiketler',
    },
    {
      name: 'viewCount',
      title: 'Görüntülenme Sayısı',
      type: 'number',
      initialValue: 0,
    },
    {
      name: 'watchCount',
      title: 'İzleyici Sayısı',
      type: 'number',
      initialValue: 0,
      description: 'Kaç kişi bu açık artırmayı takip ediyor',
    },
    {
      name: 'notes',
      title: 'Notlar',
      type: 'text',
      description: 'Satıcı notları veya özel açıklamalar',
    },
  ],
  orderings: [
    {
      title: 'Bitiş Zamanı (Yakın → Uzak)',
      name: 'endTimeAsc',
      by: [{ field: 'endTime', direction: 'asc' }],
    },
    {
      title: 'Mevcut Teklif (Yüksek → Düşük)',
      name: 'currentBidDesc',
      by: [{ field: 'currentBid', direction: 'desc' }],
    },
    {
      title: 'Oluşturulma Tarihi (Yeni → Eski)',
      name: 'createdAtDesc',
      by: [{ field: '_createdAt', direction: 'desc' }],
    },
    {
      title: 'Popülerlik (Görüntülenme)',
      name: 'viewCountDesc',
      by: [{ field: 'viewCount', direction: 'desc' }],
    },
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'currentBid',
      media: 'image',
      status: 'status',
    },
    prepare({ title, subtitle, media, status }) {
      return {
        title: title || 'İsimsiz Açık Artırma',
        subtitle: `₺${subtitle || 0} - ${status || 'Durum Belirsiz'}`,
        media,
      };
    },
  },
});
