import { TagIcon } from "@sanity/icons";
import { defineField, defineType } from "sanity";

export const salesType = defineType({
    name: 'sales',
    title: 'Sales',
    type: 'document',
    icon: TagIcon,
    fields: [
        defineField({
            name: 'title',
            title: 'Title',
            type: 'string',
        }),
        defineField({
            name: 'description',
            title: 'Sale Description',
            type: 'text',
        }),
        defineField({
            name: 'discountAmount',
            title: 'Discount Amount',
            type: 'number',
            description: 'The amount of discount to apply to the product',
        }),
        defineField({
            name: 'validityFrom',
            title: 'Validity From',
            type: 'date',
        }),
        defineField({
            name: 'validityUntil',
            title: 'Validity Until',
            type: 'date',
        }),
        defineField({
            name: 'isActive',
            title: 'Is Active',
            type: 'boolean',
            description: 'If the sale is active',
            initialValue: true,
        }),
        defineField({
            name: 'couponCode',
            title: 'Coupon Code',
            type: 'string',
            description: 'The coupon code to apply to the product',
        }),
    ],
    preview: {
        select: {
            title: 'title',
            discountAmount: 'discountAmount',
            couponCode: 'couponCode',
            isActive: 'isActive',
        },
        prepare(selection) {
            const {title, discountAmount, couponCode, isActive } = selection;
            const status = isActive ? 'Active' : 'Inactive';
            return {
                title: title,
                subtitle: `${discountAmount}% off -Code:${couponCode} - ${status}`,
            };
        },
    },
});
