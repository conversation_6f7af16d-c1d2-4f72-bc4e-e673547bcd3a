import { defineType } from "sanity";

export const giftVoucherType = defineType({
  name: "giftVoucher",
  title: "Hediye Çeki",
  type: "document",
  fields: [
    {
      name: "code",
      title: "Hediye Çeki Kodu",
      type: "string",
      validation: (Rule) => Rule.required(),
      description: "Benzersiz hediye çeki kodu",
    },
    {
      name: "owner",
      title: "<PERSON><PERSON>",
      type: "reference",
      to: [{ type: "user" }],
      validation: (Rule) => Rule.required(),
      description: "Hediye çekinin sahibi olan kullanıcı",
    },
    {
      name: "amount",
      title: "<PERSON><PERSON> (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Hediye çekinin tutarı",
    },
    {
      name: "remainingAmount",
      title: "<PERSON><PERSON> (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Hediye çekinin kalan tutarı",
    },
    {
      name: "isActive",
      title: "Aktif",
      type: "boolean",
      initialValue: true,
      description: "Hediye çeki kullanılabilir mi?",
    },
    {
      name: "isUsed",
      title: "<PERSON><PERSON><PERSON>ldı",
      type: "boolean",
      initialValue: false,
      description: "Hediye çeki tamamen kullanıldı mı?",
    },
    {
      name: "sourceType",
      title: "Kaynak Türü",
      type: "string",
      options: {
        list: [
          { title: "Üyelik Bonusu", value: "membership_bonus" },
          { title: "Referans Bonusu", value: "referral_bonus" },
          { title: "Satış Bonusu", value: "sales_bonus" },
          { title: "Seviye Yükseltme Bonusu", value: "level_upgrade_bonus" },
          { title: "Aktivite Bonusu", value: "activity_bonus" },
          { title: "Liderlik Bonusu", value: "leadership_bonus" },
          { title: "Manuel Oluşturma", value: "manual_creation" },
          { title: "Promosyon", value: "promotion" },
        ],
      },
      validation: (Rule) => Rule.required(),
      description: "Hediye çekinin nasıl oluşturulduğu",
    },
    {
      name: "sourceReference",
      title: "Kaynak Referansı",
      type: "string",
      description: "Hediye çekini oluşturan işlemin referansı (sipariş ID, üyelik ID, vb.)",
    },
    {
      name: "expiryDate",
      title: "Son Kullanma Tarihi",
      type: "datetime",
      description: "Hediye çekinin son kullanma tarihi",
    },
    {
      name: "createdAt",
      title: "Oluşturulma Tarihi",
      type: "datetime",
      initialValue: (new Date()).toISOString(),
    },
    {
      name: "usedAt",
      title: "Kullanılma Tarihi",
      type: "datetime",
      description: "Hediye çekinin tamamen kullanıldığı tarih",
    },
    {
      name: "description",
      title: "Açıklama",
      type: "text",
      description: "Hediye çeki hakkında ek bilgiler",
    },
    {
      name: "minOrderAmount",
      title: "Minimum Sipariş Tutarı (TL)",
      type: "number",
      validation: (Rule) => Rule.min(0),
      description: "Hediye çekini kullanmak için minimum sipariş tutarı",
    },
    {
      name: "maxUsageAmount",
      title: "Maksimum Kullanım Tutarı (TL)",
      type: "number",
      validation: (Rule) => Rule.min(0),
      description: "Tek seferde kullanılabilecek maksimum tutar",
    },
    {
      name: "applicableCategories",
      title: "Geçerli Kategoriler",
      type: "array",
      of: [
        {
          type: "reference",
          to: [{ type: "category" }],
        },
      ],
      description: "Hediye çekinin hangi kategorilerde kullanılabileceği (boş ise tüm kategoriler)",
    },
    {
      name: "usageHistory",
      title: "Kullanım Geçmişi",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "usedAmount",
              title: "Kullanılan Tutar (TL)",
              type: "number",
              validation: (Rule) => Rule.required().min(0),
            },
            {
              name: "relatedOrder",
              title: "İlgili Sipariş",
              type: "reference",
              to: [{ type: "order" }],
            },
            {
              name: "usedAt",
              title: "Kullanım Tarihi",
              type: "datetime",
              initialValue: (new Date()).toISOString(),
            },
            {
              name: "description",
              title: "Açıklama",
              type: "string",
            },
          ],
        },
      ],
      description: "Hediye çekinin kullanım geçmişi",
    },
  ],
  orderings: [
    {
      title: "Tarih (Yeni → Eski)",
      name: "createdAtDesc",
      by: [{ field: "createdAt", direction: "desc" }],
    },
    {
      title: "Tutar (Yüksek → Düşük)",
      name: "amountDesc",
      by: [{ field: "amount", direction: "desc" }],
    },
    {
      title: "Aktif Hediye Çekleri",
      name: "activeFirst",
      by: [
        { field: "isActive", direction: "desc" },
        { field: "createdAt", direction: "desc" },
      ],
    },
  ],
});
