import { defineType } from "sanity";

export const giveawayType = defineType({
  name: "giveaway",
  title: "<PERSON><PERSON><PERSON><PERSON>",
  type: "document",
  fields: [
    {
      name: "title",
      title: "<PERSON><PERSON><PERSON><PERSON> Başlığı",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "description",
      title: "Açıklama",
      type: "text",
    },
    {
      name: "status",
      title: "Durum",
      type: "string",
      options: {
        list: [
          { title: "Aktif", value: "active" },
          { title: "Pasif", value: "inactive" },
          { title: "Tamamlandı", value: "completed" },
        ],
      },
      initialValue: "inactive",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "startDate",
      title: "<PERSON>şlangı<PERSON> Tarihi",
      type: "datetime",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "endDate",
      title: "Bitiş Tarihi",
      type: "datetime",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "drawDate",
      title: "<PERSON><PERSON><PERSON><PERSON>",
      type: "datetime",
      description: "Çekiliş ne zaman yapılacak (otomatik olarak ayarlanabilir)",
    },
    {
      name: "totalTickets",
      title: "Toplam Bilet Sayısı",
      type: "number",
      validation: (Rule) => Rule.required().min(1),
    },
    {
      name: "ticketsSold",
      title: "Satılan Bilet Sayısı",
      type: "number",
      initialValue: 0,
      readOnly: true,
    },
    {
      name: "ticketPrice",
      title: "Bilet Fiyatı (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
    },
    {
      name: "ticketSalePercentageForDraw",
      title: "Çekiliş İçin Gerekli Satış Yüzdesi",
      type: "number",
      description: "Bu yüzde satıldığında çekiliş tarihi otomatik ayarlanır",
      validation: (Rule) => Rule.min(0).max(100),
      initialValue: 80,
    },
    {
      name: "numbersPerCard",
      title: "Kart Başına Numara Adedi",
      type: "number",
      description: "Her kartta kaç farklı bilet numarası olacak? (örn: 3 veya 9)",
      validation: (Rule) => Rule.required().min(1),
      initialValue: 3,
    },
    {
      name: "ticketDigitLength",
      title: "Bilet Numarası Hane Sayısı",
      type: "number",
      description: "Bilet numaraları kaç haneli olacak? (örn: 3, 4, 5)",
      validation: (Rule) => Rule.required().min(3).max(5),
      initialValue: 3,
    },
    {
      name: "participants",
      title: "Katılımcılar",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "user",
              title: "Kullanıcı",
              type: "reference",
              to: [{ type: "user" }],
            },
            {
              name: "tickets",
              title: "Biletler",
              type: "array",
              of: [
                {
                  type: "object",
                  fields: [
                    {
                      name: "ticketNumber",
                      title: "Bilet Numarası",
                      type: "string",
                    },
                    {
                      name: "purchasedAt",
                      title: "Satın Alınma Tarihi",
                      type: "datetime",
                    },
                    {
                      name: "chosenDigitCount",
                      title: "Seçilen Rakam Sayısı",
                      type: "number",
                    },
                    {
                      name: "status",
                      title: "Durum",
                      type: "string",
                      options: {
                        list: [
                          { title: "Kazandınız", value: "won" },
                          { title: "Kazanamadınız", value: "lost" },
                        ],
                      },
                      initialValue: "lost",
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      name: "prizes",
      title: "Ödüller",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "rank",
              title: "Sıralama",
              type: "number",
              validation: (Rule) => Rule.required().min(1),
            },
            {
              name: "title",
              title: "Ödül Başlığı",
              type: "string",
              validation: (Rule) => Rule.required(),
            },
            {
              name: "description",
              title: "Ödül Açıklaması",
              type: "text",
            },
            {
              name: "value",
              title: "Ödül Değeri (TL)",
              type: "number",
            },
            {
              name: "image",
              title: "Ödül Resmi",
              type: "image",
            },
          ],
        },
      ],
    },
    {
      name: "winningNumbers",
      title: "Kazanan Numaralar",
      type: "array",
      of: [{ type: "string" }],
      description: "Çekiliş sonrası belirlenen kazanan numaralar. Katılımcı ve bilet eşleşmesini görmek için katılımcılar alanına bakın.",
    },
    {
      name: "winners",
      title: "Kazananlar",
      type: "array",
      of: [
        {
          type: "object",
          fields: [
            {
              name: "user",
              title: "Kazanan Kullanıcı",
              type: "reference",
              to: [{ type: "user" }],
            },
            {
              name: "ticketNumber",
              title: "Kazanan Bilet Numarası",
              type: "string",
            },
            {
              name: "prize",
              title: "Kazanılan Ödül",
              type: "string",
            },
            {
              name: "rank",
              title: "Kazanma Sırası",
              type: "number",
            },
          ],
        },
      ],
    },
    {
      name: "rules",
      title: "Çekiliş Kuralları",
      type: "text",
    },
    {
      name: "image",
      title: "Çekiliş Görseli",
      type: "image",
      options: {
        hotspot: true,
      },
    },
  ],
  preview: {
    select: {
      title: "title",
      status: "status",
      ticketsSold: "ticketsSold",
      totalTickets: "totalTickets",
      media: "image",
    },
    prepare(selection) {
      const { title, status, ticketsSold, totalTickets } = selection;
      return {
        title: title,
        subtitle: `${status} - ${ticketsSold || 0}/${totalTickets || 0} bilet satıldı`,
      };
    },
  },
});
