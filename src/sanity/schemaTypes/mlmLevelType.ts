import { defineType } from "sanity";

export const mlmLevelType = defineType({
  name: "mlmLevel",
  title: "MLM Seviyesi",
  type: "document",
  fields: [
    {
      name: "level",
      title: "<PERSON><PERSON><PERSON>",
      type: "string",
      options: {
        list: [
          { title: "<PERSON>ronz", value: "bronze" },
          { title: "<PERSON>ü<PERSON>ü<PERSON>", value: "silver" },
          { title: "Altın", value: "gold" },
          { title: "Elmas", value: "diamond" },
          { title: "<PERSON>ırlant<PERSON>", value: "platinum" },
          { title: "Safir", value: "sapphire" },
        ],
      },
      validation: (Rule) => Rule.required(),
    },
    {
      name: "title",
      title: "Seviye Adı",
      type: "string",
      validation: (Rule) => Rule.required(),
    },
    {
      name: "description",
      title: "Açıklama",
      type: "text",
    },
    {
      name: "membershipFee",
      title: "Üyelik Ücreti (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Tek seferlik üyelik ücreti",
    },
    {
      name: "monthlyFee",
      title: "Aylık Aidat (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Aylık aidat ücreti",
    },
    {
      name: "productPackageValue",
      title: "Ürün Paketi Değeri (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Üyelikle birlikte verilen ürün paketinin değeri",
    },
    {
      name: "discountPercentage",
      title: "İndirim Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.required().min(0).max(100),
      description: "Ürün alımlarında geçerli indirim oranı",
    },
    {
      name: "directCommissionRate",
      title: "Doğrudan Komisyon Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.required().min(0).max(100),
      description: "Doğrudan referanstan kazanılan komisyon oranı",
    },
    {
      name: "secondLevelCommissionRate",
      title: "2. Seviye Komisyon Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.min(0).max(100),
      description: "İkinci seviye referanstan kazanılan komisyon oranı",
    },
    {
      name: "thirdLevelCommissionRate",
      title: "3. Seviye Komisyon Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.min(0).max(100),
      description: "Üçüncü seviye referanstan kazanılan komisyon oranı",
    },
    {
      name: "fourthLevelCommissionRate",
      title: "4. Seviye Komisyon Oranı (%)",
      type: "number",
      validation: (Rule) => Rule.min(0).max(100),
      description: "Dördüncü seviye referanstan kazanılan komisyon oranı",
    },
    {
      name: "giftVoucherAmount",
      title: "Hediye Çeki Tutarı (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Üyelikle birlikte verilen hediye çeki tutarı",
    },
    {
      name: "monthlyWalletBonus",
      title: "Aylık Cüzdan Bonusu (TL)",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Her ay cüzdana yatırılan bonus tutarı",
    },
    {
      name: "upgradeRequirements",
      title: "Yükseltme Şartları",
      type: "object",
      fields: [
        {
          name: "requiredReferrals",
          title: "Gerekli Referans Sayısı",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "requiredSalesVolume",
          title: "Gerekli Satış Hacmi (TL)",
          type: "number",
          validation: (Rule) => Rule.min(0),
        },
        {
          name: "timeLimit",
          title: "Zaman Sınırı (Gün)",
          type: "number",
          validation: (Rule) => Rule.min(0),
          description: "Şartları sağlamak için verilen süre (gün)",
        },
      ],
    },
    {
      name: "benefits",
      title: "Avantajlar",
      type: "array",
      of: [{ type: "string" }],
      description: "Bu seviyenin sağladığı avantajlar listesi",
    },
    {
      name: "isActive",
      title: "Aktif",
      type: "boolean",
      initialValue: true,
      description: "Bu seviye aktif mi?",
    },
    {
      name: "order",
      title: "Sıralama",
      type: "number",
      validation: (Rule) => Rule.required().min(0),
      description: "Seviyelerin sıralanması için kullanılır",
    },
  ],
  orderings: [
    {
      title: "Seviye Sırası",
      name: "orderAsc",
      by: [{ field: "order", direction: "asc" }],
    },
  ],
});
