import { type SchemaTypeDefinition } from 'sanity'

import {blockContentType} from './blockContentType'
import {categoryType} from './categoryType'
import {postType} from './postType'
import {authorType} from './authorType'

// E-commerce schemas
import {product} from '../../schemas/product'
import {vendor} from '../../schemas/vendor'
import {order} from '../../schemas/order'
import {bid} from '../../schemas/bid'
import {member} from '../../schemas/member'
import {commission} from '../../schemas/commission'

// MLM schemas
import {userType, walletTopUpRequestType} from './userType'
import {mlmBonusType} from './mlmBonusType'
import {mlmLevelType} from './mlmLevelType'
import {salesType} from './salesType'
import {mlmCommissionType} from './mlmCommissionType'
import {giftVoucherType} from './giftVoucherType'
import {membershipType} from './membershipType'
import {auctionType} from './auctionType'
import {giveawayType} from './giveawayType'

export const schema: { types: SchemaTypeDefinition[] } = {
  types: [
    // Blog schemas (default)
    blockContentType,
    categoryType,
    postType,
    authorType,
    // E-commerce schemas
    product,
    vendor,
    order,
    bid,
    member,
    commission,
    // MLM schemas
    userType,
    walletTopUpRequestType,
    mlmBonusType,
    mlmLevelType,
    salesType,
    mlmCommissionType,
    giftVoucherType,
    membershipType,
    auctionType,
    giveawayType,
  ],
}
