import createImageUrlBuilder from '@sanity/image-url'
import { SanityImageSource } from "@sanity/image-url/lib/types/types";

import { dataset, projectId } from '../env'

// https://www.sanity.io/docs/image-url
const builder = createImageUrlBuilder({ projectId, dataset })

export const urlFor = (source: SanityImageSource) => {
  return builder.image(source)
}

// Helper function to safely get image URL from Sanity image object
export const getImageUrl = (image: any, width?: number, height?: number): string | null => {
  if (!image) return null;

  // If image already has asset.url, use it directly
  if (image.asset?.url) {
    return image.asset.url;
  }

  // If image has asset._ref or asset._id, build URL using urlFor
  if (image.asset?._ref || image.asset?._id || image._ref || image._id) {
    try {
      let url = urlFor(image);
      if (width) url = url.width(width);
      if (height) url = url.height(height);
      return url.url();
    } catch (error) {
      console.error('Error building image URL:', error);
      return null;
    }
  }

  // If it's a direct URL string
  if (typeof image === 'string' && (image.startsWith('http') || image.startsWith('/'))) {
    return image;
  }

  return null;
}

// Helper function to get optimized image URL for different sizes
export const getOptimizedImageUrl = (image: any, options: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpg' | 'png';
} = {}): string | null => {
  const url = getImageUrl(image);
  if (!url) return null;

  try {
    let builder = urlFor(image);

    if (options.width) builder = builder.width(options.width);
    if (options.height) builder = builder.height(options.height);
    if (options.quality) builder = builder.quality(options.quality);
    if (options.format) builder = builder.format(options.format);

    return builder.url();
  } catch (error) {
    console.error('Error building optimized image URL:', error);
    return url; // Fallback to basic URL
  }
}
