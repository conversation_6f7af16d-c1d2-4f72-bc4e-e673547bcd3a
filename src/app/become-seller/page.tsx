'use client';

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Store, CheckCircle, Clock, AlertCircle } from 'lucide-react';

export default function BecomeSellerPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  
  const [formData, setFormData] = useState({
    businessType: '',
    businessName: '',
    businessAddress: '',
    taxNumber: '',
    contactPerson: '',
    contactPhone: '',
    contactEmail: '',
    businessDescription: '',
    productCategories: '',
    estimatedMonthlyVolume: '',
    hasPhysicalStore: false,
    websiteUrl: '',
    socialMediaLinks: '',
    bankAccountInfo: '',
    identityDocument: '',
    businessLicense: '',
    taxCertificate: '',
    agreedToTerms: false
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.agreedToTerms) {
      alert('Lütfen şartları ve koşulları kabul edin.');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch('/api/seller-applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        setSubmitted(true);
      } else {
        const error = await response.json();
        alert(error.message || 'Başvuru gönderilirken hata oluştu');
      }
    } catch (error) {
      console.error('Error submitting application:', error);
      alert('Başvuru gönderilirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Giriş Yapın</h1>
          <p className="text-muted-foreground mb-6">
            Satıcı başvurusu yapmak için giriş yapmalısınız.
          </p>
          <Button onClick={() => router.push('/sign-in')}>
            Giriş Yap
          </Button>
        </div>
      </div>
    );
  }

  if (submitted) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-2xl mx-auto text-center">
          <CheckCircle className="w-16 h-16 text-green-600 mx-auto mb-6" />
          <h1 className="text-3xl font-bold mb-4">Başvurunuz Alındı!</h1>
          <p className="text-gray-600 mb-6">
            Satıcı başvurunuz başarıyla gönderildi. Başvurunuz incelendikten sonra 
            size email ile bilgi verilecektir.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <Clock className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-blue-800">
                İnceleme süreci genellikle 2-3 iş günü sürmektedir.
              </span>
            </div>
          </div>
          <Button onClick={() => router.push('/dashboard')}>
            Dashboard'a Dön
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <Store className="w-16 h-16 text-blue-600 mx-auto mb-4" />
        <h1 className="text-3xl font-bold mb-2">Satıcı Ol</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Platformumuzda satıcı olarak ürünlerinizi satın ve gelir elde edin. 
          Başvuru formunu doldurun ve onay sürecini başlatın.
        </p>
      </div>

      {/* Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="text-center">
            <Store className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <CardTitle className="text-lg">Kolay Başlangıç</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 text-center">
              Basit başvuru formu ile hızlıca satıcı olun
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <CheckCircle className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <CardTitle className="text-lg">Hızlı Onay</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 text-center">
              2-3 iş günü içinde başvurunuz değerlendirilir
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <AlertCircle className="w-8 h-8 text-orange-600 mx-auto mb-2" />
            <CardTitle className="text-lg">Güvenli Platform</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 text-center">
              Güvenli ödeme sistemi ve müşteri desteği
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Application Form */}
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Satıcı Başvuru Formu</CardTitle>
          <CardDescription>
            Lütfen tüm alanları eksiksiz doldurun
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Business Type */}
            <div>
              <Label htmlFor="businessType">İşletme Türü *</Label>
              <Select value={formData.businessType} onValueChange={(value) => handleInputChange('businessType', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="İşletme türünü seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="individual">Bireysel</SelectItem>
                  <SelectItem value="company">Şirket</SelectItem>
                  <SelectItem value="cooperative">Kooperatif</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Business Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="businessName">İşletme/Şirket Adı *</Label>
                <Input
                  id="businessName"
                  value={formData.businessName}
                  onChange={(e) => handleInputChange('businessName', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="taxNumber">Vergi Numarası *</Label>
                <Input
                  id="taxNumber"
                  value={formData.taxNumber}
                  onChange={(e) => handleInputChange('taxNumber', e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="businessAddress">İşletme Adresi *</Label>
              <Textarea
                id="businessAddress"
                value={formData.businessAddress}
                onChange={(e) => handleInputChange('businessAddress', e.target.value)}
                required
              />
            </div>

            {/* Contact Info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactPerson">İletişim Kişisi *</Label>
                <Input
                  id="contactPerson"
                  value={formData.contactPerson}
                  onChange={(e) => handleInputChange('contactPerson', e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="contactPhone">Telefon *</Label>
                <Input
                  id="contactPhone"
                  value={formData.contactPhone}
                  onChange={(e) => handleInputChange('contactPhone', e.target.value)}
                  required
                />
              </div>
            </div>

            <div>
              <Label htmlFor="contactEmail">E-posta *</Label>
              <Input
                id="contactEmail"
                type="email"
                value={formData.contactEmail}
                onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                required
              />
            </div>

            {/* Business Details */}
            <div>
              <Label htmlFor="businessDescription">İşletme Açıklaması *</Label>
              <Textarea
                id="businessDescription"
                value={formData.businessDescription}
                onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                placeholder="İşletmeniz hakkında kısa bilgi verin"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="productCategories">Ürün Kategorileri *</Label>
                <Input
                  id="productCategories"
                  value={formData.productCategories}
                  onChange={(e) => handleInputChange('productCategories', e.target.value)}
                  placeholder="Elektronik, Giyim, Ev & Bahçe..."
                  required
                />
              </div>
              <div>
                <Label htmlFor="estimatedMonthlyVolume">Tahmini Aylık Satış Hacmi</Label>
                <Select value={formData.estimatedMonthlyVolume} onValueChange={(value) => handleInputChange('estimatedMonthlyVolume', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Seçin" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-10">0-10 ürün</SelectItem>
                    <SelectItem value="10-50">10-50 ürün</SelectItem>
                    <SelectItem value="50-100">50-100 ürün</SelectItem>
                    <SelectItem value="100+">100+ ürün</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Additional Info */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="hasPhysicalStore"
                checked={formData.hasPhysicalStore}
                onCheckedChange={(checked) => handleInputChange('hasPhysicalStore', checked as boolean)}
              />
              <Label htmlFor="hasPhysicalStore">Fiziksel mağazam var</Label>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="websiteUrl">Website URL</Label>
                <Input
                  id="websiteUrl"
                  value={formData.websiteUrl}
                  onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
                  placeholder="https://..."
                />
              </div>
              <div>
                <Label htmlFor="socialMediaLinks">Sosyal Medya Hesapları</Label>
                <Input
                  id="socialMediaLinks"
                  value={formData.socialMediaLinks}
                  onChange={(e) => handleInputChange('socialMediaLinks', e.target.value)}
                  placeholder="Instagram, Facebook, Twitter..."
                />
              </div>
            </div>

            {/* Terms */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="agreedToTerms"
                checked={formData.agreedToTerms}
                onCheckedChange={(checked) => handleInputChange('agreedToTerms', checked as boolean)}
              />
              <Label htmlFor="agreedToTerms">
                Satıcı şartları ve koşullarını kabul ediyorum *
              </Label>
            </div>

            {/* Submit */}
            <div className="flex items-center justify-end space-x-4">
              <Button type="button" variant="outline" onClick={() => router.back()}>
                İptal
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? 'Gönderiliyor...' : 'Başvuru Gönder'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
