'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { CheckCircle, Package, ArrowRight } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useCartStore } from '@/store/cart-store';

export default function CheckoutSuccessPage() {
  const searchParams = useSearchParams();
  const sessionId = searchParams.get('session_id');
  const [orderNumber, setOrderNumber] = useState<string>('');
  const [isClient, setIsClient] = useState(false);
  const clearCart = useCartStore((state) => state.clearCart);

  // Generate order number only on client side to avoid hydration mismatch
  useEffect(() => {
    setIsClient(true);
    if (!orderNumber) {
      setOrderNumber(Math.random().toString(36).substr(2, 9).toUpperCase());
    }
  }, [orderNumber]);

  useEffect(() => {
    // Clear cart after successful payment
    if (sessionId) {
      clearCart();
      // In a real app, you might also want to:
      // - Fetch order details from your API
      // - Send analytics event
      // - Show order confirmation
    }
  }, [sessionId, clearCart]);

  if (!sessionId) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-md mx-auto text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Payment Error</h1>
          <p className="text-muted-foreground mb-6">
            No payment session found. Please try again.
          </p>
          <Link href="/cart">
            <Button>Return to Cart</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-16">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-green-600 mb-2">Payment Successful!</h1>
          <p className="text-muted-foreground">
            Thank you for your purchase. Your order has been confirmed.
          </p>
        </div>

        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Package className="h-5 w-5 mr-2" />
              Order Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Order Number</p>
                <p className="font-semibold">
                  {isClient ? `#${orderNumber}` : '#Loading...'}
                </p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Payment Status</p>
                <p className="font-semibold text-green-600">Paid</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Session ID</p>
                <p className="font-mono text-xs">{sessionId}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Estimated Delivery</p>
                <p className="font-semibold">3-5 business days</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-blue-900 mb-2">What's Next?</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• You'll receive an email confirmation shortly</li>
            <li>• We'll send you tracking information once your order ships</li>
            <li>• You can track your order status in your dashboard</li>
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/dashboard">
            <Button variant="outline" className="w-full sm:w-auto">
              View Dashboard
            </Button>
          </Link>
          <Link href="/products">
            <Button className="w-full sm:w-auto">
              Continue Shopping
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
