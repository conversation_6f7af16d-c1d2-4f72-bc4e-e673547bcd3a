"use client";

import { useState } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export default function MakeAdminPage() {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [targetClerkId, setTargetClerkId] = useState('');
  const [action, setAction] = useState('make_admin');

  const makeAdmin = async (clerkId: string) => {
    try {
      setLoading(true);
      setMessage('');

      const response = await fetch('/api/admin/mlm/make-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetClerkId: clerkId
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ ${data.message}`);
      } else {
        setMessage(`❌ ${data.message}`);
      }
    } catch (error) {
      setMessage('❌ Hata oluştu');
      console.error('Make admin error:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateUserPrivileges = async (clerkId: string, actionType: string) => {
    try {
      setLoading(true);
      setMessage('');

      const response = await fetch('/api/admin/make-seller', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetClerkId: clerkId,
          action: actionType
        }),
      });

      const data = await response.json();

      if (data.success) {
        setMessage(`✅ ${data.message}`);
      } else {
        setMessage(`❌ ${data.message}`);
      }
    } catch (error) {
      setMessage('❌ Hata oluştu');
      console.error('Update privileges error:', error);
    } finally {
      setLoading(false);
    }
  };

  const makeSelfAdmin = () => {
    if (user?.id) {
      makeAdmin(user.id);
    }
  };

  const makeTargetAdmin = () => {
    if (targetClerkId.trim()) {
      if (action === 'make_admin') {
        makeAdmin(targetClerkId.trim());
      } else {
        updateUserPrivileges(targetClerkId.trim(), action);
      }
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle>Kullanıcı Yetkileri</CardTitle>
          <CardDescription>
            Development ortamında kullanıcı yetkilerini yönetmek için
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {user && (
            <div>
              <p className="text-sm text-gray-600 mb-2">
                Mevcut kullanıcı: {user.fullName || user.emailAddresses[0]?.emailAddress}
              </p>
              <p className="text-xs text-gray-500 mb-4">
                Clerk ID: {user.id}
              </p>
              <Button 
                onClick={makeSelfAdmin} 
                disabled={loading}
                className="w-full mb-4"
              >
                {loading ? 'İşleniyor...' : 'Kendimi Admin Yap'}
              </Button>
            </div>
          )}

          <div className="border-t pt-4">
            <Label htmlFor="targetClerkId">Kullanıcı Yetkileri</Label>
            <Input
              id="targetClerkId"
              value={targetClerkId}
              onChange={(e) => setTargetClerkId(e.target.value)}
              placeholder="Clerk User ID"
              className="mb-2"
            />

            <Label htmlFor="action">İşlem</Label>
            <select
              id="action"
              value={action}
              onChange={(e) => setAction(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md mb-2"
            >
              <option value="make_admin">Admin Yap</option>
              <option value="make_seller">Satıcı Yap</option>
              <option value="remove_seller">Satıcı Yetkisini Kaldır</option>
              <option value="approve_bidding">Teklif Verme Onayı</option>
              <option value="remove_bidding">Teklif Verme Onayını Kaldır</option>
            </select>

            <Button
              onClick={makeTargetAdmin}
              disabled={loading || !targetClerkId.trim()}
              variant="outline"
              className="w-full"
            >
              {loading ? 'İşleniyor...' : 'Uygula'}
            </Button>
          </div>

          {message && (
            <div className="p-3 bg-gray-100 rounded text-sm">
              {message}
            </div>
          )}

          <div className="text-xs text-gray-500 mt-4">
            <p>⚠️ Bu sayfa sadece development ortamında kullanılmalıdır.</p>
            <p>Admin olduktan sonra <a href="/admin/mlm" className="text-blue-600 hover:underline">/admin/mlm</a> sayfasına erişebilirsiniz.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
