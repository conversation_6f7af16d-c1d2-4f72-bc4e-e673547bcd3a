import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { client } from '@/sanity/lib/client';
import AuctionDetail from '@/components/auctions/AuctionDetail';

interface AuctionPageProps {
  params: Promise<{
    auctionId: string;
  }>;
}

async function getAuction(auctionId: string) {
  try {
    const auction = await client.fetch(
      `*[_type == "auction" && (id == $auctionId || _id == $auctionId)][0]{
        _id,
        id,
        name,
        description,
        status
      }`,
      { auctionId }
    );
    return auction;
  } catch (error) {
    console.error('Error fetching auction:', error);
    return null;
  }
}

export async function generateMetadata({ params }: AuctionPageProps): Promise<Metadata> {
  const { auctionId } = await params;
  const auction = await getAuction(auctionId);

  if (!auction) {
    return {
      title: 'Açık Artırma Bulunamadı | E-Ticaret',
      description: 'Aradığınız açık artırma bulunamadı.',
    };
  }

  return {
    title: `${auction.name} - Açık Artırma | E-Ticaret`,
    description: auction.description || `${auction.name} için açık artırma`,
  };
}

export default async function AuctionPage({ params }: AuctionPageProps) {
  const { auctionId } = await params;
  const auction = await getAuction(auctionId);

  if (!auction) {
    notFound();
  }

  return <AuctionDetail auctionId={auctionId} />;
}
