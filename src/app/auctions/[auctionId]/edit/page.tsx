import { Metadata } from 'next';
import { auth } from '@clerk/nextjs/server';
import { redirect, notFound } from 'next/navigation';
import { client } from '@/sanity/lib/client';
import EditAuction from '@/components/auctions/EditAuction';

interface EditAuctionPageProps {
  params: Promise<{
    auctionId: string;
  }>;
}

async function getAuction(auctionId: string, userId: string) {
  try {
    // Get user first to check permissions
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return null;
    }

    // Get auction with full details
    const auction = await client.fetch(
      `*[_type == "auction" && (id == $auctionId || _id == $auctionId)][0]{
        _id,
        id,
        name,
        description,
        image{
          asset->{
            _id,
            url
          }
        },
        images[]{
          asset->{
            _id,
            url
          }
        },
        currentBid,
        startingBid,
        bidIncrementAmount,
        reservePrice,
        buyNowPrice,
        startTime,
        endTime,
        status,
        category,
        condition,
        featured,
        autoExtend,
        extendMinutes,
        seller->{
          _id,
          clerkId
        },
        shippingInfo,
        tags,
        notes,
        _createdAt,
        _updatedAt
      }`,
      { auctionId }
    );

    if (!auction) {
      return null;
    }

    // Check if user can edit this auction (seller or admin)
    const canEdit = user.isAdmin || auction.seller?.clerkId === userId;
    
    if (!canEdit) {
      return null;
    }

    return auction;
  } catch (error) {
    console.error('Error fetching auction for edit:', error);
    return null;
  }
}

export async function generateMetadata({ params }: EditAuctionPageProps): Promise<Metadata> {
  const { auctionId } = await params;
  
  return {
    title: `Açık Artırma Düzenle | E-Ticaret`,
    description: 'Açık artırma bilgilerini düzenleyin',
  };
}

export default async function EditAuctionPage({ params }: EditAuctionPageProps) {
  const { userId } = await auth();
  const { auctionId } = await params;

  if (!userId) {
    redirect('/sign-in');
  }

  const auction = await getAuction(auctionId, userId);

  if (!auction) {
    notFound();
  }

  // Check if auction can be edited (not ended, not cancelled)
  if (auction.status === 'ended' || auction.status === 'cancelled') {
    redirect(`/auctions/${auctionId}?error=cannot-edit`);
  }

  return <EditAuction auction={auction} />;
}
