import { Metadata } from 'next';
import { auth } from '@clerk/nextjs/server';
import { redirect } from 'next/navigation';
import { client } from '@/sanity/lib/client';
import CreateAuction from '@/components/auctions/CreateAuction';

export const metadata: Metadata = {
  title: 'Açık Artırma Oluştur | E-Ticaret',
  description: 'Yeni açık artırma oluşturun ve ürününüzü satışa çıkarın',
};

async function checkAdminAccess(userId: string) {
  try {
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]{ isAdmin }`,
      { clerkId: userId }
    );

    return user?.isAdmin === true;
  } catch (error) {
    console.error('Admin access check error:', error);
    return false;
  }
}

export default async function CreateAuctionPage() {
  const { userId } = await auth();

  if (!userId) {
    redirect('/sign-in');
  }

  // Admin yetkisi kont<PERSON>
  const canCreateAuction = await checkAdminAccess(userId);

  if (!canCreateAuction) {
    redirect('/auctions?error=unauthorized');
  }

  return <CreateAuction />;
}
