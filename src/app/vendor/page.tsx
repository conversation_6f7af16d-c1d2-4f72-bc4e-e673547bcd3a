'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import Link from 'next/link';
import { 
  Package, 
  DollarSign, 
  TrendingUp, 
  Users,
  Plus,
  BarChart3,
  Settings,
  Eye,
  Edit,
  ArrowRight
} from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function VendorDashboard() {
  const { user, isLoaded } = useUser();
  const [vendorStats, setVendorStats] = useState({
    totalProducts: 0,
    totalSales: 0,
    totalRevenue: 0,
    activeOrders: 0,
    rating: 0,
    reviews: 0
  });
  const [recentProducts, setRecentProducts] = useState([]);
  const [isVendor, setIsVendor] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkVendorAccess = async () => {
      if (!isLoaded || !user) {
        setLoading(false);
        return;
      }

      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const data = await response.json();
          const userIsVendor = data.user?.isSeller || data.user?.isAdmin;
          setIsVendor(userIsVendor);

          if (userIsVendor) {
            // Fetch vendor stats
            setVendorStats({
              totalProducts: 15,
              totalSales: 127,
              totalRevenue: 12450.75,
              activeOrders: 8,
              rating: 4.7,
              reviews: 89
            });
          }
        }
      } catch (error) {
        console.error('Error checking vendor access:', error);
      } finally {
        setLoading(false);
      }
    };

    checkVendorAccess();
  }, [isLoaded, user]);

  if (!isLoaded || loading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Giriş Yapın</h1>
          <p className="text-muted-foreground mb-6">
            Satıcı dashboard'a erişmek için giriş yapmalısınız.
          </p>
          <Link href="/sign-in">
            <Button>Giriş Yap</Button>
          </Link>
        </div>
      </div>
    );
  }

  if (!isVendor) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Yetkisiz Erişim</h1>
          <p className="text-muted-foreground mb-6">
            Bu sayfaya erişmek için satıcı yetkisine sahip olmanız gerekiyor.
          </p>
          <Link href="/dashboard">
            <Button>Dashboard'a Dön</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold">Satıcı Dashboard</h1>
          <p className="text-muted-foreground">
            Hoş geldiniz, {user.firstName || 'Satıcı'}! Ürünlerinizi ve satışlarınızı yönetin.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Link href="/vendor/products/add">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Ürün Ekle
            </Button>
          </Link>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Ayarlar
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Products</p>
                <p className="text-2xl font-bold">{vendorStats.totalProducts}</p>
              </div>
              <Package className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Sales</p>
                <p className="text-2xl font-bold">{vendorStats.totalSales}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                <p className="text-2xl font-bold">${vendorStats.totalRevenue.toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Orders</p>
                <p className="text-2xl font-bold">{vendorStats.activeOrders}</p>
              </div>
              <Users className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Performance Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2" />
                  Performance Overview
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Store Rating</span>
                  <div className="flex items-center space-x-1">
                    <span className="font-bold">{vendorStats.rating}</span>
                    <span className="text-yellow-500">★</span>
                    <span className="text-sm text-muted-foreground">({vendorStats.reviews} reviews)</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Conversion Rate</span>
                  <span className="font-bold text-green-600">12.5%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Order Value</span>
                  <span className="font-bold">${(vendorStats.totalRevenue / vendorStats.totalSales).toFixed(2)}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Return Rate</span>
                  <span className="font-bold text-red-600">2.1%</span>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/vendor/products/add">
                  <Button variant="outline" className="w-full justify-start">
                    <Plus className="h-4 w-4 mr-2" />
                    Yeni Ürün Ekle
                    <ArrowRight className="h-4 w-4 ml-auto" />
                  </Button>
                </Link>
                <Button variant="outline" className="w-full justify-start">
                  <Eye className="h-4 w-4 mr-2" />
                  Mağaza Vitrinini Görüntüle
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Edit className="h-4 w-4 mr-2" />
                  Mağaza Profilini Düzenle
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Analitikleri Görüntüle
                  <ArrowRight className="h-4 w-4 ml-auto" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="products">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Ürünleriniz</CardTitle>
              <Link href="/vendor/products/add">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Ürün Ekle
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Ürünlerinizi Yönetin</h3>
                <p className="text-muted-foreground mb-4">
                  Ürün kataloğunuzu ekleyin, düzenleyin ve yönetin.
                </p>
                <Link href="/vendor/products/add">
                  <Button>Yeni Ürün Ekle</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Recent Orders</h3>
                <p className="text-muted-foreground mb-4">
                  Orders from customers will appear here when they purchase your products.
                </p>
                <Link href="/products">
                  <Button variant="outline">View Your Products</Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Sales Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Analytics Coming Soon</h3>
                <p className="text-muted-foreground mb-4">
                  Detailed sales analytics and insights will be available here.
                </p>
                <Badge variant="secondary">Feature in Development</Badge>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
