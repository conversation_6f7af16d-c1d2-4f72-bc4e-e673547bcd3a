'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import WalletManagement from '@/components/wallet/WalletManagement';
import {
  ShoppingBag,
  Heart,
  User,
  CreditCard,
  Package,
  Settings,
  Users,
  TrendingUp,
  Wallet,
  Gift,
  Award,
  DollarSign
} from 'lucide-react';
import Link from 'next/link';

interface UserData {
  _id: string;
  name: string;
  email: string;
  isAdmin: boolean;
  isAdminApproved: boolean;
  isSeller: boolean;
  walletBalance: number;
  giftVoucherBalance: number;
  mlmLevel: string;
  directReferrals: number;
  totalTeamMembers: number;
  totalCommissionEarned: number;
  monthlyCommissionEarned: number;
  totalSales: number;
  membershipStartDate: string;
}

export default function DashboardPage() {
  const { user, isLoaded } = useUser();
  const router = useRouter();
  const [userData, setUserData] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      if (!isLoaded) return;

      if (!user) {
        router.push('/sign-in');
        return;
      }

      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const data = await response.json();
          setUserData(data.user);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [user, isLoaded, router]);

  if (!isLoaded || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">
          Hoş geldiniz, {userData?.name || user.firstName || user.emailAddresses[0].emailAddress}!
        </h1>
        <p className="text-muted-foreground">
          Hesabınızı yönetin ve siparişlerinizi takip edin
        </p>
      </div>

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-5">
          <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
          <TabsTrigger value="orders">Siparişler</TabsTrigger>
          <TabsTrigger value="wallet">Cüzdan</TabsTrigger>
          <TabsTrigger value="mlm">MLM Dashboard</TabsTrigger>
          <TabsTrigger value="settings">Ayarlar</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {/* Quick Stats */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Toplam Sipariş</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">
                  +2 geçen aydan
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Favori Ürünler</CardTitle>
                <Heart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8</div>
                <p className="text-xs text-muted-foreground">
                  Kaydedilen ürünler
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Cüzdan Bakiyesi</CardTitle>
                <Wallet className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(userData?.walletBalance || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Mevcut bakiye
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Hediye Çeki</CardTitle>
                <Gift className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(userData?.giftVoucherBalance || 0)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Hediye çeki bakiyesi
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Orders */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  Son Siparişler
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Sipariş #12345</p>
                    <p className="text-sm text-muted-foreground">2 ürün • ₺299.98</p>
                  </div>
                  <Badge variant="secondary">Teslim Edildi</Badge>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Sipariş #12344</p>
                    <p className="text-sm text-muted-foreground">1 ürün • ₺79.99</p>
                  </div>
                  <Badge variant="outline">Kargoda</Badge>
                </div>
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Sipariş #12343</p>
                    <p className="text-sm text-muted-foreground">3 ürün • ₺199.97</p>
                  </div>
                  <Badge>Hazırlanıyor</Badge>
                </div>
                <Link href="/orders">
                  <Button variant="outline" className="w-full">
                    Tüm Siparişleri Görüntüle
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Settings className="h-5 w-5 mr-2" />
                  Hızlı İşlemler
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/profile">
                  <Button variant="outline" className="w-full justify-start">
                    <User className="h-4 w-4 mr-2" />
                    Profili Düzenle
                  </Button>
                </Link>
                <Link href="/orders">
                  <Button variant="outline" className="w-full justify-start">
                    <Package className="h-4 w-4 mr-2" />
                    Siparişleri Takip Et
                  </Button>
                </Link>
                <Link href="/wishlist">
                  <Button variant="outline" className="w-full justify-start">
                    <Heart className="h-4 w-4 mr-2" />
                    Favori Listesi
                  </Button>
                </Link>
                {userData?.isSeller && (
                  <Link href="/vendor">
                    <Button variant="outline" className="w-full justify-start">
                      <Package className="h-4 w-4 mr-2" />
                      Satıcı Paneli
                    </Button>
                  </Link>
                )}
                <Link href="/products">
                  <Button className="w-full justify-start">
                    <ShoppingBag className="h-4 w-4 mr-2" />
                    Alışverişe Devam Et
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Siparişlerim</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Sipariş geçmişiniz burada görüntülenecek.</p>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Wallet Tab */}
        <TabsContent value="wallet">
          <WalletManagement />
        </TabsContent>

        {/* MLM Tab */}
        <TabsContent value="mlm" className="space-y-6">
          {userData?.isAdminApproved ? (
            <>
              {/* MLM Stats */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">MLM Seviyesi</CardTitle>
                    <Award className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold capitalize">
                      {userData.mlmLevel || 'Bronze'}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Mevcut seviye
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Direkt Referanslar</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {userData.directReferrals || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Davet ettiğiniz kişiler
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Toplam Takım</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {userData.totalTeamMembers || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Tüm takım üyeleri
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Toplam Komisyon</CardTitle>
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatCurrency(userData.totalCommissionEarned || 0)}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Kazandığınız komisyon
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* MLM Actions */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card>
                  <CardHeader>
                    <CardTitle>MLM İstatistikleri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex justify-between">
                      <span>Aylık Komisyon:</span>
                      <span className="font-semibold">
                        {formatCurrency(userData.monthlyCommissionEarned || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Toplam Satış:</span>
                      <span className="font-semibold">
                        {formatCurrency(userData.totalSales || 0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Üyelik Tarihi:</span>
                      <span className="font-semibold">
                        {userData.membershipStartDate ?
                          new Date(userData.membershipStartDate).toLocaleDateString('tr-TR') :
                          'Belirtilmemiş'
                        }
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Hızlı İşlemler</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Button variant="outline" className="w-full justify-start">
                      <Users className="h-4 w-4 mr-2" />
                      Takımımı Görüntüle
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <DollarSign className="h-4 w-4 mr-2" />
                      Komisyon Geçmişi
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      Performans Raporu
                    </Button>
                    <Button className="w-full justify-start">
                      <Award className="h-4 w-4 mr-2" />
                      Referans Linki Paylaş
                    </Button>
                  </CardContent>
                </Card>
              </div>
            </>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <Award className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-semibold text-gray-700 mb-2">
                  MLM Sistemi
                </h3>
                <p className="text-gray-600 mb-4">
                  MLM sistemine katılmak için admin onayı bekleniyor.
                </p>
                <Badge variant="outline">Onay Bekleniyor</Badge>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Hesap Ayarları</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Hesap ayarları burada olacak.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
