'use client';

import { useState, useMemo, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Filter, Grid, List, SlidersHorizontal, X, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import ProductCard from '@/components/product/ProductCard';
import ProductFiltersComponent from '@/components/product/ProductFilters';
import { sampleProducts, categories, brands } from '@/data/products';
import { ProductFilters, SanityProduct } from '@/types';
import { useSearchStore } from '@/store/search-store';

export default function ProductsPage() {
  const searchParams = useSearchParams();
  const searchQuery = searchParams.get('search') || '';

  const { searchQuery: storeSearchQuery, filters: storeFilters, setSearchQuery, setFilters } = useSearchStore();

  const [filters, setLocalFilters] = useState<ProductFilters>({
    category: 'All',
    brand: 'All',
    sortBy: 'newest'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [products, setProducts] = useState<SanityProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch products from Sanity API
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        setError(null);

        const params = new URLSearchParams();
        if (searchQuery || storeSearchQuery) {
          params.append('search', searchQuery || storeSearchQuery);
        }
        if (filters.category && filters.category !== 'All') {
          params.append('category', filters.category);
        }
        if (filters.brand && filters.brand !== 'All') {
          params.append('brand', filters.brand);
        }

        const response = await fetch(`/api/products?${params.toString()}`);
        const result = await response.json();

        if (result.success) {
          setProducts(result.data || []);
          console.log(`Loaded ${result.data?.length || 0} products from Sanity`);
        } else {
          setError(result.error || 'Failed to fetch products');
          console.error('Products API error:', result.error);
        }
      } catch (err) {
        setError('Failed to fetch products');
        console.error('Fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [searchQuery, storeSearchQuery, filters.category, filters.brand]);

  // Sync URL search with store
  useEffect(() => {
    if (searchQuery && searchQuery !== storeSearchQuery) {
      setSearchQuery(searchQuery);
    }
  }, [searchQuery, storeSearchQuery, setSearchQuery]);

  const filteredProducts = useMemo(() => {
    let filtered = [...products];

    // Search is already handled by API, so we don't need to filter again for search
    // But we can apply additional client-side filters if needed

    // Apply price filters (client-side)
    if (filters.minPrice !== undefined) {
      filtered = filtered.filter(product => product.price >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      filtered = filtered.filter(product => product.price <= filters.maxPrice!);
    }

    // Apply stock filter (client-side)
    if (filters.inStock) {
      filtered = filtered.filter(product => product.stock > 0);
    }

    // Apply sorting
    switch (filters.sortBy) {
      case 'price-asc':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-desc':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
      default:
        filtered.sort((a, b) => new Date(b._createdAt).getTime() - new Date(a._createdAt).getTime());
        break;
    }

    return filtered;
  }, [products, filters]);

  const updateFilter = (newFilters: Partial<ProductFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }));
  };

  const updateSingleFilter = (key: keyof ProductFilters, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setLocalFilters({
      category: 'All',
      brand: 'All',
      sortBy: 'newest',
      minPrice: undefined,
      maxPrice: undefined,
      inStock: undefined,
      onSale: undefined,
      rating: undefined
    });
  };

  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'sortBy') return false; // sortBy is not considered an active filter
    if (value === undefined || value === 'All' || value === 'newest') return false;
    if (key === 'inStock' || key === 'onSale') return value === true;
    return true;
  }).length;

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        {searchQuery || storeSearchQuery ? (
          <div>
            <h1 className="text-3xl font-bold mb-2">
              Search Results
              {(searchQuery || storeSearchQuery) && (
                <span className="text-primary"> for "{searchQuery || storeSearchQuery}"</span>
              )}
            </h1>
            <div className="flex items-center gap-2 mb-2">
              <p className="text-muted-foreground">
                {filteredProducts.length} {filteredProducts.length === 1 ? 'product' : 'products'} found
              </p>
              {(searchQuery || storeSearchQuery) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchQuery('');
                    window.history.pushState({}, '', '/products');
                  }}
                  className="h-auto p-1"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear search
                </Button>
              )}
            </div>
          </div>
        ) : (
          <div>
            <h1 className="text-3xl font-bold mb-2">All Products</h1>
            <p className="text-muted-foreground">
              {loading ? 'Loading products...' : `Discover our complete collection of ${products.length} amazing products from Sanity CMS`}
            </p>
          </div>
        )}
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className={`lg:w-64 ${showFilters ? 'block' : 'hidden lg:block'}`}>
          <ProductFiltersComponent
            filters={filters}
            onFiltersChange={updateFilter}
            onClearFilters={clearFilters}
          />
        </div>

        {/* Products Grid */}
        <div className="flex-1">
          {/* Toolbar */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="lg:hidden"
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                Filters
                {activeFiltersCount > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {activeFiltersCount}
                  </Badge>
                )}
              </Button>
              <span className="text-sm text-muted-foreground">
                {filteredProducts.length} products found
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Select value={filters.sortBy} onValueChange={(value: any) => updateSingleFilter('sortBy', value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest</SelectItem>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="price-asc">Price: Low to High</SelectItem>
                  <SelectItem value="price-desc">Price: High to Low</SelectItem>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Products Grid */}
          {loading ? (
            <div className="flex items-center justify-center py-16">
              <Loader2 className="h-8 w-8 animate-spin mr-2" />
              <span>Loading products from Sanity CMS...</span>
            </div>
          ) : error ? (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="h-12 w-12 text-red-500" />
              </div>
              <h3 className="text-xl font-semibold mb-2">Error loading products</h3>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>Try Again</Button>
            </div>
          ) : filteredProducts.length > 0 ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredProducts.map((product) => (
                <ProductCard key={product._id} product={{
                  id: product._id,
                  name: product.name,
                  description: product.description || '',
                  price: product.price,
                  image: product.imageUrl || '/placeholder-product.jpg',
                  images: product.images || [product.imageUrl || '/placeholder-product.jpg'],
                  category: product.category,
                  brand: product.vendor?.name || 'Unknown',
                  rating: 4.5, // Default rating since Sanity doesn't have this yet
                  reviews: 0, // Default reviews
                  stock: product.stock,
                  isOnSale: false, // Default
                  originalPrice: product.price,
                  tags: [],
                  createdAt: product._createdAt
                }} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                <Filter className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No products found</h3>
              <p className="text-muted-foreground mb-4">
                {products.length === 0 ? 'No products available in Sanity CMS yet.' : 'Try adjusting your filters to see more results.'}
              </p>
              <Button onClick={clearFilters}>Clear All Filters</Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
