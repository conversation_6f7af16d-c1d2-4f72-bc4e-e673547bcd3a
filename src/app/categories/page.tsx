'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ArrowRight, Package, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface CategoryData {
  name: string;
  count: number;
  products: Array<{
    _id: string;
    name: string;
    price: number;
    imageUrl?: string;
  }>;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/categories');
        const result = await response.json();
        
        if (result.success && result.data) {
          // Filter out categories with 0 products
          const activeCategories = result.data.filter((cat: CategoryData) => cat.count > 0);
          setCategories(activeCategories);
        } else {
          setError(result.error || 'Failed to fetch categories');
        }
      } catch (err) {
        setError('Failed to fetch categories');
        console.error('Categories fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  const getCategoryDisplayName = (categoryName: string) => {
    const displayNames: Record<string, string> = {
      'electronics': 'Electronics',
      'clothing': 'Clothing & Fashion',
      'home-garden': 'Home & Garden',
      'sports': 'Sports & Fitness',
      'books': 'Books & Education',
      'automotive': 'Automotive',
      'health-beauty': 'Health & Beauty',
      'other': 'Other'
    };
    return displayNames[categoryName] || categoryName.charAt(0).toUpperCase() + categoryName.slice(1);
  };

  const getCategoryIcon = (categoryName: string) => {
    const icons: Record<string, string> = {
      'electronics': '📱',
      'clothing': '👕',
      'home-garden': '🏠',
      'sports': '⚽',
      'books': '📚',
      'automotive': '🚗',
      'health-beauty': '💄',
      'other': '📦'
    };
    return icons[categoryName] || '📦';
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="flex items-center justify-center">
          <Loader2 className="h-8 w-8 animate-spin mr-2" />
          <span>Loading categories from Sanity CMS...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-2">Error Loading Categories</h1>
          <p className="text-muted-foreground mb-6">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Shop by Category</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover products across {categories.length} categories with {categories.reduce((total, cat) => total + cat.count, 0)} total products from Sanity CMS
        </p>
      </div>

      {/* Categories Grid */}
      {categories.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category) => (
            <Card key={category.name} className="group hover:shadow-lg transition-shadow duration-300">
              <CardHeader className="text-center">
                <div className="text-4xl mb-2">{getCategoryIcon(category.name)}</div>
                <CardTitle className="text-xl">{getCategoryDisplayName(category.name)}</CardTitle>
                <Badge variant="secondary" className="w-fit mx-auto">
                  {category.count} products
                </Badge>
              </CardHeader>
              
              <CardContent>
                {/* Sample Products */}
                {category.products.length > 0 && (
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {category.products.slice(0, 3).map((product) => (
                      <div key={product._id} className="aspect-square rounded-lg overflow-hidden bg-muted">
                        <Image
                          src={product.imageUrl || '/placeholder-product.jpg'}
                          alt={product.name}
                          width={100}
                          height={100}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Sample Product Names */}
                <div className="space-y-1 mb-4">
                  {category.products.slice(0, 2).map((product) => (
                    <p key={product._id} className="text-sm text-muted-foreground truncate">
                      {product.name} - ${product.price}
                    </p>
                  ))}
                  {category.products.length > 2 && (
                    <p className="text-xs text-muted-foreground">
                      +{category.products.length - 2} more products
                    </p>
                  )}
                </div>

                <Link href={`/products?category=${category.name}`}>
                  <Button className="w-full group-hover:bg-primary/90">
                    Browse {getCategoryDisplayName(category.name)}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-16">
          <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h2 className="text-2xl font-bold mb-2">No Categories Available</h2>
          <p className="text-muted-foreground mb-6">
            No product categories found. Add some products in Sanity Studio to see categories here.
          </p>
          <Link href="/studio">
            <Button>
              Open Sanity Studio
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
