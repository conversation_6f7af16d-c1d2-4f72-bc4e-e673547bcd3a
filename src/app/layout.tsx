import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { <PERSON><PERSON><PERSON><PERSON> } from '@clerk/nextjs';
import { Toaster } from "@/components/ui/sonner";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";
import PerformanceMonitor from "@/components/performance/PerformanceMonitor";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Shoptech - Modern E-commerce Platform",
  description: "Discover amazing products with our cutting-edge e-commerce platform built with Next.js 15 and React 19",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${inter.variable} font-sans antialiased`}>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        <Toaster />
        <PerformanceMonitor />
        </body>
      </html>
    </ClerkProvider>
  );
}
