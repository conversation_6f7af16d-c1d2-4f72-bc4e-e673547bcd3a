import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, Star, Truck, Shield, Headphones } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import ProductCard from '@/components/product/ProductCard';
import { cdnClient } from '@/lib/sanity';
import { SanityProduct } from '@/types';
import { Metadata } from 'next';

// SEO Metadata for better search engine optimization
export const metadata: Metadata = {
  title: 'Shoptech - Modern E-commerce Platform',
  description: 'Discover amazing products with our cutting-edge e-commerce platform built with Next.js 15 and React 19. Fast, secure, and user-friendly shopping experience.',
  keywords: 'e-commerce, shopping, products, online store, electronics, fashion, home garden, automotive',
  authors: [{ name: 'Shoptech Team' }],
  openGraph: {
    title: 'Shoptech - Modern E-commerce Platform',
    description: 'Experience the future of online shopping with lightning-fast performance',
    type: 'website',
    url: 'https://shoptech.com',
    siteName: 'Shoptech',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Shoptech E-commerce Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Shoptech - Modern E-commerce Platform',
    description: 'Experience the future of online shopping with lightning-fast performance',
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

async function getFeaturedProducts(): Promise<SanityProduct[]> {
  try {
    const query = `*[_type == "product" && status == "active"] | order(_createdAt desc)[0...6]{
      _id,
      name,
      description,
      price,
      stock,
      category,
      status,
      "imageUrl": images[0].asset->url,
      "images": images[].asset->url,
      "vendor": vendor->{_id, name, userId},
      _createdAt
    }`;

    const products = await cdnClient.fetch(query);
    console.log(`Fetched ${products.length} featured products from Sanity`);
    return products;
  } catch (error) {
    console.error('Error fetching featured products:', error);
    return [];
  }
}

export default async function Home() {
  const featuredProducts = await getFeaturedProducts();

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-primary/10 via-primary/5 to-background">
        <div className="container mx-auto px-2 py-16">
          <div className="max-w-1xl">
            <Badge variant="secondary" className="mb-4">
              🚀 New Collection Available
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6">
              Discover Amazing Products with{' '}
              <span className="text-primary">Shoptech</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl">
              Experience the future of online shopping with our cutting-edge platform.
              Built with Next.js 15 and React 19 for lightning-fast performance.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link href="/products">
                <Button size="lg" className="w-full sm:w-auto">
                  Shop Now
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Link href="/deals">
                <Button variant="outline" size="lg" className="w-full sm:w-auto">
                  View Hot Deals
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>
      {/* Featured Products Section */}
      <section className="container mx-auto px-4">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold mb-2">Featured Products</h2>
            <p className="text-muted-foreground">
              {featuredProducts.length > 0
                ? `Discover our most popular items (${featuredProducts.length} products from Sanity CMS)`
                : 'Loading products from Sanity CMS...'
              }
            </p>
          </div>
          <Link href="/products">
            <Button variant="outline">
              View All Products
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
        {featuredProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {featuredProducts.map((product) => (
              <ProductCard key={product._id} product={{
                id: product._id,
                name: product.name,
                description: product.description || '',
                price: product.price,
                images: product.images || [product.imageUrl || '/placeholder-product.jpg'],
                category: product.category,
                brand: product.vendor?.name || 'Unknown',
                rating: 4.5, // Default rating
                reviews: 0, // Default reviews
                stock: product.stock,
                isOnSale: false, // Default
                originalPrice: product.price,
                tags: [],
                createdAt: new Date(product._createdAt),
                updatedAt: new Date(product._createdAt)
              }} />
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <p className="text-muted-foreground">No products available yet. Add some products in Sanity Studio!</p>
            <Link href="/studio">
              <Button className="mt-4">
                Open Sanity Studio
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        )}
      </section>
            {/* Features Section */}
      <section className="relative bg-gradient-to-r from-primary/10 via-primary/5 to-background">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Truck className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">Free Shipping</h3>
            <p className="text-muted-foreground">
              Free shipping on orders over $50. Fast and reliable delivery worldwide.
            </p>
          </div>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Shield className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">Secure Payment</h3>
            <p className="text-muted-foreground">
              Your payment information is processed securely with Stripe integration.
            </p>
          </div>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
              <Headphones className="h-8 w-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold">24/7 Support</h3>
            <p className="text-muted-foreground">
              Our customer support team is here to help you anytime, anywhere.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
