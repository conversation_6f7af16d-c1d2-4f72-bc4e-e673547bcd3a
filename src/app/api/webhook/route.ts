import { NextRequest, NextResponse } from "next/server";
import <PERSON><PERSON> from "stripe";
import { client } from "@/sanity/lib/client";

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function POST(req: NextRequest) {
    const body = await req.text();
    const sig = req.headers.get("stripe-signature") as string;
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

    let event: Stripe.Event;

    try {
        event = stripe.webhooks.constructEvent(body, sig, webhookSecret);
    } catch (err) {
        console.error("Webhook signature verification failed.", err);
        return NextResponse.json(
            { error: "Webhook signature verification failed." },
            { status: 400 }
        );
    }

    if (event.type === "checkout.session.completed") {
        const session = event.data.object as Stripe.Checkout.Session;

        try {
            // Check if this is a wallet top-up or regular order
            if (session.metadata?.type === 'wallet_topup') {
                await processWalletTopUp(session);
                console.log("Wallet top-up processed", session.id);
            } else {
                const order = await createOrderInSanity(session);
                console.log("Order created in Sanity", order);
            }
        } catch (error) {
            console.error("Error processing checkout session", error);
            return NextResponse.json(
                { error: "Error processing checkout session" },
                { status: 500 }
            );
        }
    }

    return NextResponse.json({ received: true }, { status: 200 });
}

async function processWalletTopUp(session: Stripe.Checkout.Session) {
    const { metadata } = session;
    
    if (!metadata) {
        throw new Error("Metadata is missing from the Stripe session.");
    }

    const { topUpRequestId, userId, amount } = metadata;

    if (!topUpRequestId || !userId || !amount) {
        throw new Error("Required metadata is missing for wallet top-up.");
    }

    // Update the top-up request status
    await client
        .patch(topUpRequestId)
        .set({ 
            status: 'completed',
            stripeSessionId: session.id,
            completedAt: new Date().toISOString()
        })
        .commit();

    // Get current user wallet balance
    const user = await client.fetch(
        `*[_type == "user" && _id == $userId][0]{ walletBalance }`,
        { userId }
    );

    if (!user) {
        throw new Error("User not found for wallet top-up.");
    }

    // Update user wallet balance
    const newBalance = (user.walletBalance || 0) + parseFloat(amount);
    await client
        .patch(userId)
        .set({ walletBalance: newBalance })
        .commit();

    // Create wallet transaction record
    await client.create({
        _type: 'walletTransaction',
        user: {
            _type: 'reference',
            _ref: userId
        },
        type: 'deposit',
        amount: parseFloat(amount),
        description: `Cüzdana ${amount} TL yüklendi (Stripe)`,
        status: 'completed',
        stripeSessionId: session.id,
        _createdAt: new Date().toISOString()
    });

    console.log(`Wallet top-up completed for user ${userId}: ${amount} TL`);
}

async function createOrderInSanity(session: Stripe.Checkout.Session) {
    const { metadata } = session;
    
    if (!metadata) {
        throw new Error("Metadata is missing from the Stripe session.");
    }

    const { orderNumber, customerName, customerEmail, clerkUserId } = metadata;

    if (!orderNumber || !customerName || !customerEmail) {
        throw new Error("Required metadata is missing from the Stripe session.");
    }

    // Get the user from Sanity
    let user = null;
    if (clerkUserId) {
        user = await client.fetch(
            `*[_type == "user" && clerkId == $clerkUserId][0]`,
            { clerkUserId }
        );
    }

    // Retrieve line items from the session
    const lineItems = await stripe.checkout.sessions.listLineItems(session.id, {
        expand: ["data.price.product"],
    });

    const sanityProducts = lineItems.data.map((item) => ({
        _key: crypto.randomUUID(),
        product: {
            _type: "reference",
            _ref: item.price?.product?.metadata?.productId || "",
        },
        quantity: item.quantity || 0,
    }));

    const order = await client.create({
        _type: "order",
        orderNumber,
        stripeCheckoutSessionId: session.id,
        customerName,
        customerEmail,
        clerkUserId: clerkUserId || null,
        user: user ? {
            _type: "reference",
            _ref: user._id,
        } : null,
        products: sanityProducts,
        currency: session.currency || "usd",
        amountTotal: session.amount_total ? session.amount_total / 100 : 0,
        status: "paid",
        orderDate: new Date().toISOString(),
    });

    return order;
}
