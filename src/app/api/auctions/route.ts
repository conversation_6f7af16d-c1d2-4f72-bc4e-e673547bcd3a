import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'active';
    const category = searchParams.get('category');
    const featured = searchParams.get('featured');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Build query
    let query = `*[_type == "auction"`;
    
    // Add filters
    const filters = [];
    if (status !== 'all') {
      filters.push(`status == "${status}"`);
    }
    if (category) {
      filters.push(`category == "${category}"`);
    }
    if (featured === 'true') {
      filters.push(`featured == true`);
    }

    if (filters.length > 0) {
      query += ` && (${filters.join(' && ')})`;
    }

    query += `] | order(endTime asc)`;
    
    if (limit > 0) {
      query += `[${offset}...${offset + limit}]`;
    }

    query += `{
      _id,
      id,
      name,
      description,
      image{
        asset->{
          _id,
          url
        }
      },
      images[]{
        asset->{
          _id,
          url
        }
      },
      currentBid,
      startingBid,
      bidIncrementAmount,
      reservePrice,
      buyNowPrice,
      startTime,
      endTime,
      status,
      category,
      condition,
      featured,
      viewCount,
      watchCount,
      seller->{
        _id,
        name,
        email
      },
      bidHistory[]{
        bidAmount,
        bidTime,
        bidder->{
          _id,
          name
        }
      },
      shippingInfo,
      tags
    }`;

    const auctions = await client.fetch(query);

    // Get total count for pagination
    let countQuery = `count(*[_type == "auction"`;
    if (filters.length > 0) {
      countQuery += ` && (${filters.join(' && ')})`;
    }
    countQuery += `])`;
    
    const totalCount = await client.fetch(countQuery);

    return NextResponse.json({
      success: true,
      data: {
        auctions,
        pagination: {
          total: totalCount,
          limit,
          offset,
          hasMore: offset + limit < totalCount
        }
      }
    });

  } catch (error) {
    console.error('Auctions fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is seller or admin
    if (!user.isSeller && !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Only sellers and admins can create auctions' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      startingBid,
      bidIncrementAmount = 1,
      reservePrice,
      buyNowPrice,
      startTime,
      endTime,
      category,
      condition,
      shippingCost = 0,
      freeShippingThreshold,
      estimatedDelivery = 3,
      tags = [],
      notes,
      images = [],
      image
    } = body;

    // Validation
    if (!name || !startingBid || !startTime || !endTime) {
      return NextResponse.json(
        { success: false, message: 'Required fields missing' },
        { status: 400 }
      );
    }

    if (new Date(startTime) >= new Date(endTime)) {
      return NextResponse.json(
        { success: false, message: 'End time must be after start time' },
        { status: 400 }
      );
    }

    // Generate unique auction ID
    const auctionId = `auction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const newAuction = {
      _type: 'auction',
      id: auctionId,
      name,
      description,
      image,
      images,
      currentBid: 0,
      startingBid,
      bidIncrementAmount,
      reservePrice,
      buyNowPrice,
      startTime,
      endTime,
      status: new Date() >= new Date(startTime) ? 'active' : 'pending',
      category,
      condition,
      seller: {
        _type: 'reference',
        _ref: user._id
      },
      featured: false,
      autoExtend: true,
      extendMinutes: 5,
      shippingInfo: {
        shippingCost,
        freeShippingThreshold,
        estimatedDelivery
      },
      tags,
      viewCount: 0,
      watchCount: 0,
      notes,
      bidders: [],
      bidHistory: []
    };

    const createdAuction = await client.create(newAuction);

    return NextResponse.json({
      success: true,
      data: createdAuction,
      message: 'Auction created successfully'
    });

  } catch (error) {
    console.error('Auction creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
