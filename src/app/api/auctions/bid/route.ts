import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { auctionId, bidAmount } = body;

    if (!auctionId || !bidAmount) {
      return NextResponse.json(
        { success: false, message: 'Auction ID and bid amount are required' },
        { status: 400 }
      );
    }

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is approved for bidding
    if (!user.isAdminApproved && !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin approval required for bidding' },
        { status: 403 }
      );
    }

    // Check if user has sufficient wallet balance
    if (user.walletBalance < bidAmount) {
      return NextResponse.json(
        { success: false, message: 'Insufficient wallet balance' },
        { status: 400 }
      );
    }

    // Get auction by _id or id field
    let auction = await client.fetch(
      `*[_type == "auction" && (_id == $auctionId || id == $auctionId)][0]{
        _id,
        id,
        name,
        currentBid,
        startingBid,
        bidIncrementAmount,
        endTime,
        status,
        seller,
        bidHistory,
        autoExtend,
        extendMinutes,
        image{
          asset->{
            _id,
            url
          }
        }
      }`,
      { auctionId }
    );

    if (!auction) {
      return NextResponse.json(
        { success: false, message: 'Auction not found' },
        { status: 404 }
      );
    }

    // Check if auction is active
    if (auction.status !== 'active') {
      return NextResponse.json(
        { success: false, message: 'Auction is not active' },
        { status: 400 }
      );
    }

    // Check if auction has ended
    if (new Date() > new Date(auction.endTime)) {
      return NextResponse.json(
        { success: false, message: 'Auction has ended' },
        { status: 400 }
      );
    }

    // Check if user is not the seller
    if (auction.seller?._ref === user._id) {
      return NextResponse.json(
        { success: false, message: 'Sellers cannot bid on their own auctions' },
        { status: 400 }
      );
    }

    const currentBid = auction.currentBid || 0;
    const startingBid = auction.startingBid || 0;
    const bidIncrementAmount = auction.bidIncrementAmount || 1;

    // Determine the base amount for validation
    const baseBid = currentBid > 0 ? currentBid : startingBid;

    // Validate bid amount
    if (bidAmount <= baseBid) {
      const message = currentBid > 0
        ? `Your bid must be higher than the current bid (₺${currentBid})`
        : `Your bid must be higher than the starting price (₺${startingBid})`;
      return NextResponse.json(
        { success: false, message },
        { status: 400 }
      );
    }

    // Check minimum increment (only for existing bids)
    if (bidAmount < baseBid + bidIncrementAmount && currentBid > 0) {
      return NextResponse.json(
        { success: false, message: `Minimum bid increment is ₺${bidIncrementAmount}. Minimum bid: ₺${baseBid + bidIncrementAmount}` },
        { status: 400 }
      );
    }

    // Check for auto-extend
    let newEndTime = auction.endTime;
    const timeUntilEnd = new Date(auction.endTime).getTime() - new Date().getTime();
    const fiveMinutesInMs = 5 * 60 * 1000;

    if (auction.autoExtend && timeUntilEnd < fiveMinutesInMs) {
      const extendMs = (auction.extendMinutes || 5) * 60 * 1000;
      newEndTime = new Date(new Date(auction.endTime).getTime() + extendMs).toISOString();
    }

    // Create bid entry
    const bidEntry = {
      _key: `${Date.now()}-${user._id}`,
      bidAmount: bidAmount,
      bidTime: new Date().toISOString(),
      bidder: { _ref: user._id, _type: 'reference' },
      isAutoBid: false
    };

    // Get current bidHistory and bidders
    const currentBidHistory = auction.bidHistory || [];
    const currentBidders = auction.bidders || [];

    // Add new bid to history
    const newBidHistory = [bidEntry, ...currentBidHistory];

    // Add bidder if not already in list
    const bidderExists = currentBidders.some((bidder: any) => bidder._ref === user._id);
    const newBidders = bidderExists ? currentBidders : [...currentBidders, { _ref: user._id, _type: 'reference' }];

    // Update auction with new bid
    const transaction = client.transaction();

    // Update auction
    transaction.patch(auction._id, (patch) =>
      patch
        .set({
          currentBid: bidAmount,
          endTime: newEndTime,
          bidHistory: newBidHistory,
          bidders: newBidders
        })
    );

    // Reserve the bid amount from user's wallet (optional - for bid security)
    // transaction.patch(user._id, (patch) =>
    //   patch.dec({ walletBalance: bidAmount })
    // );

    await transaction.commit();

    return NextResponse.json({
      success: true,
      data: {
        newBid: bidAmount,
        newEndTime,
        timeExtended: newEndTime !== auction.endTime
      },
      message: 'Bid placed successfully'
    });

  } catch (error) {
    console.error('Bid placement error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
