import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ auctionId: string }> }
) {
  try {
    const { auctionId } = await params;

    // Get auction details
    const auction = await client.fetch(
      `*[_type == "auction" && (_id == $auctionId || id == $auctionId)][0]{
        _id,
        id,
        name,
        description,
        image{
          asset->{
            _id,
            url
          }
        },
        images[]{
          asset->{
            _id,
            url
          }
        },
        currentBid,
        startingBid,
        bidIncrementAmount,
        reservePrice,
        buyNowPrice,
        startTime,
        endTime,
        status,
        category,
        condition,
        featured,
        autoExtend,
        extendMinutes,
        viewCount,
        watchCount,
        notes,
        tags,
        seller->{
          _id,
          name,
          email,
          avatar
        },
        winner->{
          _id,
          name
        },
        winningBid,
        bidHistory[]{
          bidAmount,
          bidTime,
          isAutoBid,
          bidder->{
            _id,
            name
          }
        } | order(bidTime desc),
        bidders[]->{
          _id,
          name
        },
        shippingInfo,
        _createdAt,
        _updatedAt
      }`,
      { auctionId }
    );

    if (!auction) {
      return NextResponse.json(
        { success: false, message: 'Auction not found' },
        { status: 404 }
      );
    }

    // Increment view count (set to 1 if not exists, otherwise increment)
    await client
      .patch(auction._id)
      .setIfMissing({ viewCount: 0 })
      .inc({ viewCount: 1 })
      .commit();

    // Calculate time remaining
    const now = new Date();
    const endTime = new Date(auction.endTime);
    const timeRemaining = endTime.getTime() - now.getTime();
    const isActive = auction.status === 'active' && timeRemaining > 0;

    // Get bid statistics
    const bidStats = {
      totalBids: auction.bidHistory?.length || 0,
      uniqueBidders: auction.bidders?.length || 0,
      averageBid: auction.bidHistory?.length > 0 
        ? auction.bidHistory.reduce((sum: number, bid: any) => sum + bid.bidAmount, 0) / auction.bidHistory.length 
        : 0,
      highestBid: auction.currentBid || 0,
      timeRemaining: Math.max(0, timeRemaining),
      isActive
    };

    return NextResponse.json({
      success: true,
      data: {
        auction: {
          ...auction,
          viewCount: (auction.viewCount || 0) + 1
        },
        bidStats
      }
    });

  } catch (error) {
    console.error('Auction detail fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ auctionId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { auctionId } = await params;
    const body = await request.json();

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Get auction
    const auction = await client.fetch(
      `*[_type == "auction" && (_id == $auctionId || id == $auctionId)][0]{
        _id,
        seller
      }`,
      { auctionId }
    );

    if (!auction) {
      return NextResponse.json(
        { success: false, message: 'Auction not found' },
        { status: 404 }
      );
    }

    // Check if user is the seller or admin
    if (auction.seller?._ref !== user._id && !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Only the seller or admin can update this auction' },
        { status: 403 }
      );
    }

    // Update auction
    const updatedAuction = await client
      .patch(auction._id)
      .set({
        ...body,
        _updatedAt: new Date().toISOString()
      })
      .commit();

    return NextResponse.json({
      success: true,
      data: updatedAuction,
      message: 'Auction updated successfully'
    });

  } catch (error) {
    console.error('Auction update error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ auctionId: string }> }
) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { auctionId } = await params;

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Get auction
    const auction = await client.fetch(
      `*[_type == "auction" && (_id == $auctionId || id == $auctionId)][0]{
        _id,
        seller,
        status,
        bidHistory
      }`,
      { auctionId }
    );

    if (!auction) {
      return NextResponse.json(
        { success: false, message: 'Auction not found' },
        { status: 404 }
      );
    }

    // Check if user is the seller or admin
    if (auction.seller?._ref !== user._id && !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Only the seller or admin can delete this auction' },
        { status: 403 }
      );
    }

    // Check if auction has bids
    if (auction.bidHistory && auction.bidHistory.length > 0) {
      return NextResponse.json(
        { success: false, message: 'Cannot delete auction with existing bids' },
        { status: 400 }
      );
    }

    // Delete auction
    await client.delete(auction._id);

    return NextResponse.json({
      success: true,
      message: 'Auction deleted successfully'
    });

  } catch (error) {
    console.error('Auction deletion error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
