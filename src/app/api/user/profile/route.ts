import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0] {
        _id,
        clerkId,
        name,
        email,
        isAdmin,
        isAdminApproved,
        isSeller,
        walletBalance,
        giftVoucherBalance,
        mlmLevel,
        directReferrals,
        totalTeamMembers,
        totalCommissionEarned,
        monthlyCommissionEarned,
        totalSales,
        membershipStartDate
      }`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ user });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
