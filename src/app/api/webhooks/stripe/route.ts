import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';

export async function POST(req: NextRequest) {
  // Initialize Stripe on server-side
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!);

  const body = await req.text();
  const signature = headers().get('stripe-signature');

  if (!signature) {
    return NextResponse.json(
      { error: 'No signature found' },
      { status: 400 }
    );
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Webhook signature verification failed' },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case 'checkout.session.completed':
        const session = event.data.object;
        console.log('Payment successful:', session.id);
        
        // Here you would typically:
        // 1. Save order to database
        // 2. Send confirmation email
        // 3. Update inventory
        // 4. Clear user's cart
        
        await handleSuccessfulPayment(session);
        break;

      case 'payment_intent.payment_failed':
        const paymentIntent = event.data.object;
        console.log('Payment failed:', paymentIntent.id);
        
        // Handle failed payment
        await handleFailedPayment(paymentIntent);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook handler error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handleSuccessfulPayment(session: any) {
  // In a real application, you would:
  // 1. Create order record in database
  // 2. Update product inventory
  // 3. Send confirmation email
  // 4. Clear user's cart
  
  console.log('Processing successful payment:', {
    sessionId: session.id,
    customerEmail: session.customer_email,
    amountTotal: session.amount_total,
    currency: session.currency,
    metadata: session.metadata,
  });
  
  // Example: Save order to database
  // await createOrder({
  //   sessionId: session.id,
  //   customerEmail: session.customer_email,
  //   amount: session.amount_total,
  //   currency: session.currency,
  //   items: JSON.parse(session.metadata.items || '[]'),
  //   status: 'paid'
  // });
}

async function handleFailedPayment(paymentIntent: any) {
  console.log('Processing failed payment:', {
    paymentIntentId: paymentIntent.id,
    lastPaymentError: paymentIntent.last_payment_error,
  });
  
  // Handle failed payment logic
  // Maybe send email to customer about failed payment
}
