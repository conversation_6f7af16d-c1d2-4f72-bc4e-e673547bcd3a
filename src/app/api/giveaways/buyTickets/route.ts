import { client } from '@/sanity/lib/client';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';

// Helper function to generate a random ticket number
const generateRandomTicket = (length: number): string => {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  const uniqueNum = Math.floor(Math.random() * (max - min + 1)) + min;
  return uniqueNum.toString();
};

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Yetkisiz: Giriş yapmalısınız.' },
        { status: 401 }
      );
    }

    const { giveawayId, numberOfTickets } = await req.json();

    // Get user from Sanity
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdminApproved) {
      return NextResponse.json(
        { error: 'Bilet satın almak için yönetici onayı gerekiyor.' },
        { status: 403 }
      );
    }

    // Get giveaway
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0]`,
      { giveawayId }
    );

    if (!giveaway) {
      return NextResponse.json(
        { error: 'Çekiliş bulunamadı.' },
        { status: 404 }
      );
    }

    if (giveaway.status !== 'active') {
      return NextResponse.json(
        { error: 'Çekiliş aktif değil.' },
        { status: 400 }
      );
    }

    // Check ticket availability
    const numbersPerCard = giveaway.numbersPerCard || 3;
    const ticketDigitLength = giveaway.ticketDigitLength || 3;
    const ticketPrice = giveaway.ticketPrice || 0;
    const totalCombinations = Math.pow(10, ticketDigitLength);
    const maxCards = Math.floor(totalCombinations / numbersPerCard);
    const ticketsSold = giveaway.ticketsSold || 0;
    const availableCards = maxCards - ticketsSold;

    if (availableCards <= 0) {
      return NextResponse.json(
        { error: 'Bilet bitmiştir.' },
        { status: 400 }
      );
    }

    if (numberOfTickets > availableCards) {
      return NextResponse.json(
        { error: `Yeterli kart yok. Maksimum ${availableCards} kart alabilirsiniz.` },
        { status: 400 }
      );
    }

    // Check wallet balance
    if ((user.walletBalance || 0) < (ticketPrice * numberOfTickets)) {
      return NextResponse.json(
        { error: 'Yetersiz cüzdan bakiyesi.' },
        { status: 400 }
      );
    }

    // Generate unique ticket numbers
    const soldNumbers = new Set<string>();
    if (giveaway.participants) {
      for (const participant of giveaway.participants) {
        if (participant.tickets) {
          for (const ticket of participant.tickets) {
            soldNumbers.add(ticket.ticketNumber);
          }
        }
      }
    }

    // Generate new cards
    const newCards: string[][] = [];
    for (let c = 0; c < numberOfTickets; c++) {
      const cardNumbers: string[] = [];
      for (let n = 0; n < numbersPerCard; n++) {
        let uniqueNum;
        do {
          uniqueNum = generateRandomTicket(ticketDigitLength);
        } while (soldNumbers.has(uniqueNum) || cardNumbers.includes(uniqueNum));
        cardNumbers.push(uniqueNum);
        soldNumbers.add(uniqueNum);
      }
      newCards.push(cardNumbers);
    }

    // Update giveaway with new tickets
    const transaction = client.transaction();
    
    const userAlreadyParticipant = (giveaway.participants || []).some(
      (p: any) => p.user?._ref === user._id
    );

    const newTicketObjs = newCards.flatMap(card => 
      card.map(ticketNumber => ({
        _key: `${Date.now()}-${user._id}-${ticketNumber}`,
        ticketNumber,
        purchasedAt: new Date().toISOString(),
        chosenDigitCount: ticketDigitLength,
        status: 'lost'
      }))
    );

    if (userAlreadyParticipant) {
      // Add tickets to existing participant
      const participantIndex = giveaway.participants.findIndex(
        (p: any) => p.user?._ref === user._id
      );
      
      transaction.patch(giveaway._id, patch =>
        patch
          .setIfMissing({ participants: [] })
          .append(`participants[${participantIndex}].tickets`, newTicketObjs)
          .inc({ ticketsSold: numberOfTickets })
      );
    } else {
      // Add new participant
      transaction.patch(giveaway._id, patch =>
        patch
          .setIfMissing({ participants: [] })
          .append('participants', [
            {
              _key: `${Date.now()}-${user._id}`,
              user: { _ref: user._id, _type: 'reference' },
              tickets: newTicketObjs,
            },
          ])
          .inc({ ticketsSold: numberOfTickets })
      );
    }

    // Update user wallet balance
    transaction.patch(user._id, {
      set: { 
        walletBalance: (user.walletBalance || 0) - (ticketPrice * numberOfTickets) 
      }
    });

    await transaction.commit();

    return NextResponse.json({ 
      success: true, 
      purchasedTickets: newCards 
    });

  } catch (error) {
    console.error('Bilet satın alırken hata oluştu:', error);
    return NextResponse.json(
      { error: 'Bilet satın alınamadı.' },
      { status: 500 }
    );
  }
}
