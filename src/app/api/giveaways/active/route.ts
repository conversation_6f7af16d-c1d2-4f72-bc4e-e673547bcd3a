import { NextResponse } from 'next/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const giveaways = await client.fetch(`
      *[_type == "giveaway" && status == "active"] | order(_createdAt desc) {
        _id,
        title,
        description,
        status,
        startDate,
        endDate,
        drawDate,
        totalTickets,
        ticketsSold,
        ticketPrice,
        ticketSalePercentageForDraw,
        numbersPerCard,
        ticketDigitLength,
        participants,
        prizes,
        winningNumbers,
        winners,
        rules,
        image{
          asset->{
            _id,
            url
          }
        },
        images[]{
          asset->{
            _id,
            url
          }
        }
      }
    `);
    
    return NextResponse.json({ giveaways });
  } catch (error) {
    console.error('Error fetching active giveaways:', error);
    return NextResponse.json({ error: 'Aktif çekilişler alınamadı.' }, { status: 500 });
  }
}
