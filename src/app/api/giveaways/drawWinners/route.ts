import { client } from '@/sanity/lib/client';
import { auth } from '@clerk/nextjs/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Yetkisiz: <PERSON><PERSON><PERSON> yapmalısınız.' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { error: 'Bu işlem için admin yetkisi gereklidir.' },
        { status: 403 }
      );
    }

    const { giveawayId, winnerCount = 1 } = await req.json();

    // Get giveaway with participants
    const giveaway = await client.fetch(
      `*[_type == "giveaway" && _id == $giveawayId][0] {
        _id,
        title,
        status,
        totalTickets,
        ticketsSold,
        participants[] {
          user-> {
            _id,
            name,
            email,
            clerkId
          },
          tickets[] {
            ticketNumber,
            purchasedAt,
            status
          }
        },
        prizes[] {
          rank,
          title,
          value
        },
        winners,
        winningNumbers
      }`,
      { giveawayId }
    );

    if (!giveaway) {
      return NextResponse.json(
        { error: 'Çekiliş bulunamadı.' },
        { status: 404 }
      );
    }

    if (giveaway.status !== 'active') {
      return NextResponse.json(
        { error: 'Sadece aktif çekilişlerde kazanan belirlenebilir.' },
        { status: 400 }
      );
    }

    if (giveaway.winners && giveaway.winners.length > 0) {
      return NextResponse.json(
        { error: 'Bu çekilişte zaten kazananlar belirlenmiş.' },
        { status: 400 }
      );
    }

    // Collect all tickets
    const allTickets: Array<{
      ticketNumber: string;
      userId: string;
      userName: string;
      userEmail: string;
    }> = [];

    if (giveaway.participants) {
      for (const participant of giveaway.participants) {
        if (participant.user && participant.tickets) {
          for (const ticket of participant.tickets) {
            allTickets.push({
              ticketNumber: ticket.ticketNumber,
              userId: participant.user._id,
              userName: participant.user.name || participant.user.email,
              userEmail: participant.user.email || '',
            });
          }
        }
      }
    }

    if (allTickets.length === 0) {
      return NextResponse.json(
        { error: 'Çekilişte hiç bilet satılmamış.' },
        { status: 400 }
      );
    }

    // Randomly select winners
    const shuffledTickets = [...allTickets].sort(() => Math.random() - 0.5);
    const selectedWinners = shuffledTickets.slice(0, Math.min(winnerCount, shuffledTickets.length));
    
    // Prepare winners data
    const winners = selectedWinners.map((winner, index) => ({
      _key: `winner-${Date.now()}-${index}`,
      user: {
        _ref: winner.userId,
        _type: 'reference'
      },
      ticketNumber: winner.ticketNumber,
      prize: giveaway.prizes?.[index]?.title || `${index + 1}. Ödül`,
      rank: index + 1
    }));

    const winningNumbers = selectedWinners.map(w => w.ticketNumber);

    // Update giveaway with winners
    await client
      .patch(giveaway._id)
      .set({
        status: 'completed',
        winners: winners,
        winningNumbers: winningNumbers,
        drawDate: new Date().toISOString()
      })
      .commit();

    // Update winning tickets status
    const transaction = client.transaction();
    
    for (const participant of giveaway.participants || []) {
      if (participant.user && participant.tickets) {
        const updatedTickets = participant.tickets.map((ticket: any) => ({
          ...ticket,
          status: winningNumbers.includes(ticket.ticketNumber) ? 'won' : 'lost'
        }));
        
        const participantIndex = giveaway.participants.findIndex(
          (p: any) => p.user._id === participant.user._id
        );
        
        if (participantIndex !== -1) {
          transaction.patch(giveaway._id, patch =>
            patch.set({
              [`participants[${participantIndex}].tickets`]: updatedTickets
            })
          );
        }
      }
    }

    await transaction.commit();

    // Prepare response
    const responseWinners = selectedWinners.map((winner, index) => ({
      displayName: winner.userName,
      userName: winner.userName,
      userEmail: winner.userEmail,
      ticketNumber: winner.ticketNumber,
      prize: giveaway.prizes?.[index]?.title || `${index + 1}. Ödül`,
      rank: index + 1
    }));

    return NextResponse.json({
      success: true,
      giveawayTitle: giveaway.title,
      winningNumbers,
      winners: responseWinners,
      message: 'Çekiliş başarıyla tamamlandı!'
    });

  } catch (error) {
    console.error('Draw winners error:', error);
    return NextResponse.json(
      { error: 'Çekiliş yapılırken bir hata oluştu.' },
      { status: 500 }
    );
  }
}
