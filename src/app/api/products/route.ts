import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { cdnClient } from '@/lib/sanity';
import { client } from '@/sanity/lib/client';
import { apiCache, CACHE_TTL, CACHE_KEYS, withCache } from '@/lib/cache';

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const vendor = searchParams.get('vendor');
    const brand = searchParams.get('brand');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');
    const search = searchParams.get('search');

    // Generate cache key based on parameters
    const cacheKey = apiCache.generateKey(CACHE_KEYS.PRODUCTS, {
      category,
      vendor,
      brand,
      search,
      limit,
      offset
    });

    // Try to get from cache first
    const cachedResult = apiCache.get(cacheKey);
    if (cachedResult) {
      console.log(`Cache HIT for products: ${cacheKey}`);
      const response = NextResponse.json({
        ...cachedResult,
        meta: {
          ...cachedResult.meta,
          cached: true,
          duration: `${Date.now() - startTime}ms (cached)`
        }
      });
      response.headers.set('Cache-Control', 's-maxage=30, stale-while-revalidate');
      return response;
    }

    console.log(`Cache MISS for products: ${cacheKey}`);

    // Build dynamic GROQ query based on filters
    let query = `*[_type == "product" && status == "active"`;
    const params: any = {};

    if (category) {
      query += ` && category == $category`;
      params.category = category;
    }

    if (vendor) {
      query += ` && vendor._ref == $vendorId`;
      params.vendorId = vendor;
    }

    if (brand && brand !== 'All') {
      query += ` && vendor->name match $brand`;
      params.brand = `*${brand}*`;
    }

    if (search) {
      query += ` && (name match $search || description match $search)`;
      params.search = `*${search}*`;
    }

    query += `]{
      _id,
      name,
      description,
      price,
      stock,
      category,
      status,
      "imageUrl": images[0].asset->url,
      "images": images[].asset->url,
      "vendor": vendor->{_id, name, userId},
      _createdAt
    } | order(name asc)[${offset}...${offset + limit}]`;

    console.log('Executing GROQ query:', query);
    console.log('With params:', params);

    const products = await cdnClient.fetch(query, params);
    const duration = Date.now() - startTime;

    console.log(`Fetched ${products.length} products from Sanity`);

    const result = {
      success: true,
      data: products,
      pagination: {
        limit,
        offset,
        total: products.length,
        hasMore: products.length === limit
      },
      meta: {
        duration: `${duration}ms`,
        cached: false
      },
      message: `Fetched ${products.length} products from Sanity CMS`
    };

    // Cache the result
    const ttl = search ? CACHE_TTL.SEARCH : CACHE_TTL.PRODUCTS;
    apiCache.set(cacheKey, result, ttl);

    // Cache-Control header'ı ekle
    const response = NextResponse.json(result);
    response.headers.set('Cache-Control', 's-maxage=30, stale-while-revalidate');

    return response;

  } catch (error) {
    console.error('Products API error:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch products from Sanity',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized'
      }, { status: 401 });
    }

    // Check if user is vendor or admin
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || (!user.isSeller && !user.isAdmin)) {
      return NextResponse.json({
        success: false,
        error: 'Vendor access required'
      }, { status: 403 });
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      discountedPrice,
      category,
      brand,
      stock,
      sku,
      tags,
      specifications
    } = body;

    // Validation
    if (!name || !description || !price || !category || stock === undefined) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: name, description, price, category, stock'
      }, { status: 400 });
    }

    // Create vendor if not exists
    let vendor = await client.fetch(
      `*[_type == "vendor" && user._ref == $userId][0]`,
      { userId: user._id }
    );

    if (!vendor) {
      vendor = await client.create({
        _type: 'vendor',
        name: user.name || 'Vendor',
        email: user.email,
        user: {
          _type: 'reference',
          _ref: user._id
        },
        isActive: true,
        rating: 0,
        totalSales: 0,
        joinDate: new Date().toISOString()
      });
    }

    // Create product
    const product = await client.create({
      _type: 'product',
      name,
      description,
      price: parseFloat(price),
      discountedPrice: discountedPrice ? parseFloat(discountedPrice) : null,
      category: {
        _type: 'reference',
        _ref: category
      },
      vendor: {
        _type: 'reference',
        _ref: vendor._id
      },
      brand: brand || null,
      stock: parseInt(stock),
      sku: sku || null,
      tags: tags || [],
      specifications: specifications || null,
      status: 'active',
      isFeatured: false,
      rating: 0,
      reviewCount: 0,
      totalSales: 0,
      _createdAt: new Date().toISOString()
    });

    return NextResponse.json({
      success: true,
      product,
      message: 'Product created successfully'
    }, { status: 201 });

  } catch (error) {
    console.error('Create product error:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to create product'
    }, { status: 500 });
  }
}
