import { NextRequest, NextResponse } from 'next/server';
import { cdnClient } from '@/lib/sanity';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // GROQ query to fetch single product with all details
    const query = `*[_type == "product" && _id == $id][0]{
      _id,
      name,
      description,
      price,
      stock,
      category,
      status,
      "images": images[].asset->url,
      "imageUrl": images[0].asset->url,
      "vendor": vendor->{
        _id,
        name,
        userId,
        description,
        "logo": logo.asset->url,
        rating,
        totalReviews
      },
      tags,
      specifications,
      _createdAt,
      _updatedAt
    }`;

    console.log('Fetching product with ID:', id);

    const product = await cdnClient.fetch(query, { id });

    if (!product) {
      return NextResponse.json({
        success: false,
        error: 'Product not found'
      }, { status: 404 });
    }

    console.log('Fetched product:', product.name);

    const response = NextResponse.json({
      success: true,
      data: product,
      message: `Product ${product.name} fetched from Sanity CMS`
    });

    response.headers.set('Cache-Control', 's-maxage=60, stale-while-revalidate');
    
    return response;

  } catch (error) {
    console.error('Product fetch error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch product from Sanity',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
