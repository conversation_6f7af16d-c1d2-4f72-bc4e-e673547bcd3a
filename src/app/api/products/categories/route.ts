import { NextRequest, NextResponse } from 'next/server';
import { cdnClient } from '@/lib/sanity';
import { apiCache, CACHE_TTL, CACHE_KEYS } from '@/lib/cache';

export async function GET(request: NextRequest) {
  const startTime = Date.now();

  try {
    const cacheKey = apiCache.generateKey(CACHE_KEYS.CATEGORIES, {});

    // Try to get from cache first
    const cachedResult = apiCache.get(cacheKey);
    if (cachedResult) {
      console.log(`Cache HIT for categories: ${cacheKey}`);
      const response = NextResponse.json({
        ...cachedResult,
        meta: {
          ...cachedResult.meta,
          cached: true,
          duration: `${Date.now() - startTime}ms (cached)`
        }
      });
      response.headers.set('Cache-Control', 's-maxage=300, stale-while-revalidate');
      return response;
    }

    console.log(`Cache MISS for categories: ${cacheKey}`);

    // Get unique categories from products
    const categories = await cdnClient.fetch(`
      array::unique(*[_type == "product" && status == "active"].category) | order(@)
    `);

    // Get unique brands (vendor names) from products
    const brands = await cdnClient.fetch(`
      array::unique(*[_type == "product" && status == "active"].vendor->name) | order(@)
    `);

    const duration = Date.now() - startTime;

    console.log(`Fetched ${categories.length} categories and ${brands.length} brands from Sanity`);

    const result = {
      success: true,
      data: {
        categories: ['All', ...categories.filter(Boolean)],
        brands: ['All', ...brands.filter(Boolean)]
      },
      meta: {
        duration: `${duration}ms`,
        cached: false
      },
      message: `Fetched categories and brands from Sanity CMS`
    };

    // Cache the result for 5 minutes
    apiCache.set(cacheKey, result, CACHE_TTL.CATEGORIES || 300);

    const response = NextResponse.json(result);
    response.headers.set('Cache-Control', 's-maxage=300, stale-while-revalidate');

    return response;

  } catch (error) {
    console.error('Categories API error:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch categories from Sanity',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
