import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { applicationId } = await req.json();

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    // Get the application
    const application = await client.fetch(
      `*[_type == "sellerApplication" && _id == $applicationId][0]`,
      { applicationId }
    );

    if (!application) {
      return NextResponse.json(
        { error: 'Application not found' },
        { status: 404 }
      );
    }

    // Update application status
    await client
      .patch(applicationId)
      .set({
        status: 'approved',
        reviewedAt: new Date().toISOString(),
        reviewedBy: {
          _type: 'reference',
          _ref: adminUser._id
        }
      })
      .commit();

    // Update user to be a seller
    if (application.applicant && application.applicant._ref) {
      await client
        .patch(application.applicant._ref)
        .set({ isSeller: true })
        .commit();
    }

    return NextResponse.json({
      success: true,
      message: 'Seller application approved successfully'
    });

  } catch (error) {
    console.error('Error approving seller application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
