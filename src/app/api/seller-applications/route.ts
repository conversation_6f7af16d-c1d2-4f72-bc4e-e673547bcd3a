import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all seller applications
    const applications = await client.fetch(`
      *[_type == "sellerApplication"] | order(_createdAt desc) {
        _id,
        businessType,
        businessName,
        contactPerson,
        contactEmail,
        contactPhone,
        status,
        submittedAt,
        "applicant": applicant-> {
          name,
          email,
          clerkId
        }
      }
    `);

    return NextResponse.json({ applications });
  } catch (error) {
    console.error('Error fetching seller applications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user info
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user already has a pending application
    const existingApplication = await client.fetch(
      `*[_type == "sellerApplication" && applicant._ref == $userId && status == "pending"][0]`,
      { userId: user._id }
    );

    if (existingApplication) {
      return NextResponse.json(
        { error: 'You already have a pending seller application' },
        { status: 400 }
      );
    }

    const body = await req.json();
    const {
      businessType,
      businessName,
      businessAddress,
      taxNumber,
      contactPerson,
      contactPhone,
      contactEmail,
      businessDescription,
      productCategories,
      estimatedMonthlyVolume,
      hasPhysicalStore,
      websiteUrl,
      socialMediaLinks,
      agreedToTerms
    } = body;

    // Validate required fields
    if (!businessType || !businessName || !taxNumber || !contactPerson || !contactPhone || !contactEmail || !businessDescription || !productCategories || !agreedToTerms) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create seller application
    const application = await client.create({
      _type: 'sellerApplication',
      applicant: {
        _type: 'reference',
        _ref: user._id
      },
      businessType,
      businessName,
      businessAddress,
      taxNumber,
      contactPerson,
      contactPhone,
      contactEmail,
      businessDescription,
      productCategories,
      estimatedMonthlyVolume: estimatedMonthlyVolume || null,
      hasPhysicalStore: hasPhysicalStore || false,
      websiteUrl: websiteUrl || null,
      socialMediaLinks: socialMediaLinks || null,
      status: 'pending',
      submittedAt: new Date().toISOString(),
      reviewedAt: null,
      reviewedBy: null,
      reviewNotes: null
    });

    return NextResponse.json({
      success: true,
      application,
      message: 'Seller application submitted successfully'
    });

  } catch (error) {
    console.error('Error creating seller application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
