import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { applicationId, reason } = await req.json();

    if (!applicationId) {
      return NextResponse.json(
        { error: 'Application ID is required' },
        { status: 400 }
      );
    }

    // Update application status
    await client
      .patch(applicationId)
      .set({
        status: 'rejected',
        reviewedAt: new Date().toISOString(),
        reviewedBy: {
          _type: 'reference',
          _ref: adminUser._id
        },
        reviewNotes: reason || 'No reason provided'
      })
      .commit();

    return NextResponse.json({
      success: true,
      message: 'Seller application rejected successfully'
    });

  } catch (error) {
    console.error('Error rejecting seller application:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
