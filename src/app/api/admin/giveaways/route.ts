import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const {
      title,
      description,
      ticketPrice,
      totalTickets,
      numbersPerCard,
      ticketDigitLength,
      startDate,
      endDate,
      prizes,
      images = [],
      image
    } = body;

    // Validate required fields
    if (!title || !description || !ticketPrice || !totalTickets || !startDate || !endDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create giveaway
    const giveaway = await client.create({
      _type: 'giveaway',
      title,
      description,
      image,
      images,
      ticketPrice: parseFloat(ticketPrice),
      totalTickets: parseInt(totalTickets),
      ticketsSold: 0,
      numbersPerCard: parseInt(numbersPerCard) || 3,
      ticketDigitLength: parseInt(ticketDigitLength) || 3,
      startDate: new Date(startDate).toISOString(),
      endDate: new Date(endDate).toISOString(),
      status: 'active',
      prizes: prizes || [],
      participants: [],
      winningNumbers: [],
      winners: [],
      createdAt: new Date().toISOString(),
      createdBy: {
        _type: 'reference',
        _ref: adminUser._id
      }
    });

    return NextResponse.json({
      success: true,
      giveaway,
      message: 'Giveaway created successfully'
    });

  } catch (error) {
    console.error('Error creating giveaway:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
