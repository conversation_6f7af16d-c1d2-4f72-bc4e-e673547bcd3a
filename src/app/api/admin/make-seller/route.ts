import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetClerkId, action = 'make_seller' } = body;

    if (!targetClerkId) {
      return NextResponse.json(
        { success: false, message: 'Target Clerk ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcıyı bul veya oluştur
    let user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: targetClerkId }
    );

    if (!user) {
      // Clerk'ten kullanıcı bilgilerini al
      try {
        const clerkUser = await clerkClient.users.getUser(targetClerkId);
        const userEmail = clerkUser.emailAddresses[0]?.emailAddress || '<EMAIL>';
        const userName = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || userEmail;

        // Kullanıcı yoksa oluştur
        const newUser = {
          _type: 'user',
          clerkId: targetClerkId,
          name: userName,
          email: userEmail,
          isAdmin: false,
          isAdminApproved: false,
          isSeller: false,
          walletBalance: 0,
          giftVoucherBalance: 0,
          mlmLevel: 'bronze',
          directReferrals: 0,
          totalTeamMembers: 0,
          totalCommissionEarned: 0,
          monthlyCommissionEarned: 0,
          totalSales: 0,
          membershipStartDate: new Date().toISOString()
        };

        user = await client.create(newUser);
      } catch (clerkError) {
        console.error('Clerk user fetch error:', clerkError);
        return NextResponse.json(
          { success: false, message: 'Invalid Clerk user ID' },
          { status: 400 }
        );
      }
    }

    let updateData = {};
    let message = '';

    switch (action) {
      case 'make_seller':
        updateData = { isSeller: true };
        message = `User ${user.name} is now a seller`;
        break;
      case 'remove_seller':
        updateData = { isSeller: false };
        message = `Seller privileges removed from ${user.name}`;
        break;
      case 'approve_bidding':
        updateData = { isAdminApproved: true };
        message = `User ${user.name} is now approved for bidding`;
        break;
      case 'remove_bidding':
        updateData = { isAdminApproved: false };
        message = `Bidding privileges removed from ${user.name}`;
        break;
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

    // Kullanıcıyı güncelle
    await client
      .patch(user._id)
      .set(updateData)
      .commit();

    return NextResponse.json({
      success: true,
      message,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        ...updateData
      }
    });

  } catch (error) {
    console.error('User privilege update error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
