import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all wallet transactions with user details
    const transactions = await client.fetch(
      `*[_type == "walletTransaction"] | order(_createdAt desc) [0...50] {
        _id,
        type,
        amount,
        description,
        status,
        _createdAt,
        user->{
          name,
          email
        }
      }`
    );

    return NextResponse.json({ transactions });
  } catch (error) {
    console.error('Error fetching admin wallet transactions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
