import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { transactionId, reason } = await req.json();

    if (!transactionId) {
      return NextResponse.json(
        { error: 'Transaction ID is required' },
        { status: 400 }
      );
    }

    // Get the transaction
    const transaction = await client.fetch(
      `*[_type == "walletTransaction" && _id == $transactionId][0]{
        _id,
        amount,
        user->{
          _id,
          walletBalance
        }
      }`,
      { transactionId }
    );

    if (!transaction) {
      return NextResponse.json(
        { error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Refund the amount back to user's wallet
    const currentBalance = transaction.user.walletBalance || 0;
    const newBalance = currentBalance + transaction.amount;

    await client
      .patch(transaction.user._id)
      .set({ walletBalance: newBalance })
      .commit();

    // Update transaction status
    await client
      .patch(transactionId)
      .set({
        status: 'failed',
        rejectedBy: {
          _type: 'reference',
          _ref: adminUser._id
        },
        rejectedAt: new Date().toISOString(),
        rejectionReason: reason || 'No reason provided'
      })
      .commit();

    return NextResponse.json({
      success: true,
      message: 'Withdrawal rejected and amount refunded'
    });

  } catch (error) {
    console.error('Error rejecting withdrawal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
