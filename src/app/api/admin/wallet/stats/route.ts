import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get wallet statistics
    const stats = await client.fetch(`{
      "totalTransactions": count(*[_type == "walletTransaction"]),
      "pendingWithdrawals": count(*[_type == "walletTransaction" && type == "withdrawal" && status == "pending"]),
      "totalWalletBalance": sum(*[_type == "user"].walletBalance),
      "monthlyVolume": sum(*[_type == "walletTransaction" && _createdAt > dateTime(now()) - 60*60*24*30].amount)
    }`);

    return NextResponse.json({ 
      stats: {
        totalTransactions: stats.totalTransactions || 0,
        pendingWithdrawals: stats.pendingWithdrawals || 0,
        totalWalletBalance: stats.totalWalletBalance || 0,
        monthlyVolume: stats.monthlyVolume || 0
      }
    });
  } catch (error) {
    console.error('Error fetching admin wallet stats:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
