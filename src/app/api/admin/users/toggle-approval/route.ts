import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { userId: targetUserId, isApproved } = await req.json();

    if (!targetUserId || typeof isApproved !== 'boolean') {
      return NextResponse.json(
        { error: 'User ID and approval status are required' },
        { status: 400 }
      );
    }

    // Update user approval status
    await client
      .patch(targetUserId)
      .set({ isAdminApproved: isApproved })
      .commit();

    return NextResponse.json({ 
      success: true, 
      message: `User ${isApproved ? 'approved' : 'unapproved'} successfully` 
    });
  } catch (error) {
    console.error('Error toggling user approval:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
