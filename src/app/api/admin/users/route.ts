import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all users with detailed information
    const users = await client.fetch(`
      *[_type == "user"] | order(_createdAt desc) {
        _id,
        name,
        email,
        clerkId,
        isAdmin,
        isAdminApproved,
        isSeller,
        walletBalance,
        giftVoucherBalance,
        mlmLevel,
        directReferrals,
        totalTeamMembers,
        totalCommissionEarned,
        joinDate,
        lastLoginDate,
        _createdAt
      }
    `);

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
