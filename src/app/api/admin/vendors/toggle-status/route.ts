import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    const { vendorId, isActive } = await req.json();

    if (!vendorId || typeof isActive !== 'boolean') {
      return NextResponse.json(
        { error: 'Vendor ID and isActive status are required' },
        { status: 400 }
      );
    }

    // Update vendor status
    await client
      .patch(vendorId)
      .set({ isActive })
      .commit();

    return NextResponse.json({ 
      success: true, 
      message: `Vendor ${isActive ? 'activated' : 'deactivated'} successfully` 
    });
  } catch (error) {
    console.error('Error toggling vendor status:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
