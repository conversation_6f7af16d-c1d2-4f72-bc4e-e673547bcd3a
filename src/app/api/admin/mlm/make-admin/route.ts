import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetClerkId } = body;

    if (!targetClerkId) {
      return NextResponse.json(
        { success: false, message: 'Target Clerk ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcıyı bul veya oluştur
    let user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: targetClerkId }
    );

    if (!user) {
      // Clerk'ten kullanıcı bilgilerini al
      try {
        const clerkUser = await clerkClient.users.getUser(targetClerkId);
        const userEmail = clerkUser.emailAddresses[0]?.emailAddress || '<EMAIL>';
        const userName = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || userEmail;

        // Kullanıcı yoksa oluştur
        const newUser = {
          _type: 'user',
          clerkId: targetClerkId,
          name: userName,
          email: userEmail,
          isAdmin: true,
          isAdminApproved: true,
          isSeller: true,
          walletBalance: 0,
          giftVoucherBalance: 0,
          mlmLevel: 'bronze',
          directReferrals: 0,
          totalTeamMembers: 0,
          totalCommissionEarned: 0,
          monthlyCommissionEarned: 0,
          totalSales: 0,
          membershipStartDate: new Date().toISOString()
        };

        user = await client.create(newUser);
      } catch (clerkError) {
        console.error('Clerk user fetch error:', clerkError);
        return NextResponse.json(
          { success: false, message: 'Invalid Clerk user ID' },
          { status: 400 }
        );
      }
    }

    // Admin yetkisi ver (eğer zaten admin değilse)
    if (!user.isAdmin) {
      await client
        .patch(user._id)
        .set({
          isAdmin: true,
          isAdminApproved: true,
          isSeller: true
        })
        .commit();
    }

    return NextResponse.json({
      success: true,
      message: `User ${user.name} is now an admin`,
      user: {
        _id: user._id,
        name: user.name,
        email: user.email,
        isAdmin: true
      }
    });

  } catch (error) {
    console.error('Make admin error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
