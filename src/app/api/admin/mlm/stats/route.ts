import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

async function checkAdminAccess(userId: string) {
  try {
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]{ isAdmin }`,
      { clerkId: userId }
    );
    return user?.isAdmin === true;
  } catch (error) {
    return false;
  }
}

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Admin yetkisi kontrolü
    const isAdmin = await checkAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Tüm kullanıcıları getir
    const users = await client.fetch(`
      *[_type == "user"]{
        _id,
        name,
        email,
        mlmLevel,
        sponsorCode,
        walletBalance,
        totalCommissionEarned,
        directReferrals,
        totalTeamMembers,
        isActive,
        membershipStartDate
      }
    `);

    // Komisyonları getir
    const commissions = await client.fetch(`
      *[_type == "mlmCommission"]{ amount, createdAt, status }
    `);

    // Bonusları getir
    const bonuses = await client.fetch(`
      *[_type == "mlmBonus"]{ amount, createdAt, status }
    `);

    // İstatistikleri hesapla
    const totalUsers = users.length;
    const activeUsers = users.filter((u: any) => u.isActive).length;
    const totalCommissions = commissions.reduce((sum: number, c: any) => sum + (c.amount || 0), 0);
    const totalBonuses = bonuses.reduce((sum: number, b: any) => sum + (b.amount || 0), 0);

    // Aylık büyüme hesapla
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const currentMonthUsers = users.filter((u: any) => {
      const membershipDate = new Date(u.membershipStartDate);
      return membershipDate.getMonth() === currentMonth && membershipDate.getFullYear() === currentYear;
    }).length;

    const lastMonthUsers = users.filter((u: any) => {
      const membershipDate = new Date(u.membershipStartDate);
      return membershipDate.getMonth() === lastMonth && membershipDate.getFullYear() === lastMonthYear;
    }).length;

    const monthlyGrowth = lastMonthUsers > 0 
      ? ((currentMonthUsers - lastMonthUsers) / lastMonthUsers) * 100 
      : currentMonthUsers > 0 ? 100 : 0;

    // Seviye dağılımı
    const levelDistribution = users.reduce((acc: any, user: any) => {
      acc[user.mlmLevel] = (acc[user.mlmLevel] || 0) + 1;
      return acc;
    }, {});

    const stats = {
      totalUsers,
      activeUsers,
      totalCommissions,
      totalBonuses,
      monthlyGrowth: Math.round(monthlyGrowth * 100) / 100,
      levelDistribution
    };

    return NextResponse.json({
      success: true,
      data: {
        stats,
        users
      }
    });

  } catch (error) {
    console.error('Admin stats error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
