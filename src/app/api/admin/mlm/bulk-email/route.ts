import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
import { sendMLMNotification } from '@/lib/email-service';

async function checkAdminAccess(userId: string) {
  try {
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]{ isAdmin }`,
      { clerkId: userId }
    );
    return user?.isAdmin === true;
  } catch (error) {
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Admin yetkisi kontrolü
    const isAdmin = await checkAdminAccess(userId);
    if (!isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { emailType = 'monthly_report', targetLevel = 'all' } = body;

    // Hede<PERSON> getir
    let userQuery = `*[_type == "user" && isActive == true]`;
    if (targetLevel !== 'all') {
      userQuery += `[mlmLevel == "${targetLevel}"]`;
    }
    userQuery += `{ _id, name, email, mlmLevel, totalCommissionEarned, directReferrals, totalSales }`;

    const users = await client.fetch(userQuery);

    if (users.length === 0) {
      return NextResponse.json({
        success: false,
        message: 'No users found for the specified criteria'
      });
    }

    let successCount = 0;
    let failureCount = 0;

    // Email gönderme işlemi
    for (const user of users) {
      try {
        if (emailType === 'monthly_report') {
          // Aylık rapor verilerini hazırla
          const currentMonth = new Date().toLocaleDateString('tr-TR', { 
            year: 'numeric', 
            month: 'long' 
          });

          // Kullanıcının aylık komisyonlarını getir
          const monthStart = new Date();
          monthStart.setDate(1);
          monthStart.setHours(0, 0, 0, 0);

          const monthEnd = new Date();
          monthEnd.setMonth(monthEnd.getMonth() + 1);
          monthEnd.setDate(0);
          monthEnd.setHours(23, 59, 59, 999);

          const monthlyCommissions = await client.fetch(`
            *[_type == "mlmCommission" && recipient._ref == $userId && createdAt >= $startDate && createdAt <= $endDate]
          `, { 
            userId: user._id, 
            startDate: monthStart.toISOString(), 
            endDate: monthEnd.toISOString() 
          });

          const monthlyBonuses = await client.fetch(`
            *[_type == "mlmBonus" && recipient._ref == $userId && createdAt >= $startDate && createdAt <= $endDate]
          `, { 
            userId: user._id, 
            startDate: monthStart.toISOString(), 
            endDate: monthEnd.toISOString() 
          });

          const totalCommissions = monthlyCommissions.reduce((sum: number, c: any) => sum + (c.amount || 0), 0);
          const totalBonuses = monthlyBonuses.reduce((sum: number, b: any) => sum + (b.amount || 0), 0);

          // Önceki ay ile karşılaştırma
          const prevMonthStart = new Date(monthStart);
          prevMonthStart.setMonth(prevMonthStart.getMonth() - 1);
          const prevMonthEnd = new Date(monthEnd);
          prevMonthEnd.setMonth(prevMonthEnd.getMonth() - 1);

          const prevMonthReferrals = await client.fetch(`
            *[_type == "user" && sponsor._ref == $userId && membershipStartDate >= $startDate && membershipStartDate <= $endDate]
          `, { 
            userId: user._id, 
            startDate: prevMonthStart.toISOString(), 
            endDate: prevMonthEnd.toISOString() 
          });

          const currentMonthReferrals = await client.fetch(`
            *[_type == "user" && sponsor._ref == $userId && membershipStartDate >= $startDate && membershipStartDate <= $endDate]
          `, { 
            userId: user._id, 
            startDate: monthStart.toISOString(), 
            endDate: monthEnd.toISOString() 
          });

          const teamGrowth = prevMonthReferrals.length > 0 
            ? ((currentMonthReferrals.length - prevMonthReferrals.length) / prevMonthReferrals.length) * 100 
            : currentMonthReferrals.length > 0 ? 100 : 0;

          // Sonraki seviye ilerlemesi (basit hesaplama)
          const levelRequirements = {
            bronze: { requiredReferrals: 3, requiredSales: 5000 },
            silver: { requiredReferrals: 5, requiredSales: 15000 },
            gold: { requiredReferrals: 8, requiredSales: 30000 },
            diamond: { requiredReferrals: 12, requiredSales: 50000 },
            platinum: { requiredReferrals: 20, requiredSales: 100000 },
            sapphire: { requiredReferrals: 0, requiredSales: 0 }
          };

          const nextLevelReq = levelRequirements[user.mlmLevel as keyof typeof levelRequirements];
          const referralProgress = nextLevelReq.requiredReferrals > 0 
            ? Math.min((user.directReferrals / nextLevelReq.requiredReferrals) * 100, 100) 
            : 100;
          const salesProgress = nextLevelReq.requiredSales > 0 
            ? Math.min((user.totalSales / nextLevelReq.requiredSales) * 100, 100) 
            : 100;
          const nextLevelProgress = Math.min((referralProgress + salesProgress) / 2, 100);

          await sendMLMNotification.monthlyReport(
            user.email,
            user.name,
            {
              month: currentMonth,
              totalCommissions,
              totalBonuses,
              newReferrals: currentMonthReferrals.length,
              teamGrowth: Math.round(teamGrowth * 100) / 100,
              nextLevelProgress: Math.round(nextLevelProgress * 100) / 100
            }
          );
        }

        successCount++;
        
        // Rate limiting - emailler arasında kısa bekleme
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (emailError) {
        console.error(`Email sending failed for user ${user.email}:`, emailError);
        failureCount++;
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        totalUsers: users.length,
        successCount,
        failureCount
      },
      message: `Bulk email completed. ${successCount} successful, ${failureCount} failed.`
    });

  } catch (error) {
    console.error('Bulk email error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
