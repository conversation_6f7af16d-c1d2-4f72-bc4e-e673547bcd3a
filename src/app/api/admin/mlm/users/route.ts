import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all MLM users
    const users = await client.fetch(`
      *[_type == "user"] | order(_createdAt desc) {
        _id,
        clerkId,
        name,
        email,
        isAdmin,
        isAdminApproved,
        isSeller,
        walletBalance,
        giftVoucherBalance,
        mlmLevel,
        directReferrals,
        totalTeamMembers,
        totalCommissionEarned,
        monthlyCommissionEarned,
        totalSales,
        membershipStartDate,
        _createdAt
      }
    `);

    return NextResponse.json({ users });
  } catch (error) {
    console.error('Error fetching MLM users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
