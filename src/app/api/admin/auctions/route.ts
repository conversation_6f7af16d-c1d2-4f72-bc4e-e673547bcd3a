import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if user is admin
    const adminUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!adminUser || !adminUser.isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all auctions with detailed information
    const auctions = await client.fetch(`
      *[_type == "auction"] | order(_createdAt desc) {
        _id,
        id,
        name,
        description,
        startingBid,
        currentBid,
        startTime,
        endTime,
        status,
        viewCount,
        image{
          asset->{
            _id,
            url
          }
        },
        "seller": seller-> {
          name,
          email
        },
        "bids": *[_type == "bid" && auction._ref == ^._id] | order(amount desc) {
          _id,
          amount,
          bidTime,
          "bidder": bidder-> {
            name,
            email
          }
        }
      }
    `);

    return NextResponse.json({ auctions });
  } catch (error) {
    console.error('Error fetching auctions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
