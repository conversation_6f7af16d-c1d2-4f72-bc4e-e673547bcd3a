import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

interface RouteParams {
  params: Promise<{
    productId: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    const { productId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get product details
    const product = await client.fetch(
      `*[_type == "product" && (_id == $productId || id == $productId)][0]{
        _id,
        id,
        name,
        description,
        price,
        category,
        status,
        image{
          asset->{
            _id,
            url
          }
        },
        images[]{
          asset->{
            _id,
            url
          }
        },
        seller->{
          _id,
          name,
          email
        },
        featured,
        viewCount,
        _createdAt,
        _updatedAt
      }`,
      { productId }
    );

    if (!product) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product
    });

  } catch (error) {
    console.error('Admin product fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    const { productId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get existing product
    const existingProduct = await client.fetch(
      `*[_type == "product" && (_id == $productId || id == $productId)][0]`,
      { productId }
    );

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      category,
      status,
      images,
      image,
      featured
    } = body;

    // Prepare update data
    const updateData: any = {
      _updatedAt: new Date().toISOString()
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (price !== undefined) updateData.price = price ? parseFloat(price) : null;
    if (category !== undefined) updateData.category = category;
    if (status !== undefined) updateData.status = status;
    if (images !== undefined) updateData.images = images;
    if (image !== undefined) updateData.image = image;
    if (featured !== undefined) updateData.featured = featured;

    // Update product
    const result = await client
      .patch(existingProduct._id)
      .set(updateData)
      .commit();

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Product updated successfully'
    });

  } catch (error) {
    console.error('Admin product update error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    const { productId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get existing product
    const existingProduct = await client.fetch(
      `*[_type == "product" && (_id == $productId || id == $productId)][0]`,
      { productId }
    );

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }

    const body = await request.json();

    // Update product with partial data
    const result = await client
      .patch(existingProduct._id)
      .set({
        ...body,
        _updatedAt: new Date().toISOString()
      })
      .commit();

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Product updated successfully'
    });

  } catch (error) {
    console.error('Admin product patch error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    const { productId } = await params;

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get existing product
    const existingProduct = await client.fetch(
      `*[_type == "product" && (_id == $productId || id == $productId)][0]`,
      { productId }
    );

    if (!existingProduct) {
      return NextResponse.json(
        { success: false, message: 'Product not found' },
        { status: 404 }
      );
    }

    // Delete product
    await client.delete(existingProduct._id);

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    });

  } catch (error) {
    console.error('Admin product delete error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
