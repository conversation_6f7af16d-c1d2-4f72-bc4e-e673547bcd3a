import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    // Get all products with detailed information
    const products = await client.fetch(`
      *[_type == "product"] | order(_createdAt desc) {
        _id,
        id,
        name,
        description,
        price,
        category,
        status,
        image{
          asset->{
            _id,
            url
          }
        },
        images[]{
          asset->{
            _id,
            url
          }
        },
        "seller": seller-> {
          name,
          email
        },
        _createdAt,
        _updatedAt
      }
    `);

    return NextResponse.json({
      success: true,
      data: products
    });

  } catch (error) {
    console.error('Admin products fetch error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user and check admin status
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user || !user.isAdmin) {
      return NextResponse.json(
        { success: false, message: 'Admin access required' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const {
      name,
      description,
      price,
      category,
      status = 'draft',
      images = [],
      image,
      sellerId
    } = body;

    // Validation
    if (!name) {
      return NextResponse.json(
        { success: false, message: 'Product name is required' },
        { status: 400 }
      );
    }

    // Get seller if specified
    let seller = null;
    if (sellerId) {
      seller = await client.fetch(
        `*[_type == "user" && _id == $sellerId][0]`,
        { sellerId }
      );
      
      if (!seller) {
        return NextResponse.json(
          { success: false, message: 'Seller not found' },
          { status: 404 }
        );
      }
    }

    // Generate unique product ID
    const productId = `product_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Create product
    const newProduct = {
      _type: 'product',
      id: productId,
      name,
      description,
      price: price ? parseFloat(price) : undefined,
      category,
      status,
      image,
      images,
      seller: seller ? {
        _type: 'reference',
        _ref: seller._id
      } : undefined,
      featured: false,
      viewCount: 0,
      _createdAt: new Date().toISOString(),
      _updatedAt: new Date().toISOString()
    };

    const result = await client.create(newProduct);

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Product created successfully'
    });

  } catch (error) {
    console.error('Admin product creation error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
