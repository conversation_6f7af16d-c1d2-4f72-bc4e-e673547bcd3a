import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const ownerId = searchParams.get('ownerId');

    if (!ownerId) {
      return NextResponse.json(
        { success: false, message: 'Owner ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcının hediye çeklerini getir (şimdilik mock data)
    const mockGiftVouchers = [
      {
        _id: 'gv1',
        code: 'WELCOME50',
        amount: 50,
        remainingAmount: 50,
        isActive: true,
        isUsed: false,
        expiryDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        sourceType: 'welcome_bonus',
        createdAt: new Date().toISOString()
      },
      {
        _id: 'gv2',
        code: 'LEVEL100',
        amount: 100,
        remainingAmount: 75,
        isActive: true,
        isUsed: false,
        expiryDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
        sourceType: 'level_upgrade_bonus',
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    return NextResponse.json({
      success: true,
      data: {
        giftVouchers: mockGiftVouchers,
        totalBalance: mockGiftVouchers.reduce((sum, gv) => sum + gv.remainingAmount, 0)
      }
    });

  } catch (error) {
    console.error('Gift vouchers GET error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      ownerId, 
      amount, 
      sourceType = 'manual', 
      expiryDays = 30,
      description 
    } = body;

    if (!ownerId || !amount || amount <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid parameters' },
        { status: 400 }
      );
    }

    // Hediye çeki kodu oluştur
    const generateVoucherCode = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };

    const voucherCode = generateVoucherCode();
    const expiryDate = new Date(Date.now() + expiryDays * 24 * 60 * 60 * 1000);

    // Yeni hediye çeki oluştur (şimdilik mock response)
    const newVoucher = {
      _id: `gv_${Date.now()}`,
      code: voucherCode,
      amount,
      remainingAmount: amount,
      isActive: true,
      isUsed: false,
      expiryDate: expiryDate.toISOString(),
      sourceType,
      description: description || 'Manuel hediye çeki',
      createdAt: new Date().toISOString()
    };

    // Kullanıcının hediye çeki bakiyesini güncelle
    await client
      .patch(ownerId)
      .inc({ giftVoucherBalance: amount })
      .commit();

    return NextResponse.json({
      success: true,
      data: {
        voucher: newVoucher
      },
      message: 'Gift voucher created successfully'
    });

  } catch (error) {
    console.error('Gift vouchers POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
