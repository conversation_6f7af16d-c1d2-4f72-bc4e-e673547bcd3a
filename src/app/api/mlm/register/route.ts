import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
import { sendMLMNotification } from '@/lib/email-service';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Kullanıcının MLM kaydını kontrol et
    const existingUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (existingUser) {
      return NextResponse.json({
        success: true,
        user: existingUser,
        message: 'User already registered'
      });
    }

    return NextResponse.json({
      success: false,
      message: 'User not found in MLM system'
    });

  } catch (error) {
    console.error('MLM register GET error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, email, phone, sponsorCode, selectedLevel = 'bronze' } = body;

    // Kullanıcının zaten kayıtlı olup olmadığını kontrol et
    const existingUser = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (existingUser) {
      return NextResponse.json({
        success: false,
        message: 'User already registered in MLM system'
      });
    }

    // Sponsor kontrolü
    let sponsor = null;
    if (sponsorCode) {
      sponsor = await client.fetch(
        `*[_type == "user" && sponsorCode == $sponsorCode][0]`,
        { sponsorCode }
      );

      if (!sponsor) {
        return NextResponse.json({
          success: false,
          message: 'Invalid sponsor code'
        });
      }
    }

    // Benzersiz sponsor kodu oluştur
    const generateSponsorCode = () => {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let result = '';
      for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return result;
    };

    let newSponsorCode = generateSponsorCode();
    
    // Sponsor kodunun benzersiz olduğundan emin ol
    while (true) {
      const existingCode = await client.fetch(
        `*[_type == "user" && sponsorCode == $sponsorCode][0]`,
        { sponsorCode: newSponsorCode }
      );
      
      if (!existingCode) break;
      newSponsorCode = generateSponsorCode();
    }

    // Yeni kullanıcı oluştur
    const newUser = {
      _type: 'user',
      clerkId: userId,
      name,
      email,
      phone,
      mlmLevel: selectedLevel,
      sponsorCode: newSponsorCode,
      walletBalance: 0,
      totalCommissionEarned: 0,
      monthlyCommissionEarned: 0,
      totalSales: 0,
      totalTeamSales: 0,
      directReferrals: 0,
      totalTeamMembers: 0,
      membershipStartDate: new Date().toISOString(),
      isActive: true,
      giftVoucherBalance: 0,
      isAdminApproved: false,
      isAdmin: false,
    };

    // Sponsor varsa referans ilişkisini kur
    if (sponsor) {
      newUser.sponsor = {
        _type: 'reference',
        _ref: sponsor._id
      };
    }

    // Kullanıcıyı kaydet
    const createdUser = await client.create(newUser);

    // Sponsor varsa sponsor'un referans sayısını artır
    if (sponsor) {
      await client
        .patch(sponsor._id)
        .inc({ directReferrals: 1 })
        .commit();

      // Binary tree pozisyonu belirle (basit algoritma)
      const leftChild = await client.fetch(
        `*[_type == "user" && references($sponsorId) in sponsor._ref && defined(leftChild)][0]`,
        { sponsorId: sponsor._id }
      );

      const rightChild = await client.fetch(
        `*[_type == "user" && references($sponsorId) in sponsor._ref && defined(rightChild)][0]`,
        { sponsorId: sponsor._id }
      );

      // Sol pozisyon boşsa sol tarafa, değilse sağ tarafa yerleştir
      if (!leftChild) {
        await client
          .patch(sponsor._id)
          .set({
            leftChild: {
              _type: 'reference',
              _ref: createdUser._id
            }
          })
          .commit();
      } else if (!rightChild) {
        await client
          .patch(sponsor._id)
          .set({
            rightChild: {
              _type: 'reference',
              _ref: createdUser._id
            }
          })
          .commit();
      }
    }

    // Hoş geldin bonusu ver
    const welcomeBonus = {
      _type: 'mlmBonus',
      recipient: {
        _type: 'reference',
        _ref: createdUser._id
      },
      bonusType: 'welcome_bonus',
      amount: 50, // 50 TL hoş geldin bonusu
      paymentType: 'gift_voucher',
      status: 'approved',
      description: 'MLM sistemine hoş geldin bonusu',
      createdAt: new Date().toISOString()
    };

    await client.create(welcomeBonus);

    // Kullanıcının hediye çeki bakiyesini güncelle
    await client
      .patch(createdUser._id)
      .inc({ giftVoucherBalance: 50 })
      .commit();

    // Hoş geldin email'i gönder
    try {
      await sendMLMNotification.welcomeBonus(email, name, 50, sponsor?.name);
    } catch (emailError) {
      console.error('Welcome email sending failed:', emailError);
      // Email hatası kayıt işlemini durdurmaz
    }

    return NextResponse.json({
      success: true,
      user: createdUser,
      message: 'MLM registration successful'
    });

  } catch (error) {
    console.error('MLM register POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
