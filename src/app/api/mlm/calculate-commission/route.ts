import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
// import { sendMLMNotification } from '@/lib/email-service';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');
    const type = searchParams.get('type') || 'received';

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcının komisyonlarını getir
    let query = '';
    if (type === 'received') {
      query = `*[_type == "mlmCommission" && recipient._ref == $userId] | order(createdAt desc)`;
    } else if (type === 'given') {
      query = `*[_type == "mlmCommission" && source._ref == $userId] | order(createdAt desc)`;
    }

    const commissions = await client.fetch(query, { userId: targetUserId });

    // Komisyon istatistikleri
    const totalAmount = commissions.reduce((sum: number, c: any) => sum + (c.amount || 0), 0);
    const paidAmount = commissions
      .filter((c: any) => c.status === 'paid')
      .reduce((sum: number, c: any) => sum + (c.amount || 0), 0);
    const pendingAmount = commissions
      .filter((c: any) => c.status === 'pending' || c.status === 'approved')
      .reduce((sum: number, c: any) => sum + (c.amount || 0), 0);

    return NextResponse.json({
      success: true,
      data: {
        commissions,
        stats: {
          total: totalAmount,
          paid: paidAmount,
          pending: pendingAmount,
          count: commissions.length
        }
      }
    });

  } catch (error) {
    console.error('Calculate commission error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      targetUserId, 
      sourceUserId, 
      commissionType, 
      baseAmount, 
      percentage, 
      level = 1,
      relatedOrderId,
      description 
    } = body;

    if (!targetUserId || !sourceUserId || !commissionType || !baseAmount || !percentage) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Komisyon tutarını hesapla
    const commissionAmount = (baseAmount * percentage) / 100;

    // Yeni komisyon oluştur
    const newCommission = {
      _type: 'mlmCommission',
      recipient: {
        _type: 'reference',
        _ref: targetUserId
      },
      source: {
        _type: 'reference',
        _ref: sourceUserId
      },
      commissionType,
      amount: commissionAmount,
      percentage,
      level,
      baseAmount,
      status: 'pending',
      paymentMethod: 'wallet',
      createdAt: new Date().toISOString(),
      description: description || `${commissionType} komisyonu`
    };

    if (relatedOrderId) {
      newCommission.relatedOrder = {
        _type: 'reference',
        _ref: relatedOrderId
      };
    }

    const createdCommission = await client.create(newCommission);

    // Komisyon email bildirimi gönder
    try {
      // Alıcı ve kaynak kullanıcı bilgilerini getir
      const [recipient, sourceUser] = await Promise.all([
        client.fetch(`*[_type == "user" && _id == $id][0]{ name, email, totalCommissionEarned }`, { id: targetUserId }),
        client.fetch(`*[_type == "user" && _id == $id][0]{ name }`, { id: sourceUserId })
      ]);

      if (recipient && sourceUser) {
        console.log(`📧 Commission email would be sent to ${recipient.email} for ₺${commissionAmount} ${commissionType} from ${sourceUser.name}`);
        // await sendMLMNotification.commissionEarned(recipient.email, recipient.name, commissionAmount, commissionType, sourceUser.name, recipient.totalCommissionEarned + commissionAmount);
      }
    } catch (emailError) {
      console.error('Commission email sending failed:', emailError);
      // Email hatası komisyon işlemini durdurmaz
    }

    return NextResponse.json({
      success: true,
      data: {
        commission: createdCommission,
        amount: commissionAmount
      },
      message: 'Commission calculated and created successfully'
    });

  } catch (error) {
    console.error('Calculate commission POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
