import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
// import { sendMLMNotification } from '@/lib/email-service';

const MLM_LEVELS = ['bronze', 'silver', 'gold', 'diamond', 'platinum', 'sapphire'];

const LEVEL_DATA = {
  bronze: {
    title: 'Bronz',
    directCommissionRate: 10,
    giftVoucherAmount: 50,
    monthlyWalletBonus: 25,
    membershipFee: 0,
    upgradeRequirements: { requiredReferrals: 0, requiredSalesVolume: 0, timeLimit: 0 }
  },
  silver: {
    title: 'Gümüş',
    directCommissionRate: 15,
    giftVoucherAmount: 100,
    monthlyWalletBonus: 50,
    membershipFee: 500,
    upgradeRequirements: { requiredReferrals: 3, requiredSalesVolume: 5000, timeLimit: 90 }
  },
  gold: {
    title: 'Altın',
    directCommissionRate: 20,
    giftVoucherAmount: 250,
    monthlyWalletBonus: 100,
    membershipFee: 1500,
    upgradeRequirements: { requiredReferrals: 5, requiredSalesVolume: 15000, timeLimit: 120 }
  },
  diamond: {
    title: '<PERSON><PERSON>',
    directCommissionRate: 25,
    giftVoucherAmount: 500,
    monthlyWalletBonus: 200,
    membershipFee: 3000,
    upgradeRequirements: { requiredReferrals: 8, requiredSalesVolume: 30000, timeLimit: 150 }
  },
  platinum: {
    title: 'Pırlanta',
    directCommissionRate: 30,
    giftVoucherAmount: 1000,
    monthlyWalletBonus: 400,
    membershipFee: 5000,
    upgradeRequirements: { requiredReferrals: 12, requiredSalesVolume: 50000, timeLimit: 180 }
  },
  sapphire: {
    title: 'Safir',
    directCommissionRate: 35,
    giftVoucherAmount: 2000,
    monthlyWalletBonus: 800,
    membershipFee: 10000,
    upgradeRequirements: { requiredReferrals: 20, requiredSalesVolume: 100000, timeLimit: 365 }
  }
};

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcıyı getir
    const user = await client.fetch(`
      *[_type == "user" && _id == $targetUserId][0]
    `, { targetUserId });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    const currentLevelIndex = MLM_LEVELS.indexOf(user.mlmLevel);
    const isMaxLevel = currentLevelIndex === MLM_LEVELS.length - 1;

    if (isMaxLevel) {
      return NextResponse.json({
        success: true,
        isMaxLevel: true,
        currentLevel: {
          level: user.mlmLevel,
          title: LEVEL_DATA[user.mlmLevel as keyof typeof LEVEL_DATA].title,
          data: LEVEL_DATA[user.mlmLevel as keyof typeof LEVEL_DATA]
        }
      });
    }

    const nextLevel = MLM_LEVELS[currentLevelIndex + 1];
    const currentLevelData = LEVEL_DATA[user.mlmLevel as keyof typeof LEVEL_DATA];
    const nextLevelData = LEVEL_DATA[nextLevel as keyof typeof LEVEL_DATA];

    // Üyelik tarihinden bu yana geçen gün sayısı
    const membershipDate = new Date(user.membershipStartDate);
    const daysSinceMembership = Math.floor((Date.now() - membershipDate.getTime()) / (1000 * 60 * 60 * 24));

    // Şartları kontrol et
    const requirements = nextLevelData.upgradeRequirements;
    const hasEnoughReferrals = user.directReferrals >= requirements.requiredReferrals;
    const hasEnoughSales = user.totalSales >= requirements.requiredSalesVolume;
    const withinTimeLimit = requirements.timeLimit === 0 || daysSinceMembership <= requirements.timeLimit;

    const canUpgrade = hasEnoughReferrals && hasEnoughSales && withinTimeLimit;
    const canUpgradeByPayment = user.walletBalance >= nextLevelData.membershipFee;

    return NextResponse.json({
      success: true,
      canUpgrade,
      currentLevel: {
        level: user.mlmLevel,
        title: currentLevelData.title,
        data: currentLevelData
      },
      nextLevel: {
        level: nextLevel,
        title: nextLevelData.title,
        data: nextLevelData
      },
      requirements: {
        requiredReferrals: requirements.requiredReferrals,
        requiredSalesVolume: requirements.requiredSalesVolume,
        timeLimit: requirements.timeLimit,
        hasEnoughReferrals,
        hasEnoughSales,
        withinTimeLimit
      },
      currentStats: {
        directReferrals: user.directReferrals,
        totalSales: user.totalSales,
        daysSinceMembership
      },
      upgradeFee: nextLevelData.membershipFee,
      canUpgradeByPayment
    });

  } catch (error) {
    console.error('Level upgrade GET error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetUserId, upgradeMethod, paymentMethod = 'wallet' } = body;

    if (!targetUserId || !upgradeMethod) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Kullanıcıyı getir
    const user = await client.fetch(`
      *[_type == "user" && _id == $targetUserId][0]
    `, { targetUserId });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    const currentLevelIndex = MLM_LEVELS.indexOf(user.mlmLevel);
    const isMaxLevel = currentLevelIndex === MLM_LEVELS.length - 1;

    if (isMaxLevel) {
      return NextResponse.json(
        { success: false, message: 'User is already at maximum level' },
        { status: 400 }
      );
    }

    const nextLevel = MLM_LEVELS[currentLevelIndex + 1];
    const nextLevelData = LEVEL_DATA[nextLevel as keyof typeof LEVEL_DATA];

    // Yükseltme yöntemini kontrol et
    if (upgradeMethod === 'requirements') {
      // Şartları kontrol et
      const membershipDate = new Date(user.membershipStartDate);
      const daysSinceMembership = Math.floor((Date.now() - membershipDate.getTime()) / (1000 * 60 * 60 * 24));
      
      const requirements = nextLevelData.upgradeRequirements;
      const hasEnoughReferrals = user.directReferrals >= requirements.requiredReferrals;
      const hasEnoughSales = user.totalSales >= requirements.requiredSalesVolume;
      const withinTimeLimit = requirements.timeLimit === 0 || daysSinceMembership <= requirements.timeLimit;

      if (!hasEnoughReferrals || !hasEnoughSales || !withinTimeLimit) {
        return NextResponse.json(
          { success: false, message: 'Requirements not met for upgrade' },
          { status: 400 }
        );
      }
    } else if (upgradeMethod === 'payment') {
      // Ödeme kontrolü
      if (user.walletBalance < nextLevelData.membershipFee) {
        return NextResponse.json(
          { success: false, message: 'Insufficient wallet balance' },
          { status: 400 }
        );
      }

      // Ödemeyi düş
      await client
        .patch(targetUserId)
        .dec({ walletBalance: nextLevelData.membershipFee })
        .commit();
    }

    // Seviyeyi yükselt
    await client
      .patch(targetUserId)
      .set({ 
        mlmLevel: nextLevel,
        lastLevelUpgrade: new Date().toISOString()
      })
      .inc({ giftVoucherBalance: nextLevelData.giftVoucherAmount })
      .commit();

    // Seviye yükseltme bonusu ver
    const upgradeBonus = {
      _type: 'mlmBonus',
      recipient: {
        _type: 'reference',
        _ref: targetUserId
      },
      bonusType: 'level_upgrade_bonus',
      amount: nextLevelData.giftVoucherAmount,
      paymentType: 'gift_voucher',
      status: 'paid',
      description: `${nextLevelData.title} seviyesine yükseltme bonusu`,
      createdAt: new Date().toISOString(),
      paidAt: new Date().toISOString()
    };

    await client.create(upgradeBonus);

    // Seviye yükseltme email bildirimi gönder (şimdilik devre dışı)
    try {
      const currentLevelData = LEVEL_DATA[user.mlmLevel as keyof typeof LEVEL_DATA];
      console.log(`📧 Level upgrade email would be sent to ${user.email} for ${currentLevelData.title} → ${nextLevelData.title}`);
      // await sendMLMNotification.levelUpgrade(user.email, user.name, currentLevelData.title, nextLevelData.title, nextLevelData.giftVoucherAmount, nextLevelData.benefits || []);
    } catch (emailError) {
      console.error('Level upgrade email sending failed:', emailError);
      // Email hatası yükseltme işlemini durdurmaz
    }

    return NextResponse.json({
      success: true,
      message: `Successfully upgraded to ${nextLevelData.title} level`,
      newLevel: nextLevel,
      bonus: nextLevelData.giftVoucherAmount
    });

  } catch (error) {
    console.error('Level upgrade POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
