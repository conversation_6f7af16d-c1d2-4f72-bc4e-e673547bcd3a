import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { seedMLMData } from '@/scripts/seed-mlm-data';

export async function POST() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Sadece development ortamında çalışsın
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { success: false, message: 'Seeding only allowed in development' },
        { status: 403 }
      );
    }

    const result = await seedMLMData();

    return NextResponse.json(result);

  } catch (error) {
    console.error('Seed API error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
