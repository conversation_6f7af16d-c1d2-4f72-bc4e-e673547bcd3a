import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
// import { sendMLMNotification } from '@/lib/email-service';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');
    const month = searchParams.get('month') || new Date().toISOString().slice(0, 7); // YYYY-MM format

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Ay başı ve sonu tarihlerini hesapla
    const startDate = new Date(`${month}-01T00:00:00.000Z`);
    const endDate = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0, 23, 59, 59, 999);

    // Kullanıcı bilgilerini getir
    const user = await client.fetch(`
      *[_type == "user" && _id == $userId][0]
    `, { userId: targetUserId });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Aylık komisyonları getir
    const monthlyCommissions = await client.fetch(`
      *[_type == "mlmCommission" && recipient._ref == $userId && createdAt >= $startDate && createdAt <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString() 
    });

    // Aylık bonusları getir
    const monthlyBonuses = await client.fetch(`
      *[_type == "mlmBonus" && recipient._ref == $userId && createdAt >= $startDate && createdAt <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString() 
    });

    // Aylık yeni referansları getir
    const monthlyReferrals = await client.fetch(`
      *[_type == "user" && sponsor._ref == $userId && membershipStartDate >= $startDate && membershipStartDate <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString() 
    });

    // Önceki ay verilerini getir (karşılaştırma için)
    const prevMonth = new Date(startDate);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    const prevStartDate = new Date(prevMonth.getFullYear(), prevMonth.getMonth(), 1);
    const prevEndDate = new Date(prevMonth.getFullYear(), prevMonth.getMonth() + 1, 0, 23, 59, 59, 999);

    const prevMonthReferrals = await client.fetch(`
      *[_type == "user" && sponsor._ref == $userId && membershipStartDate >= $startDate && membershipStartDate <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: prevStartDate.toISOString(), 
      endDate: prevEndDate.toISOString() 
    });

    // İstatistikleri hesapla
    const totalCommissions = monthlyCommissions.reduce((sum: number, c: any) => sum + (c.amount || 0), 0);
    const totalBonuses = monthlyBonuses.reduce((sum: number, b: any) => sum + (b.amount || 0), 0);
    const newReferrals = monthlyReferrals.length;
    const teamGrowth = prevMonthReferrals.length > 0 
      ? ((newReferrals - prevMonthReferrals.length) / prevMonthReferrals.length) * 100 
      : newReferrals > 0 ? 100 : 0;

    // Sonraki seviye ilerlemesini hesapla
    const currentLevel = user.mlmLevel;
    const levelRequirements = {
      bronze: { requiredReferrals: 3, requiredSales: 5000 },
      silver: { requiredReferrals: 5, requiredSales: 15000 },
      gold: { requiredReferrals: 8, requiredSales: 30000 },
      diamond: { requiredReferrals: 12, requiredSales: 50000 },
      platinum: { requiredReferrals: 20, requiredSales: 100000 },
      sapphire: { requiredReferrals: 0, requiredSales: 0 } // Max level
    };

    const nextLevelReq = levelRequirements[currentLevel as keyof typeof levelRequirements];
    const referralProgress = nextLevelReq.requiredReferrals > 0 
      ? Math.min((user.directReferrals / nextLevelReq.requiredReferrals) * 100, 100) 
      : 100;
    const salesProgress = nextLevelReq.requiredSales > 0 
      ? Math.min((user.totalSales / nextLevelReq.requiredSales) * 100, 100) 
      : 100;
    const nextLevelProgress = Math.min((referralProgress + salesProgress) / 2, 100);

    const monthlyReport = {
      user: {
        name: user.name,
        email: user.email,
        level: user.mlmLevel
      },
      period: {
        month,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      },
      performance: {
        totalCommissions,
        totalBonuses,
        totalEarnings: totalCommissions + totalBonuses,
        newReferrals,
        teamGrowth: Math.round(teamGrowth * 100) / 100,
        nextLevelProgress: Math.round(nextLevelProgress * 100) / 100
      },
      breakdown: {
        commissions: monthlyCommissions,
        bonuses: monthlyBonuses,
        referrals: monthlyReferrals
      },
      insights: {
        topCommissionType: monthlyCommissions.length > 0 
          ? monthlyCommissions.reduce((prev: any, current: any) => 
              (prev.amount > current.amount) ? prev : current
            ).commissionType 
          : null,
        averageCommission: monthlyCommissions.length > 0 
          ? totalCommissions / monthlyCommissions.length 
          : 0,
        isGrowthMonth: teamGrowth > 0,
        canUpgradeLevel: nextLevelProgress >= 100 && currentLevel !== 'sapphire'
      }
    };

    return NextResponse.json({
      success: true,
      data: monthlyReport
    });

  } catch (error) {
    console.error('Monthly report error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetUserId, month, sendEmail = false } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Raporu getir
    const reportResponse = await fetch(`${process.env.NEXT_PUBLIC_APP_URL}/api/mlm/monthly-report?userId=${targetUserId}&month=${month}`);
    const reportData = await reportResponse.json();

    if (!reportData.success) {
      return NextResponse.json(reportData);
    }

    const report = reportData.data;

    // Email gönder (şimdilik devre dışı)
    if (sendEmail) {
      try {
        console.log(`📧 Monthly report email would be sent to ${report.user.email}`);
        console.log('Report data:', {
          month: report.period.month,
          totalCommissions: report.performance.totalCommissions,
          totalBonuses: report.performance.totalBonuses,
          newReferrals: report.performance.newReferrals,
          teamGrowth: report.performance.teamGrowth,
          nextLevelProgress: report.performance.nextLevelProgress
        });
        
        // await sendMLMNotification.monthlyReport(
        //   report.user.email,
        //   report.user.name,
        //   {
        //     month: report.period.month,
        //     totalCommissions: report.performance.totalCommissions,
        //     totalBonuses: report.performance.totalBonuses,
        //     newReferrals: report.performance.newReferrals,
        //     teamGrowth: report.performance.teamGrowth,
        //     nextLevelProgress: report.performance.nextLevelProgress
        //   }
        // );
      } catch (emailError) {
        console.error('Monthly report email sending failed:', emailError);
        // Email hatası rapor oluşturmayı durdurmaz
      }
    }

    return NextResponse.json({
      success: true,
      data: report,
      message: sendEmail ? 'Monthly report generated and email sent' : 'Monthly report generated'
    });

  } catch (error) {
    console.error('Monthly report POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
