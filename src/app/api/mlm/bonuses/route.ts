import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';
// import { sendMLMNotification } from '@/lib/email-service';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcının bonuslarını getir
    const bonuses = await client.fetch(`
      *[_type == "mlmBonus" && recipient._ref == $userId] | order(createdAt desc)
    `, { userId: targetUserId });

    return NextResponse.json({
      success: true,
      data: {
        bonuses
      }
    });

  } catch (error) {
    console.error('Bonuses GET error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      targetUserId, 
      bonusType, 
      manualAmount, 
      description 
    } = body;

    if (!targetUserId || !bonusType) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Kullanıcıyı getir
    const user = await client.fetch(`
      *[_type == "user" && _id == $targetUserId][0]
    `, { targetUserId });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Bonus tutarını hesapla
    let bonusAmount = manualAmount || 0;
    let paymentType = 'cash';

    switch (bonusType) {
      case 'welcome_bonus':
        bonusAmount = 50;
        paymentType = 'gift_voucher';
        break;
      case 'activity_bonus':
        bonusAmount = user.totalSales * 0.01; // %1 aktivite bonusu
        break;
      case 'volume_bonus':
        bonusAmount = user.totalTeamSales * 0.005; // %0.5 ciro bonusu
        break;
      case 'leadership_bonus':
        bonusAmount = user.directReferrals * 25; // Referans başına 25 TL
        break;
      case 'level_upgrade_bonus':
        const levelBonuses = {
          bronze: 0,
          silver: 100,
          gold: 250,
          diamond: 500,
          platinum: 1000,
          sapphire: 2000
        };
        bonusAmount = levelBonuses[user.mlmLevel as keyof typeof levelBonuses] || 0;
        paymentType = 'gift_voucher';
        break;
      case 'fast_start_bonus':
        bonusAmount = 150;
        break;
      case 'manual_bonus':
        bonusAmount = manualAmount || 0;
        break;
      default:
        bonusAmount = manualAmount || 0;
    }

    if (bonusAmount <= 0) {
      return NextResponse.json(
        { success: false, message: 'Invalid bonus amount' },
        { status: 400 }
      );
    }

    // Yeni bonus oluştur
    const newBonus = {
      _type: 'mlmBonus',
      recipient: {
        _type: 'reference',
        _ref: targetUserId
      },
      bonusType,
      amount: bonusAmount,
      paymentType,
      status: 'approved',
      description: description || `${bonusType} bonusu`,
      createdAt: new Date().toISOString(),
      paidAt: new Date().toISOString()
    };

    const createdBonus = await client.create(newBonus);

    // Kullanıcının bakiyesini güncelle
    if (paymentType === 'cash') {
      await client
        .patch(targetUserId)
        .inc({
          walletBalance: bonusAmount,
          totalCommissionEarned: bonusAmount,
          monthlyCommissionEarned: bonusAmount
        })
        .commit();
    } else if (paymentType === 'gift_voucher') {
      await client
        .patch(targetUserId)
        .inc({ giftVoucherBalance: bonusAmount })
        .commit();
    }

    // Bonus email bildirimi gönder (welcome_bonus hariç, çünkü o zaten register'da gönderiliyor)
    if (bonusType !== 'welcome_bonus') {
      try {
        // Bonus türüne göre email gönder
        console.log(`📧 Sending bonus notification email for ${bonusType} to ${user.email}`);
        // Şimdilik console log, gerçek email sistemi için gerekli template'ler eklenebilir
      } catch (emailError) {
        console.error('Bonus email sending failed:', emailError);
        // Email hatası bonus işlemini durdurmaz
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        bonus: createdBonus,
        amount: bonusAmount
      },
      message: 'Bonus successfully given'
    });

  } catch (error) {
    console.error('Bonuses POST error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetUserId } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcıyı getir
    const user = await client.fetch(`
      *[_type == "user" && _id == $targetUserId][0]
    `, { targetUserId });

    if (!user) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    // Uygun bonusları kontrol et
    const eligibleBonuses = [];

    // Aktivite bonusu kontrolü
    if (user.totalSales >= 1000) {
      eligibleBonuses.push({
        bonusType: 'activity_bonus',
        amount: user.totalSales * 0.01,
        description: 'Aylık aktivite bonusu - satış performansı'
      });
    }

    // Liderlik bonusu kontrolü
    if (user.directReferrals >= 5) {
      eligibleBonuses.push({
        bonusType: 'leadership_bonus',
        amount: user.directReferrals * 25,
        description: 'Liderlik bonusu - takım büyütme'
      });
    }

    // Ciro bonusu kontrolü
    if (user.totalTeamSales >= 10000) {
      eligibleBonuses.push({
        bonusType: 'volume_bonus',
        amount: user.totalTeamSales * 0.005,
        description: 'Ciro bonusu - takım satış performansı'
      });
    }

    // Hızlı başlangıç bonusu kontrolü
    const membershipDate = new Date(user.membershipStartDate);
    const daysSinceMembership = Math.floor((Date.now() - membershipDate.getTime()) / (1000 * 60 * 60 * 24));
    
    if (daysSinceMembership <= 30 && user.directReferrals >= 3) {
      eligibleBonuses.push({
        bonusType: 'fast_start_bonus',
        amount: 150,
        description: 'Hızlı başlangıç bonusu - ilk 30 gün'
      });
    }

    return NextResponse.json({
      success: true,
      eligibleBonuses
    });

  } catch (error) {
    console.error('Bonuses PATCH error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
