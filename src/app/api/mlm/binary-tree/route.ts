import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const targetUserId = searchParams.get('userId');

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Kullanıcıyı ve alt ağacını getir
    const buildTree = async (userId: string, depth = 0, maxDepth = 5): Promise<any> => {
      if (depth > maxDepth) return null;

      const user = await client.fetch(`
        *[_type == "user" && _id == $userId][0]{
          _id,
          name,
          email,
          mlmLevel,
          sponsorCode,
          totalCommissionEarned,
          directReferrals,
          totalTeamMembers,
          totalSales,
          membershipStartDate,
          isActive,
          leftChild->{_id},
          rightChild->{_id}
        }
      `, { userId });

      if (!user) return null;

      const treeNode = {
        ...user,
        depth,
        leftChild: null,
        rightChild: null
      };

      // Sol ve sağ çocukları recursive olarak getir
      if (user.leftChild?._id) {
        treeNode.leftChild = await buildTree(user.leftChild._id, depth + 1, maxDepth);
      }
      if (user.rightChild?._id) {
        treeNode.rightChild = await buildTree(user.rightChild._id, depth + 1, maxDepth);
      }

      return treeNode;
    };

    const tree = await buildTree(targetUserId);

    if (!tree) {
      return NextResponse.json(
        { success: false, message: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      tree
    });

  } catch (error) {
    console.error('Binary tree GET error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { targetUserId, period = 'month' } = body;

    if (!targetUserId) {
      return NextResponse.json(
        { success: false, message: 'User ID is required' },
        { status: 400 }
      );
    }

    // Tarih aralığını hesapla
    const now = new Date();
    let startDate: Date;
    let endDate = now;

    switch (period) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'quarter':
        const quarterStart = Math.floor(now.getMonth() / 3) * 3;
        startDate = new Date(now.getFullYear(), quarterStart, 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    }

    // Komisyon verilerini getir
    const commissions = await client.fetch(`
      *[_type == "mlmCommission" && recipient._ref == $userId && createdAt >= $startDate && createdAt <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString() 
    });

    // Yeni üyelikleri getir
    const newMemberships = await client.fetch(`
      *[_type == "user" && sponsor._ref == $userId && membershipStartDate >= $startDate && membershipStartDate <= $endDate]
    `, { 
      userId: targetUserId, 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString() 
    });

    // Takım satışlarını getir (mock data)
    const teamSales = {
      total: 15000,
      count: 25
    };

    // Komisyon analizi
    const commissionsByType = commissions.reduce((acc: any, commission: any) => {
      acc[commission.commissionType] = (acc[commission.commissionType] || 0) + commission.amount;
      return acc;
    }, {});

    // Üyelik analizi
    const membershipsByLevel = newMemberships.reduce((acc: any, member: any) => {
      acc[member.mlmLevel] = (acc[member.mlmLevel] || 0) + 1;
      return acc;
    }, {});

    const analytics = {
      period,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      commissions: {
        total: commissions.reduce((sum: number, c: any) => sum + c.amount, 0),
        count: commissions.length,
        byType: commissionsByType
      },
      newMemberships: {
        total: newMemberships.length,
        revenue: newMemberships.reduce((sum: number, m: any) => sum + (m.membershipFee || 0), 0),
        byLevel: membershipsByLevel
      },
      teamSales
    };

    return NextResponse.json({
      success: true,
      analytics
    });

  } catch (error) {
    console.error('Binary tree PATCH error:', error);
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    );
  }
}
