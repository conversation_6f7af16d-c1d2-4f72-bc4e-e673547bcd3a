import { NextRequest, NextResponse } from 'next/server';
import { cdnClient } from '@/lib/sanity';
import { client } from '@/sanity/lib/client';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const simple = searchParams.get('simple') === 'true';

    if (simple) {
      // Simple category list for forms
      const categories = await client.fetch(`
        *[_type == "category"] | order(name asc) {
          _id,
          name,
          description
        }
      `);

      return NextResponse.json({
        success: true,
        categories,
        message: `Fetched ${categories.length} categories`
      });
    }

    // Original detailed query with product counts
    const query = `{
      "categories": [
        {
          "name": "electronics",
          "count": count(*[_type == "product" && status == "active" && category == "electronics"]),
          "products": *[_type == "product" && status == "active" && category == "electronics"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        },
        {
          "name": "clothing",
          "count": count(*[_type == "product" && status == "active" && category == "clothing"]),
          "products": *[_type == "product" && status == "active" && category == "clothing"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        },
        {
          "name": "home-garden",
          "count": count(*[_type == "product" && status == "active" && category == "home-garden"]),
          "products": *[_type == "product" && status == "active" && category == "home-garden"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        },
        {
          "name": "sports",
          "count": count(*[_type == "product" && status == "active" && category == "sports"]),
          "products": *[_type == "product" && status == "active" && category == "sports"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        },
        {
          "name": "books",
          "count": count(*[_type == "product" && status == "active" && category == "books"]),
          "products": *[_type == "product" && status == "active" && category == "books"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        },
        {
          "name": "automotive",
          "count": count(*[_type == "product" && status == "active" && category == "automotive"]),
          "products": *[_type == "product" && status == "active" && category == "automotive"][0...3]{
            _id,
            name,
            price,
            "imageUrl": images[0].asset->url
          }
        }
      ]
    }`;

    console.log('Fetching categories from Sanity...');

    const result = await cdnClient.fetch(query);
    const categories = result.categories || [];

    console.log(`Fetched ${categories.length} categories from Sanity`);

    const response = NextResponse.json({
      success: true,
      data: categories,
      message: `Fetched ${categories.length} categories from Sanity CMS`
    });

    response.headers.set('Cache-Control', 's-maxage=300, stale-while-revalidate');

    return response;

  } catch (error) {
    console.error('Categories API error:', error);

    return NextResponse.json({
      success: false,
      error: 'Failed to fetch categories from Sanity',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
