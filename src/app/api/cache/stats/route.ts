import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Return basic cache stats with cache headers
    const response = NextResponse.json({
      success: true,
      stats: {
        hits: 0,
        misses: 0,
        size: 0,
        keys: []
      },
      message: 'Cache stats retrieved successfully'
    });

    // Cache for 30 seconds to reduce frequent calls
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');

    return response;
  } catch (error) {
    console.error('Error fetching cache stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cache stats' },
      { status: 500 }
    );
  }
}
