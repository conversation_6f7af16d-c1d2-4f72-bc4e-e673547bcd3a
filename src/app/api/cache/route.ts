import { NextRequest, NextResponse } from 'next/server';
import { apiCache, invalidateCache } from '@/lib/cache';

// Cache management API endpoint
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    switch (action) {
      case 'stats':
        const stats = apiCache.getStats();
        return NextResponse.json({
          success: true,
          data: stats,
          message: 'Cache statistics retrieved'
        });

      case 'clear':
        const pattern = searchParams.get('pattern');
        if (pattern) {
          const deletedCount = invalidateCache(pattern);
          return NextResponse.json({
            success: true,
            data: { deletedCount },
            message: `Cleared ${deletedCount} cache entries matching pattern: ${pattern}`
          });
        } else {
          apiCache.clear();
          return NextResponse.json({
            success: true,
            message: 'All cache cleared'
          });
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use ?action=stats or ?action=clear'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Cache API error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to manage cache',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// Webhook endpoint for cache invalidation (e.g., from Sanity)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, pattern } = body;

    switch (type) {
      case 'product_updated':
      case 'product_created':
      case 'product_deleted':
        // Invalidate product-related caches
        const productDeleted = invalidateCache('products');
        const categoryDeleted = invalidateCache('categories');
        const searchDeleted = invalidateCache('search');
        
        return NextResponse.json({
          success: true,
          data: {
            productCacheCleared: productDeleted,
            categoryCacheCleared: categoryDeleted,
            searchCacheCleared: searchDeleted
          },
          message: 'Product-related caches invalidated'
        });

      case 'vendor_updated':
        // Invalidate vendor-related caches
        const vendorDeleted = invalidateCache('vendor');
        
        return NextResponse.json({
          success: true,
          data: { vendorCacheCleared: vendorDeleted },
          message: 'Vendor-related caches invalidated'
        });

      case 'custom':
        if (pattern) {
          const deletedCount = invalidateCache(pattern);
          return NextResponse.json({
            success: true,
            data: { deletedCount },
            message: `Custom cache invalidation: ${deletedCount} entries cleared`
          });
        }
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid invalidation type'
        }, { status: 400 });
    }

  } catch (error) {
    console.error('Cache invalidation error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to invalidate cache',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
