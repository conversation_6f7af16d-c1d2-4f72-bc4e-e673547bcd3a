import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { amount } = await req.json();

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    if (parseFloat(amount) < 10) {
      return NextResponse.json(
        { error: 'Minimum amount is 10 TL' },
        { status: 400 }
      );
    }

    // Get user
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create transaction record (for demo purposes, mark as completed immediately)
    const transaction = await client.create({
      _type: 'walletTransaction',
      user: {
        _type: 'reference',
        _ref: user._id
      },
      type: 'deposit',
      amount: parseFloat(amount),
      description: `Cüzdana ${amount} TL yüklendi (Demo)`,
      status: 'completed',
      _createdAt: new Date().toISOString()
    });

    // Update user wallet balance
    const newBalance = (user.walletBalance || 0) + parseFloat(amount);
    await client
      .patch(user._id)
      .set({ walletBalance: newBalance })
      .commit();

    return NextResponse.json({
      success: true,
      transaction,
      newBalance,
      message: 'Funds added successfully (Demo mode)'
    });

  } catch (error) {
    console.error('Error adding funds:', error);
    
    let errorMessage = 'Internal server error';
    if (error instanceof Error) {
      errorMessage = error.message;
      console.error('Detailed error:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }
    
    return NextResponse.json(
      { 
        error: errorMessage,
        details: process.env.NODE_ENV === 'development' ? error : undefined
      },
      { status: 500 }
    );
  }
}
