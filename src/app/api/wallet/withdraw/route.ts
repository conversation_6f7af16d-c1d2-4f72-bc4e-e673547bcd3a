import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { amount } = await req.json();

    if (!amount || amount <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount' },
        { status: 400 }
      );
    }

    // Get user
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user has sufficient balance
    const currentBalance = user.walletBalance || 0;
    if (currentBalance < parseFloat(amount)) {
      return NextResponse.json(
        { error: 'Insufficient balance' },
        { status: 400 }
      );
    }

    // Create transaction record
    const transaction = await client.create({
      _type: 'walletTransaction',
      user: {
        _type: 'reference',
        _ref: user._id
      },
      type: 'withdrawal',
      amount: parseFloat(amount),
      description: `Cüzdandan ${amount} TL çekildi`,
      status: 'pending', // Withdrawals start as pending for admin approval
      _createdAt: new Date().toISOString()
    });

    // Update user wallet balance
    const newBalance = currentBalance - parseFloat(amount);
    await client
      .patch(user._id)
      .set({ walletBalance: newBalance })
      .commit();

    return NextResponse.json({
      success: true,
      transaction,
      newBalance,
      message: 'Withdrawal request submitted successfully'
    });

  } catch (error) {
    console.error('Error processing withdrawal:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
