import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { client } from '@/sanity/lib/client';

export async function GET() {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user wallet data
    const user = await client.fetch(
      `*[_type == "user" && clerkId == $clerkId][0]{
        walletBalance,
        giftVoucherBalance,
        totalCommissionEarned,
        totalSpent
      }`,
      { clerkId: userId }
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Calculate pending amount from pending transactions
    const pendingTransactions = await client.fetch(
      `*[_type == "walletTransaction" && user._ref == $userId && status == "pending"]{amount}`,
      { userId: user._id }
    );

    const pendingAmount = pendingTransactions.reduce((sum: number, t: any) => sum + (t.amount || 0), 0);

    const walletData = {
      balance: user.walletBalance || 0,
      giftVoucherBalance: user.giftVoucherBalance || 0,
      totalEarned: user.totalCommissionEarned || 0,
      totalSpent: user.totalSpent || 0,
      pendingAmount
    };

    return NextResponse.json({ wallet: walletData });
  } catch (error) {
    console.error('Error fetching wallet data:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
