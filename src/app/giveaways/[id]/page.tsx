import { notFound } from 'next/navigation';
import Image from 'next/image';
import { client } from '@/sanity/lib/client';
import { Gift, Users, Clock, Calendar, Trophy, Star } from 'lucide-react';
import GiveawayTicketPurchase from '@/components/GiveawayTicketPurchase';

interface GiveawayPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getGiveaway(id: string) {
  return await client.fetch(
    `*[_type == "giveaway" && _id == $id][0] {
      _id,
      title,
      description,
      status,
      startDate,
      endDate,
      drawDate,
      totalTickets,
      ticketsSold,
      ticketPrice,
      ticketSalePercentageForDraw,
      numbersPerCard,
      ticketDigitLength,
      participants,
      prizes,
      winningNumbers,
      winners,
      rules,
      "image": image.asset->url
    }`,
    { id }
  );
}

export default async function GiveawayPage({ params }: GiveawayPageProps) {
  const { id } = await params;
  const giveaway = await getGiveaway(id);

  if (!giveaway) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const progressPercentage = Math.round(((giveaway.ticketsSold || 0) / (giveaway.totalTickets || 1)) * 100);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Image */}
            <div className="relative h-64 lg:h-96 bg-gradient-to-br from-blue-400 to-purple-500">
              {giveaway.image ? (
                <Image
                  src={giveaway.image}
                  alt={giveaway.title}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <Gift size={120} className="text-white opacity-80" />
                </div>
              )}
              
              {/* Status Badge */}
              <div className="absolute top-6 right-6">
                <span className={`px-4 py-2 rounded-full text-sm font-semibold ${
                  giveaway.status === 'active' 
                    ? 'bg-green-500 text-white' 
                    : giveaway.status === 'completed'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-500 text-white'
                }`}>
                  {giveaway.status === 'active' ? 'Aktif' : 
                   giveaway.status === 'completed' ? 'Tamamlandı' : 'Pasif'}
                </span>
              </div>
            </div>

            {/* Content */}
            <div className="p-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-4">
                {giveaway.title}
              </h1>

              {giveaway.description && (
                <p className="text-gray-600 text-lg mb-6">
                  {giveaway.description}
                </p>
              )}

              {/* Stats Grid */}
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-green-50 p-4 rounded-lg">
                  <div className="flex items-center text-green-600 mb-2">
                    <Gift className="w-5 h-5 mr-2" />
                    <span className="font-medium">Bilet Fiyatı</span>
                  </div>
                  <p className="text-2xl font-bold text-green-700">
                    {formatCurrency(giveaway.ticketPrice || 0)}
                  </p>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <div className="flex items-center text-blue-600 mb-2">
                    <Users className="w-5 h-5 mr-2" />
                    <span className="font-medium">Satılan Bilet</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-700">
                    {giveaway.ticketsSold || 0} / {giveaway.totalTickets || 0}
                  </p>
                </div>

                {giveaway.endDate && (
                  <div className="bg-orange-50 p-4 rounded-lg">
                    <div className="flex items-center text-orange-600 mb-2">
                      <Clock className="w-5 h-5 mr-2" />
                      <span className="font-medium">Bitiş Tarihi</span>
                    </div>
                    <p className="text-sm font-bold text-orange-700">
                      {formatDate(giveaway.endDate)}
                    </p>
                  </div>
                )}

                {giveaway.drawDate && (
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <div className="flex items-center text-purple-600 mb-2">
                      <Calendar className="w-5 h-5 mr-2" />
                      <span className="font-medium">Çekiliş Tarihi</span>
                    </div>
                    <p className="text-sm font-bold text-purple-700">
                      {formatDate(giveaway.drawDate)}
                    </p>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex justify-between text-sm text-gray-600 mb-2">
                  <span>Satış İlerlemesi</span>
                  <span>{progressPercentage}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Prizes */}
            {giveaway.prizes && giveaway.prizes.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <Trophy className="w-6 h-6 mr-3 text-yellow-500" />
                  Ödüller
                </h2>
                <div className="space-y-4">
                  {giveaway.prizes.map((prize: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg border border-yellow-200">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-yellow-500 text-white rounded-full flex items-center justify-center font-bold mr-4">
                          {prize.rank}
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-900">{prize.title}</h3>
                          {prize.description && (
                            <p className="text-sm text-gray-600">{prize.description}</p>
                          )}
                        </div>
                      </div>
                      {prize.value && (
                        <span className="font-bold text-yellow-700">
                          {formatCurrency(prize.value)}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Rules */}
            {giveaway.rules && (
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  📋 Çekiliş Kuralları
                </h2>
                <div className="prose prose-gray max-w-none">
                  <p className="text-gray-600 whitespace-pre-line">{giveaway.rules}</p>
                </div>
              </div>
            )}

            {/* Winners (if completed) */}
            {giveaway.status === 'completed' && giveaway.winners && giveaway.winners.length > 0 && (
              <div className="bg-white rounded-2xl shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
                  <Star className="w-6 h-6 mr-3 text-yellow-500" />
                  Kazananlar
                </h2>
                
                {giveaway.winningNumbers && (
                  <div className="mb-6 p-4 bg-green-50 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">Kazanan Numaralar:</h3>
                    <div className="flex flex-wrap gap-2">
                      {giveaway.winningNumbers.map((number: string, index: number) => (
                        <span key={index} className="bg-green-500 text-white px-3 py-1 rounded-full font-mono font-bold">
                          {number}
                        </span>
                      ))}
                    </div>
                  </div>
                )}

                <div className="space-y-3">
                  {giveaway.winners.map((winner: any, index: number) => (
                    <div key={index} className="flex items-center justify-between p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                      <div>
                        <span className="font-semibold text-gray-900">
                          {index + 1}. {winner.displayName || winner.userName || 'Kazanan'}
                        </span>
                        <span className="text-sm text-gray-600 ml-2">
                          (Bilet: {winner.ticketNumber})
                        </span>
                      </div>
                      <span className="font-bold text-yellow-700">
                        {winner.prize}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {giveaway.status === 'active' ? (
              <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-8">
                <h2 className="text-xl font-bold text-gray-900 mb-6">
                  🎫 Bilet Satın Al
                </h2>
                <GiveawayTicketPurchase 
                  giveawayId={giveaway._id}
                  ticketPrice={giveaway.ticketPrice}
                  maxTicketsPerUser={10}
                />
              </div>
            ) : (
              <div className="bg-white rounded-2xl shadow-lg p-6">
                <div className="text-center">
                  <Gift size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-700 mb-2">
                    {giveaway.status === 'completed' ? 'Çekiliş Tamamlandı' : 'Çekiliş Aktif Değil'}
                  </h3>
                  <p className="text-gray-600">
                    {giveaway.status === 'completed' 
                      ? 'Bu çekiliş tamamlanmıştır. Kazananlar yukarıda listelenmiştir.'
                      : 'Bu çekiliş henüz aktif değildir.'
                    }
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
