import Image from 'next/image';
import Link from 'next/link';
import { client } from '@/sanity/lib/client';
import { Gift, Users, Clock, Calendar } from 'lucide-react';

async function getActiveGiveaways() {
  return await client.fetch(`
    *[_type == "giveaway" && status == "active"] | order(_createdAt desc) {
      _id,
      title,
      description,
      status,
      startDate,
      endDate,
      drawDate,
      totalTickets,
      ticketsSold,
      ticketPrice,
      prizes,
      "image": image.asset->url
    }
  `);
}

export default async function GiveawaysPage() {
  const giveaways = await getActiveGiveaways();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            🎁 Aktif Çekilişler
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Harika ödüller kazanma şansını kaçırmayın! Çekilişlerimize katılın ve şansınızı deneyin.
          </p>
        </div>

        {giveaways.length === 0 ? (
          <div className="text-center py-16">
            <Gift size={80} className="mx-auto text-gray-400 mb-6" />
            <h2 className="text-3xl font-semibold text-gray-600 mb-4">
              Şu anda aktif çekiliş bulunmamaktadır
            </h2>
            <p className="text-gray-500 text-lg mb-8">
              Yeni çekilişler için takipte kalın!
            </p>
            <Link
              href="/"
              className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              Ana Sayfaya Dön
            </Link>
          </div>
        ) : (
          <>
            {/* Giveaways Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
              {giveaways.map((giveaway: any) => (
                <Link
                  href={`/giveaways/${giveaway._id}`}
                  key={giveaway._id}
                  className="group"
                >
                  <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                    {/* Image */}
                    <div className="relative h-48 bg-gradient-to-br from-blue-400 to-purple-500">
                      {giveaway.image ? (
                        <Image
                          src={giveaway.image}
                          alt={giveaway.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full">
                          <Gift size={64} className="text-white opacity-80" />
                        </div>
                      )}
                      
                      {/* Status Badge */}
                      <div className="absolute top-4 right-4">
                        <span className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                          Aktif
                        </span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                        {giveaway.title}
                      </h3>

                      {giveaway.description && (
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {giveaway.description}
                        </p>
                      )}

                      {/* Stats */}
                      <div className="space-y-3 mb-6">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-green-600">
                            <Gift className="w-4 h-4 mr-2" />
                            <span className="text-sm font-medium">Bilet Fiyatı</span>
                          </div>
                          <span className="font-bold text-green-600">
                            {formatCurrency(giveaway.ticketPrice || 0)}
                          </span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-blue-600">
                            <Users className="w-4 h-4 mr-2" />
                            <span className="text-sm font-medium">Satılan Bilet</span>
                          </div>
                          <span className="font-bold text-blue-600">
                            {giveaway.ticketsSold || 0} / {giveaway.totalTickets || 0}
                          </span>
                        </div>

                        {giveaway.endDate && (
                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-orange-600">
                              <Clock className="w-4 h-4 mr-2" />
                              <span className="text-sm font-medium">Bitiş</span>
                            </div>
                            <span className="font-bold text-orange-600 text-xs">
                              {formatDate(giveaway.endDate)}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-gray-600 mb-1">
                          <span>İlerleme</span>
                          <span>
                            {Math.round(((giveaway.ticketsSold || 0) / (giveaway.totalTickets || 1)) * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${Math.min(((giveaway.ticketsSold || 0) / (giveaway.totalTickets || 1)) * 100, 100)}%`
                            }}
                          ></div>
                        </div>
                      </div>

                      {/* Action Button */}
                      <button className="w-full bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-3 px-4 rounded-lg transition-all duration-300 transform group-hover:scale-105">
                        Çekilişe Katıl
                      </button>
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* Call to Action */}
            <div className="text-center bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Daha Fazla Çekiliş İçin Takipte Kalın!
              </h2>
              <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
                Yeni çekilişlerden haberdar olmak ve özel fırsatları kaçırmamak için bültenimize abone olun.
              </p>
              <button className="bg-gradient-to-r from-green-500 to-blue-600 hover:from-green-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                Bültene Abone Ol
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
