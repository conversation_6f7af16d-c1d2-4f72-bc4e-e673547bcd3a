"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import { getImageUrl, getOptimizedImageUrl } from '@/sanity/lib/image';
import { Gavel, ImageIcon } from 'lucide-react';

interface OptimizedImageProps {
  image: any;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fill?: boolean;
  sizes?: string;
  priority?: boolean;
  quality?: number;
  format?: 'webp' | 'jpg' | 'png';
  fallbackIcon?: React.ReactNode;
  onError?: () => void;
}

export default function OptimizedImage({
  image,
  alt,
  width,
  height,
  className = '',
  fill = false,
  sizes,
  priority = false,
  quality = 80,
  format = 'webp',
  fallbackIcon,
  onError
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Get optimized image URL
  const imageUrl = getOptimizedImageUrl(image, {
    width,
    height,
    quality,
    format
  });

  // If no image URL or error occurred, show fallback
  if (!imageUrl || imageError) {
    return (
      <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        {fallbackIcon || <ImageIcon className="w-8 h-8 text-gray-400" />}
      </div>
    );
  }

  const handleImageError = () => {
    setImageError(true);
    setIsLoading(false);
    if (onError) {
      onError();
    }
  };

  const handleImageLoad = () => {
    setIsLoading(false);
  };

  return (
    <div className={`relative ${className}`}>
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"></div>
        </div>
      )}
      <Image
        src={imageUrl}
        alt={alt}
        width={fill ? undefined : width}
        height={fill ? undefined : height}
        fill={fill}
        className={`object-cover ${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300`}
        sizes={sizes}
        priority={priority}
        quality={quality}
        onError={handleImageError}
        onLoad={handleImageLoad}
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
      />
    </div>
  );
}

// Specialized component for auction images
interface AuctionImageProps extends Omit<OptimizedImageProps, 'fallbackIcon'> {
  auction?: {
    name?: string;
    image?: any;
  };
}

export function AuctionImage({ auction, alt, ...props }: AuctionImageProps) {
  return (
    <OptimizedImage
      image={auction?.image}
      alt={alt || auction?.name || 'Açık artırma görseli'}
      fallbackIcon={<Gavel className="w-16 h-16 text-gray-400" />}
      {...props}
    />
  );
}
