"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Loader2,
  AlertCircle 
} from 'lucide-react';
import Image from 'next/image';

interface UploadedImage {
  _id: string;
  url: string;
  filename: string;
  size: number;
  mimeType: string;
}

interface ImageUploadProps {
  label?: string;
  description?: string;
  maxFiles?: number;
  maxSize?: number; // in MB
  acceptedTypes?: string[];
  onImagesChange: (images: UploadedImage[]) => void;
  initialImages?: UploadedImage[];
  disabled?: boolean;
}

export default function ImageUpload({
  label = "Resim Yükle",
  description = "JPEG, PNG veya WebP formatında resim yükleyebilirsiniz",
  maxFiles = 5,
  maxSize = 10,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  onImagesChange,
  initialImages = [],
  disabled = false
}: ImageUploadProps) {
  const [images, setImages] = useState<UploadedImage[]>(initialImages);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Check if adding these files would exceed maxFiles
    if (images.length + files.length > maxFiles) {
      setError(`En fazla ${maxFiles} resim yükleyebilirsiniz`);
      return;
    }

    setUploading(true);
    setError(null);

    try {
      const uploadPromises = Array.from(files).map(async (file) => {
        // Validate file type
        if (!acceptedTypes.includes(file.type)) {
          throw new Error(`${file.name}: Desteklenmeyen dosya formatı`);
        }

        // Validate file size
        if (file.size > maxSize * 1024 * 1024) {
          throw new Error(`${file.name}: Dosya boyutu ${maxSize}MB'dan büyük olamaz`);
        }

        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch('/api/upload/image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (!data.success) {
          throw new Error(data.message || 'Resim yüklenirken hata oluştu');
        }

        return data.data;
      });

      const uploadedImages = await Promise.all(uploadPromises);
      const newImages = [...images, ...uploadedImages];
      
      setImages(newImages);
      onImagesChange(newImages);

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (err) {
      console.error('Upload error:', err);
      setError(err instanceof Error ? err.message : 'Resim yüklenirken hata oluştu');
    } finally {
      setUploading(false);
    }
  };

  const removeImage = (imageId: string) => {
    const newImages = images.filter(img => img._id !== imageId);
    setImages(newImages);
    onImagesChange(newImages);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      <div>
        <Label>{label}</Label>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="flex items-center p-3 text-sm text-red-800 bg-red-50 border border-red-200 rounded-md">
          <AlertCircle className="w-4 h-4 mr-2" />
          {error}
        </div>
      )}

      {/* Uploaded Images */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <Card key={image._id} className="relative group">
              <CardContent className="p-2">
                <div className="aspect-square relative overflow-hidden rounded-md">
                  <Image
                    src={image.url}
                    alt={image.filename}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                  />
                  <button
                    type="button"
                    onClick={() => removeImage(image._id)}
                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    disabled={disabled}
                  >
                    <X className="w-3 h-3" />
                  </button>
                </div>
                <div className="mt-2 text-xs text-gray-600 truncate">
                  <p className="truncate">{image.filename}</p>
                  <p>{formatFileSize(image.size)}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Upload Area */}
      {images.length < maxFiles && (
        <Card className="border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors">
          <CardContent className="p-6">
            <div className="text-center">
              <div className="mx-auto w-12 h-12 text-gray-400 mb-4">
                {uploading ? (
                  <Loader2 className="w-12 h-12 animate-spin" />
                ) : (
                  <ImageIcon className="w-12 h-12" />
                )}
              </div>
              
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  {uploading ? 'Yükleniyor...' : 'Resim yüklemek için tıklayın'}
                </p>
                <p className="text-xs text-gray-500">
                  {maxFiles - images.length} resim daha ekleyebilirsiniz
                </p>
              </div>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept={acceptedTypes.join(',')}
                onChange={handleFileSelect}
                className="hidden"
                disabled={disabled || uploading}
              />

              <Button
                type="button"
                variant="outline"
                size="sm"
                className="mt-4"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled || uploading}
              >
                <Upload className="w-4 h-4 mr-2" />
                {uploading ? 'Yükleniyor...' : 'Dosya Seç'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="text-xs text-gray-500">
        <p>• Maksimum {maxFiles} resim</p>
        <p>• Maksimum {maxSize}MB dosya boyutu</p>
        <p>• Desteklenen formatlar: JPEG, PNG, WebP</p>
      </div>
    </div>
  );
}
