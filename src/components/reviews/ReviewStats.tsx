'use client';

import { Star } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useReviewStore } from '@/store/review-store';

interface ReviewStatsProps {
  productId: string;
  className?: string;
}

export default function ReviewStats({ productId, className }: ReviewStatsProps) {
  const { getProductStats } = useReviewStore();
  const stats = getProductStats(productId);

  if (stats.totalReviews === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <div className="space-y-2">
            <div className="flex items-center justify-center">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-5 w-5 text-gray-300" />
              ))}
            </div>
            <p className="text-muted-foreground">No reviews yet</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const ratingPercentages = Object.entries(stats.ratingDistribution).map(([rating, count]) => ({
    rating: parseInt(rating),
    count,
    percentage: stats.totalReviews > 0 ? (count / stats.totalReviews) * 100 : 0
  })).reverse(); // Show 5 stars first

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Customer Reviews</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Rating */}
        <div className="text-center space-y-2">
          <div className="text-4xl font-bold">{stats.averageRating}</div>
          <div className="flex items-center justify-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-5 w-5 ${
                  i < Math.floor(stats.averageRating)
                    ? 'text-yellow-400 fill-current'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <p className="text-sm text-muted-foreground">
            Based on {stats.totalReviews} {stats.totalReviews === 1 ? 'review' : 'reviews'}
          </p>
        </div>

        {/* Rating Distribution */}
        <div className="space-y-3">
          {ratingPercentages.map(({ rating, count, percentage }) => (
            <div key={rating} className="flex items-center space-x-3">
              <div className="flex items-center space-x-1 w-16">
                <span className="text-sm">{rating}</span>
                <Star className="h-3 w-3 text-yellow-400 fill-current" />
              </div>
              <div className="flex-1">
                <Progress value={percentage} className="h-2" />
              </div>
              <div className="w-12 text-right">
                <span className="text-sm text-muted-foreground">{count}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Review Breakdown */}
        <div className="grid grid-cols-2 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {Math.round((stats.ratingDistribution[5] + stats.ratingDistribution[4]) / stats.totalReviews * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">Positive</p>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {Math.round((stats.ratingDistribution[3]) / stats.totalReviews * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">Neutral</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
