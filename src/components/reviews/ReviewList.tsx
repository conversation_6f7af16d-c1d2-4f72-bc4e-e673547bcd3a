'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Star, ThumbsUp, Flag, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Review } from '@/types';
import { useReviewStore } from '@/store/review-store';
import { useUser } from '@clerk/nextjs';
import { formatDistanceToNow } from 'date-fns';
import { toast } from 'sonner';
import ReviewFilters, { ReviewFilterOptions } from './ReviewFilters';

interface ReviewListProps {
  productId: string;
  className?: string;
}

export default function ReviewList({ productId, className }: ReviewListProps) {
  const { user } = useUser();
  const { getProductReviews, getProductStats, markHelpful } = useReviewStore();
  const allReviews = getProductReviews(productId);
  const stats = getProductStats(productId);

  const [expandedReviews, setExpandedReviews] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState<ReviewFilterOptions>({ sortBy: 'newest' });
  const [helpfulClicks, setHelpfulClicks] = useState<Set<string>>(new Set());

  // Filter and sort reviews
  const filteredAndSortedReviews = allReviews
    .filter(review => {
      if (filters.rating && review.rating !== filters.rating) return false;
      if (filters.verified && !review.verified) return false;
      if (filters.withImages && (!review.images || review.images.length === 0)) return false;
      return true;
    })
    .sort((a, b) => {
      switch (filters.sortBy) {
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'oldest':
          return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        case 'highest-rating':
          return b.rating - a.rating;
        case 'lowest-rating':
          return a.rating - b.rating;
        case 'most-helpful':
          return b.helpful - a.helpful;
        default:
          return 0;
      }
    });

  const handleMarkHelpful = (reviewId: string) => {
    if (helpfulClicks.has(reviewId)) {
      toast.info('You have already marked this review as helpful');
      return;
    }

    markHelpful(reviewId, productId);
    setHelpfulClicks(prev => new Set([...prev, reviewId]));
    toast.success('Thank you for your feedback!');
  };

  const toggleExpanded = (reviewId: string) => {
    setExpandedReviews(prev => {
      const newSet = new Set(prev);
      if (newSet.has(reviewId)) {
        newSet.delete(reviewId);
      } else {
        newSet.add(reviewId);
      }
      return newSet;
    });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(date));
  };

  if (allReviews.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-muted-foreground">No reviews yet. Be the first to review this product!</p>
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Review Filters */}
      <ReviewFilters
        stats={stats}
        onFilterChange={setFilters}
        activeFilters={filters}
      />

      {/* Reviews Count */}
      <div className="flex items-center justify-between py-4">
        <h3 className="text-lg font-semibold">
          {filteredAndSortedReviews.length === allReviews.length
            ? `Customer Reviews (${allReviews.length})`
            : `Showing ${filteredAndSortedReviews.length} of ${allReviews.length} reviews`
          }
        </h3>
      </div>

      {/* Reviews */}
      <div className="space-y-6">
        {filteredAndSortedReviews.map((review) => (
          <Card key={review.id}>
            <CardContent className="p-6">
              <div className="space-y-4">
                {/* Header */}
                <div className="flex items-start justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={review.userAvatar} alt={review.userName} />
                      <AvatarFallback>
                        {review.userName.split(' ').map(n => n[0]).join('')}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2">
                        <p className="font-medium">{review.userName}</p>
                        {review.verified && (
                          <Badge variant="secondary" className="text-xs">
                            Verified Purchase
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(review.createdAt)}
                      </p>
                    </div>
                  </div>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>
                        <Flag className="h-4 w-4 mr-2" />
                        Report Review
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Rating */}
                <div className="flex items-center space-x-2">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < review.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  <span className="text-sm font-medium">{review.rating}/5</span>
                </div>

                {/* Title */}
                {review.title && (
                  <h4 className="font-semibold">{review.title}</h4>
                )}

                {/* Comment */}
                <p className="text-muted-foreground leading-relaxed">
                  {review.comment}
                </p>

                {/* Images */}
                {review.images && review.images.length > 0 && (
                  <div className="flex space-x-2">
                    {review.images.map((image, index) => (
                      <div key={index} className="relative w-20 h-20 rounded-lg overflow-hidden">
                        <Image
                          src={image}
                          alt={`Review image ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </div>
                    ))}
                  </div>
                )}

                {/* Actions */}
                <div className="flex items-center justify-between pt-2 border-t">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleHelpful(review.id)}
                    className="text-muted-foreground hover:text-foreground"
                    disabled={helpfulClicks.has(review.id)}
                  >
                    <ThumbsUp className={`h-4 w-4 mr-2 ${helpfulClicks.has(review.id) ? 'fill-current' : ''}`} />
                    Helpful ({review.helpful})
                  </Button>
                  
                  {review.updatedAt > review.createdAt && (
                    <span className="text-xs text-muted-foreground">
                      Edited {formatDate(review.updatedAt)}
                    </span>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
