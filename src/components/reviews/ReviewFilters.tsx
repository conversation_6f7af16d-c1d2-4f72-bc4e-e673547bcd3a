'use client';

import { useState } from 'react';
import { Star, Filter, SortAsc, SortDesc, Check } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { ReviewStats } from '@/types';

interface ReviewFiltersProps {
  stats: ReviewStats;
  onFilterChange: (filters: ReviewFilterOptions) => void;
  activeFilters: ReviewFilterOptions;
}

export interface ReviewFilterOptions {
  rating?: number;
  sortBy: 'newest' | 'oldest' | 'highest-rating' | 'lowest-rating' | 'most-helpful';
  verified?: boolean;
  withImages?: boolean;
}

export default function ReviewFilters({ stats, onFilterChange, activeFilters }: ReviewFiltersProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleFilterChange = (key: keyof ReviewFilterOptions, value: any) => {
    const newFilters = { ...activeFilters };
    
    if (key === 'rating' && newFilters.rating === value) {
      // Toggle off if same rating is clicked
      delete newFilters.rating;
    } else {
      newFilters[key] = value;
    }
    
    onFilterChange(newFilters);
  };

  const clearFilters = () => {
    onFilterChange({ sortBy: 'newest' });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (activeFilters.rating) count++;
    if (activeFilters.verified) count++;
    if (activeFilters.withImages) count++;
    return count;
  };

  const getSortLabel = (sortBy: string) => {
    switch (sortBy) {
      case 'newest': return 'Newest First';
      case 'oldest': return 'Oldest First';
      case 'highest-rating': return 'Highest Rating';
      case 'lowest-rating': return 'Lowest Rating';
      case 'most-helpful': return 'Most Helpful';
      default: return 'Newest First';
    }
  };

  return (
    <div className="flex items-center justify-between py-4 border-b">
      <div className="flex items-center space-x-4">
        {/* Rating Distribution */}
        <div className="flex items-center space-x-2">
          {[5, 4, 3, 2, 1].map((rating) => (
            <Button
              key={rating}
              variant={activeFilters.rating === rating ? "default" : "outline"}
              size="sm"
              onClick={() => handleFilterChange('rating', rating)}
              className="flex items-center space-x-1"
            >
              <Star className="h-3 w-3" />
              <span>{rating}</span>
              <Badge variant="secondary" className="ml-1 text-xs">
                {stats.ratingDistribution[rating as keyof typeof stats.ratingDistribution]}
              </Badge>
            </Button>
          ))}
        </div>

        {/* Additional Filters */}
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="relative">
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {getActiveFilterCount() > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                  {getActiveFilterCount()}
                </Badge>
              )}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-48">
            <DropdownMenuLabel>Filter Options</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('verified', !activeFilters.verified)}
              className="flex items-center justify-between"
            >
              <span>Verified Purchases</span>
              {activeFilters.verified && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('withImages', !activeFilters.withImages)}
              className="flex items-center justify-between"
            >
              <span>With Images</span>
              {activeFilters.withImages && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem onClick={clearFilters}>
              Clear All Filters
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Sort Options */}
      <div className="flex items-center space-x-2">
        <span className="text-sm text-muted-foreground">Sort by:</span>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm">
              {activeFilters.sortBy === 'oldest' ? (
                <SortAsc className="h-4 w-4 mr-2" />
              ) : (
                <SortDesc className="h-4 w-4 mr-2" />
              )}
              {getSortLabel(activeFilters.sortBy)}
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleFilterChange('sortBy', 'newest')}
              className="flex items-center justify-between"
            >
              <span>Newest First</span>
              {activeFilters.sortBy === 'newest' && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('sortBy', 'oldest')}
              className="flex items-center justify-between"
            >
              <span>Oldest First</span>
              {activeFilters.sortBy === 'oldest' && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('sortBy', 'highest-rating')}
              className="flex items-center justify-between"
            >
              <span>Highest Rating</span>
              {activeFilters.sortBy === 'highest-rating' && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('sortBy', 'lowest-rating')}
              className="flex items-center justify-between"
            >
              <span>Lowest Rating</span>
              {activeFilters.sortBy === 'lowest-rating' && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
            
            <DropdownMenuSeparator />
            
            <DropdownMenuItem
              onClick={() => handleFilterChange('sortBy', 'most-helpful')}
              className="flex items-center justify-between"
            >
              <span>Most Helpful</span>
              {activeFilters.sortBy === 'most-helpful' && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
