"use client";

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertCircle,
  CheckCircle,
  Gavel,
  Upload,
  Calendar,
  DollarSign
} from 'lucide-react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import ImageUpload from '@/components/ui/image-upload';

const CATEGORY_OPTIONS = [
  { value: 'electronics', label: 'Elektronik' },
  { value: 'clothing', label: 'Giyim' },
  { value: 'home-living', label: 'Ev & Yaşam' },
  { value: 'sports', label: 'Spor' },
  { value: 'books', label: 'Kitap' },
  { value: 'art', label: 'Sanat' },
  { value: 'collectibles', label: 'Koleksiyon' },
  { value: 'automotive', label: 'Otomotiv' },
  { value: 'other', label: 'Diğer' },
];

const CONDITION_OPTIONS = [
  { value: 'new', label: 'Yeni' },
  { value: 'excellent', label: 'Çok İyi' },
  { value: 'good', label: 'İyi' },
  { value: 'fair', label: 'Orta' },
  { value: 'poor', label: 'Kötü' },
];

export default function CreateAuction() {
  const { user } = useUser();
  const router = useRouter();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    startingBid: '',
    bidIncrementAmount: '1',
    reservePrice: '',
    buyNowPrice: '',
    startTime: '',
    endTime: '',
    category: 'electronics',
    condition: 'good',
    shippingCost: '0',
    freeShippingThreshold: '',
    estimatedDelivery: '3',
    tags: '',
    notes: ''
  });

  const [images, setImages] = useState<any[]>([]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Validation
      if (!formData.name || !formData.startingBid || !formData.startTime || !formData.endTime) {
        setError('Zorunlu alanları doldurun');
        return;
      }

      if (new Date(formData.startTime) >= new Date(formData.endTime)) {
        setError('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
        return;
      }

      // Prepare data
      const auctionData = {
        name: formData.name,
        description: formData.description,
        startingBid: parseFloat(formData.startingBid),
        bidIncrementAmount: parseFloat(formData.bidIncrementAmount),
        reservePrice: formData.reservePrice ? parseFloat(formData.reservePrice) : undefined,
        buyNowPrice: formData.buyNowPrice ? parseFloat(formData.buyNowPrice) : undefined,
        startTime: formData.startTime,
        endTime: formData.endTime,
        category: formData.category,
        condition: formData.condition,
        shippingCost: parseFloat(formData.shippingCost),
        freeShippingThreshold: formData.freeShippingThreshold ? parseFloat(formData.freeShippingThreshold) : undefined,
        estimatedDelivery: parseInt(formData.estimatedDelivery),
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        notes: formData.notes,
        images: images.map(img => ({
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: img._id
          }
        })),
        image: images.length > 0 ? {
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: images[0]._id
          }
        } : undefined
      };

      const response = await fetch('/api/auctions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(auctionData),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Açık artırma başarıyla oluşturuldu!');
        
        // Reset form
        setFormData({
          name: '',
          description: '',
          startingBid: '',
          bidIncrementAmount: '1',
          reservePrice: '',
          buyNowPrice: '',
          startTime: '',
          endTime: '',
          category: 'electronics',
          condition: 'good',
          shippingCost: '0',
          freeShippingThreshold: '',
          estimatedDelivery: '3',
          tags: '',
          notes: ''
        });
        setImages([]);
        setImages([]);

        // Redirect to auction detail after 2 seconds
        setTimeout(() => {
          router.push(`/auctions/${data.data.id}`);
        }, 2000);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Açık artırma oluşturulurken hata oluştu');
      console.error('Create auction error:', err);
    } finally {
      setLoading(false);
    }
  };

  // Set default dates
  React.useEffect(() => {
    const now = new Date();
    const startTime = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    const endTime = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // 7 days from now

    setFormData(prev => ({
      ...prev,
      startTime: startTime.toISOString().slice(0, 16),
      endTime: endTime.toISOString().slice(0, 16)
    }));
  }, []);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2 flex items-center">
            <Gavel className="w-8 h-8 mr-3" />
            Açık Artırma Oluştur
          </h1>
          <p className="text-gray-600">
            Ürününüz için açık artırma başlatın
          </p>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <Card className="border-red-200 bg-red-50 mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {success && (
          <Card className="border-green-200 bg-green-50 mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                <p className="text-green-800">{success}</p>
              </div>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Ürün Bilgileri</CardTitle>
              <CardDescription>
                Açık artırmaya çıkaracağınız ürünün detaylarını girin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Ürün adını girin"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="description">Açıklama</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Ürün açıklaması"
                    rows={4}
                  />
                </div>

                {/* Image Upload */}
                <div className="md:col-span-2">
                  <ImageUpload
                    label="Ürün Resimleri"
                    description="Ürününüzün fotoğraflarını yükleyin. İlk resim ana resim olarak kullanılacaktır."
                    maxFiles={5}
                    onImagesChange={setImages}
                    initialImages={images}
                    disabled={loading}
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategori</Label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    {CATEGORY_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="condition">Durum</Label>
                  <select
                    id="condition"
                    name="condition"
                    value={formData.condition}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    {CONDITION_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Pricing */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Fiyat Bilgileri
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startingBid">Başlangıç Fiyatı (TL) *</Label>
                    <Input
                      id="startingBid"
                      name="startingBid"
                      type="number"
                      value={formData.startingBid}
                      onChange={handleInputChange}
                      placeholder="0"
                      min="1"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="bidIncrementAmount">Minimum Artış (TL)</Label>
                    <Input
                      id="bidIncrementAmount"
                      name="bidIncrementAmount"
                      type="number"
                      value={formData.bidIncrementAmount}
                      onChange={handleInputChange}
                      placeholder="1"
                      min="1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="reservePrice">Rezerv Fiyat (TL)</Label>
                    <Input
                      id="reservePrice"
                      name="reservePrice"
                      type="number"
                      value={formData.reservePrice}
                      onChange={handleInputChange}
                      placeholder="Opsiyonel"
                      min="1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="buyNowPrice">Hemen Al Fiyatı (TL)</Label>
                    <Input
                      id="buyNowPrice"
                      name="buyNowPrice"
                      type="number"
                      value={formData.buyNowPrice}
                      onChange={handleInputChange}
                      placeholder="Opsiyonel"
                      min="1"
                    />
                  </div>
                </div>
              </div>

              {/* Timing */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4 flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Zaman Bilgileri
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startTime">Başlangıç Tarihi *</Label>
                    <Input
                      id="startTime"
                      name="startTime"
                      type="datetime-local"
                      value={formData.startTime}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="endTime">Bitiş Tarihi *</Label>
                    <Input
                      id="endTime"
                      name="endTime"
                      type="datetime-local"
                      value={formData.endTime}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Shipping */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium mb-4">Kargo Bilgileri</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="shippingCost">Kargo Ücreti (TL)</Label>
                    <Input
                      id="shippingCost"
                      name="shippingCost"
                      type="number"
                      value={formData.shippingCost}
                      onChange={handleInputChange}
                      placeholder="0"
                      min="0"
                    />
                  </div>

                  <div>
                    <Label htmlFor="freeShippingThreshold">Ücretsiz Kargo Eşiği (TL)</Label>
                    <Input
                      id="freeShippingThreshold"
                      name="freeShippingThreshold"
                      type="number"
                      value={formData.freeShippingThreshold}
                      onChange={handleInputChange}
                      placeholder="Opsiyonel"
                      min="1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="estimatedDelivery">Tahmini Teslimat (Gün)</Label>
                    <Input
                      id="estimatedDelivery"
                      name="estimatedDelivery"
                      type="number"
                      value={formData.estimatedDelivery}
                      onChange={handleInputChange}
                      placeholder="3"
                      min="1"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Info */}
              <div className="border-t pt-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="tags">Etiketler</Label>
                    <Input
                      id="tags"
                      name="tags"
                      value={formData.tags}
                      onChange={handleInputChange}
                      placeholder="virgül ile ayırın: elektronik, telefon, samsung"
                    />
                  </div>

                  <div>
                    <Label htmlFor="notes">Notlar</Label>
                    <Textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      placeholder="Ek bilgiler, özel durumlar"
                      rows={3}
                    />
                  </div>
                </div>
              </div>

              {/* Submit */}
              <div className="border-t pt-6">
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="w-full"
                  size="lg"
                >
                  {loading ? 'Oluşturuluyor...' : 'Açık Artırma Oluştur'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  );
}
