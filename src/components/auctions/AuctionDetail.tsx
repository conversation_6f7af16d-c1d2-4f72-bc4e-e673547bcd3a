"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Clock,
  Eye,
  Heart,
  Gavel,
  User,
  Star,
  TrendingUp,
  Users,
  Package,
  Shield,
  AlertCircle,
  CheckCircle,
  Edit
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { useUser } from '@clerk/nextjs';
import { AuctionImage } from '@/components/ui/optimized-image';

interface AuctionDetailProps {
  auctionId: string;
}

interface Auction {
  _id: string;
  id: string;
  name: string;
  description: string;
  image?: any;
  images?: any[];
  currentBid: number;
  startingBid: number;
  bidIncrementAmount: number;
  reservePrice?: number;
  buyNowPrice?: number;
  startTime: string;
  endTime: string;
  status: string;
  category: string;
  condition: string;
  featured: boolean;
  viewCount: number;
  watchCount: number;
  notes?: string;
  tags?: string[];
  seller?: {
    _id: string;
    name: string;
    email: string;
  };
  winner?: {
    _id: string;
    name: string;
  };
  winningBid?: number;
  bidHistory: Array<{
    bidAmount: number;
    bidTime: string;
    bidder: {
      _id: string;
      name: string;
    };
    isAutoBid: boolean;
  }>;
  shippingInfo?: {
    shippingCost: number;
    freeShippingThreshold?: number;
    estimatedDelivery: number;
  };
}

interface BidStats {
  totalBids: number;
  uniqueBidders: number;
  averageBid: number;
  highestBid: number;
  timeRemaining: number;
  isActive: boolean;
}

export default function AuctionDetail({ auctionId }: AuctionDetailProps) {
  const { user } = useUser();
  const [auction, setAuction] = useState<Auction | null>(null);
  const [bidStats, setBidStats] = useState<BidStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [bidAmount, setBidAmount] = useState('');
  const [bidding, setBidding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchAuctionDetail();
  }, [auctionId]);

  useEffect(() => {
    if (auction && bidStats?.isActive) {
      const timer = setInterval(() => {
        const now = new Date();
        const endTime = new Date(auction.endTime);
        const timeRemaining = endTime.getTime() - now.getTime();
        
        if (timeRemaining <= 0) {
          fetchAuctionDetail(); // Refresh to get updated status
        }
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [auction, bidStats]);

  const fetchAuctionDetail = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/auctions/${auctionId}`);
      const data = await response.json();

      if (data.success) {
        setAuction(data.data.auction);
        setBidStats(data.data.bidStats);

        // Set suggested bid amount
        const suggestedBid = (data.data.auction.currentBid || data.data.auction.startingBid) + data.data.auction.bidIncrementAmount;
        setBidAmount(suggestedBid.toString());
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Açık artırma detayları yüklenirken hata oluştu');
      console.error('Auction detail fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleBid = async () => {
    if (!user || !auction) return;

    try {
      setBidding(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/auctions/bid', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          auctionId: auction._id,
          bidAmount: parseFloat(bidAmount)
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Teklifiniz başarıyla verildi!');
        fetchAuctionDetail(); // Refresh auction data
        
        // Set next suggested bid
        const nextBid = data.data.newBid + auction.bidIncrementAmount;
        setBidAmount(nextBid.toString());
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Teklif verilirken hata oluştu');
      console.error('Bid error:', err);
    } finally {
      setBidding(false);
    }
  };

  const formatTimeRemaining = (timeRemaining: number) => {
    if (timeRemaining <= 0) {
      return 'Sona erdi';
    }

    const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

    if (days > 0) {
      return `${days}g ${hours}s ${minutes}d`;
    } else if (hours > 0) {
      return `${hours}s ${minutes}d ${seconds}sn`;
    } else if (minutes > 0) {
      return `${minutes}d ${seconds}sn`;
    } else {
      return `${seconds}sn`;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'new': return 'bg-green-100 text-green-800';
      case 'excellent': return 'bg-blue-100 text-blue-800';
      case 'good': return 'bg-yellow-100 text-yellow-800';
      case 'fair': return 'bg-orange-100 text-orange-800';
      case 'poor': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const canBid = user && auction && bidStats?.isActive && auction.seller?._id !== user.id;

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error && !auction) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="pt-6">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <p className="text-red-800">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!auction) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Gavel className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Açık artırma bulunamadı
          </h3>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Images and Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Main Image */}
          <Card>
            <CardContent className="p-0">
              <AuctionImage
                auction={auction}
                alt={auction.name}
                fill
                className="aspect-square overflow-hidden rounded-lg"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                priority
              />
            </CardContent>
          </Card>

          {/* Auction Details */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl">{auction.name}</CardTitle>
                  <CardDescription className="mt-2">
                    {auction.description}
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  {auction.featured && (
                    <Badge className="bg-yellow-500">
                      <Star className="w-3 h-3 mr-1" />
                      Öne Çıkan
                    </Badge>
                  )}
                  <Badge className={getStatusColor(auction.status)}>
                    {auction.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="details" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="details">Detaylar</TabsTrigger>
                  <TabsTrigger value="bids">Teklifler</TabsTrigger>
                  <TabsTrigger value="shipping">Kargo</TabsTrigger>
                </TabsList>
                
                <TabsContent value="details" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Kategori</Label>
                      <p className="font-medium">{auction.category}</p>
                    </div>
                    <div>
                      <Label>Durum</Label>
                      <Badge className={getConditionColor(auction.condition)}>
                        {auction.condition}
                      </Badge>
                    </div>
                    <div>
                      <Label>Başlangıç Tarihi</Label>
                      <p className="font-medium">{formatDate(auction.startTime)}</p>
                    </div>
                    <div>
                      <Label>Bitiş Tarihi</Label>
                      <p className="font-medium">{formatDate(auction.endTime)}</p>
                    </div>
                  </div>
                  
                  {auction.notes && (
                    <div>
                      <Label>Satıcı Notları</Label>
                      <p className="mt-1 text-gray-700">{auction.notes}</p>
                    </div>
                  )}
                  
                  {auction.tags && auction.tags.length > 0 && (
                    <div>
                      <Label>Etiketler</Label>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {auction.tags.map((tag, index) => (
                          <Badge key={index} variant="outline">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>
                
                <TabsContent value="bids" className="space-y-4">
                  {auction.bidHistory && auction.bidHistory.length > 0 ? (
                    <div className="space-y-2">
                      {auction.bidHistory.slice(0, 10).map((bid, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded">
                          <div className="flex items-center space-x-3">
                            <User className="w-5 h-5 text-gray-400" />
                            <div>
                              <p className="font-medium">{bid.bidder?.name || 'Bilinmeyen Kullanıcı'}</p>
                              <p className="text-sm text-gray-600">{formatDate(bid.bidTime)}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-green-600">{formatCurrency(bid.bidAmount)}</p>
                            {bid.isAutoBid && (
                              <Badge variant="outline" className="text-xs">
                                Otomatik
                              </Badge>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-center text-gray-600 py-8">
                      Henüz teklif verilmemiş
                    </p>
                  )}
                </TabsContent>
                
                <TabsContent value="shipping" className="space-y-4">
                  {auction.shippingInfo && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span>Kargo Ücreti:</span>
                        <span className="font-medium">
                          {auction.shippingInfo.shippingCost > 0 
                            ? formatCurrency(auction.shippingInfo.shippingCost)
                            : 'Ücretsiz'
                          }
                        </span>
                      </div>
                      
                      {auction.shippingInfo.freeShippingThreshold && (
                        <div className="flex items-center justify-between">
                          <span>Ücretsiz Kargo Eşiği:</span>
                          <span className="font-medium">
                            {formatCurrency(auction.shippingInfo.freeShippingThreshold)}
                          </span>
                        </div>
                      )}
                      
                      <div className="flex items-center justify-between">
                        <span>Tahmini Teslimat:</span>
                        <span className="font-medium">
                          {auction.shippingInfo.estimatedDelivery} gün
                        </span>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Bidding */}
        <div className="space-y-6">
          {/* Current Bid Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Gavel className="w-5 h-5 mr-2" />
                Mevcut Durum
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">En Yüksek Teklif</p>
                <p className="text-3xl font-bold text-green-600">
                  {formatCurrency(auction.currentBid || auction.startingBid)}
                </p>
                {auction.bidHistory && auction.bidHistory.length > 0 && (
                  <p className="text-sm text-gray-500 mt-1">
                    {auction.bidHistory[0]?.bidder?.name || 'Bilinmiyor'}
                  </p>
                )}
              </div>

              {bidStats && (
                <div className="text-center">
                  <p className="text-sm text-gray-600">Kalan Süre</p>
                  <p className={`text-xl font-bold ${bidStats.timeRemaining > 0 ? 'text-red-600' : 'text-gray-600'}`}>
                    {formatTimeRemaining(bidStats.timeRemaining)}
                  </p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <p className="text-gray-600">Toplam Teklif</p>
                  <p className="font-bold">{bidStats?.totalBids || 0}</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-600">Teklif Veren</p>
                  <p className="font-bold">{bidStats?.uniqueBidders || 0}</p>
                </div>
              </div>

              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {auction.viewCount} görüntülenme
                </div>
                <div className="flex items-center">
                  <Heart className="w-4 h-4 mr-1" />
                  {auction.watchCount} takip
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bidding Form */}
          {canBid && (
            <Card>
              <CardHeader>
                <CardTitle>Teklif Ver</CardTitle>
                <CardDescription>
                  Minimum artış: {formatCurrency(auction.bidIncrementAmount)}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {error && (
                  <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded">
                    <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                    <p className="text-red-800 text-sm">{error}</p>
                  </div>
                )}

                {success && (
                  <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded">
                    <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                    <p className="text-green-800 text-sm">{success}</p>
                  </div>
                )}

                <div>
                  <Label htmlFor="bidAmount">Teklif Tutarı (TL)</Label>
                  <Input
                    id="bidAmount"
                    type="number"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    min={(auction.currentBid || auction.startingBid) + auction.bidIncrementAmount}
                    step={auction.bidIncrementAmount}
                  />
                </div>

                <Button 
                  onClick={handleBid} 
                  disabled={bidding || !bidAmount || parseFloat(bidAmount) <= (auction.currentBid || auction.startingBid)}
                  className="w-full"
                >
                  {bidding ? 'Teklif Veriliyor...' : 'Teklif Ver'}
                </Button>

                {auction.buyNowPrice && (
                  <Button variant="outline" className="w-full">
                    Hemen Al - {formatCurrency(auction.buyNowPrice)}
                  </Button>
                )}
              </CardContent>
            </Card>
          )}

          {/* Edit Button for Seller/Admin */}
          {user && auction.seller && (
            (user.id === auction.seller.clerkId || user.publicMetadata?.isAdmin) &&
            auction.status !== 'ended' && auction.status !== 'cancelled'
          ) && (
            <Card className="mb-6">
              <CardContent className="pt-6">
                <Link href={`/auctions/${auction.id}/edit`}>
                  <Button className="w-full" variant="outline">
                    <Edit className="w-4 h-4 mr-2" />
                    Açık Artırmayı Düzenle
                  </Button>
                </Link>
              </CardContent>
            </Card>
          )}

          {/* Seller Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="w-5 h-5 mr-2" />
                Satıcı Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="font-medium">{auction.seller?.name || 'Bilinmiyor'}</p>
                <p className="text-sm text-gray-600">{auction.seller?.email || ''}</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
