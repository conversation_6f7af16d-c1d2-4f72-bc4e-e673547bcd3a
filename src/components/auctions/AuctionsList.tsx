"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Clock,
  Eye,
  Heart,
  Gavel,
  Filter,
  Search,
  Star,
  TrendingUp,
  Users
} from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { AuctionImage } from '@/components/ui/optimized-image';

interface Auction {
  _id: string;
  id: string;
  name: string;
  description: string;
  image?: any;
  currentBid: number;
  startingBid: number;
  bidIncrementAmount: number;
  endTime: string;
  status: string;
  category: string;
  condition: string;
  featured: boolean;
  viewCount: number;
  watchCount: number;
  seller?: {
    _id: string;
    name: string;
  };
  bidHistory: any[];
}

interface AuctionsListProps {
  initialAuctions?: Auction[];
  showFilters?: boolean;
  limit?: number;
  category?: string;
  featured?: boolean;
}

const CATEGORY_OPTIONS = [
  { value: 'all', label: 'Tüm Kategoriler' },
  { value: 'electronics', label: 'Elektronik' },
  { value: 'clothing', label: 'Giyim' },
  { value: 'home-living', label: 'Ev & Yaşam' },
  { value: 'sports', label: 'Spor' },
  { value: 'books', label: 'Kitap' },
  { value: 'art', label: 'Sanat' },
  { value: 'collectibles', label: 'Koleksiyon' },
  { value: 'automotive', label: 'Otomotiv' },
  { value: 'other', label: 'Diğer' },
];

const STATUS_OPTIONS = [
  { value: 'active', label: 'Aktif' },
  { value: 'pending', label: 'Beklemede' },
  { value: 'completed', label: 'Tamamlandı' },
  { value: 'cancelled', label: 'İptal' },
];

export default function AuctionsList({ 
  initialAuctions = [], 
  showFilters = true, 
  limit = 20,
  category,
  featured 
}: AuctionsListProps) {
  const [auctions, setAuctions] = useState<Auction[]>(initialAuctions);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [selectedStatus, setSelectedStatus] = useState('active');
  const [showFeatured, setShowFeatured] = useState(featured || false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAuctions();
  }, [selectedCategory, selectedStatus, showFeatured]);

  const fetchAuctions = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (selectedStatus !== 'all') params.append('status', selectedStatus);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (showFeatured) params.append('featured', 'true');
      params.append('limit', limit.toString());

      const response = await fetch(`/api/auctions?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setAuctions(data.data.auctions);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Açık artırmalar yüklenirken hata oluştu');
      console.error('Auctions fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatTimeRemaining = (endTime: string) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = end.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Sona erdi';
    }

    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) {
      return `${days}g ${hours}s`;
    } else if (hours > 0) {
      return `${hours}s ${minutes}d`;
    } else {
      return `${minutes}d`;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    const option = STATUS_OPTIONS.find(opt => opt.value === status);
    return option?.label || status;
  };

  const filteredAuctions = auctions.filter(auction =>
    auction.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    auction.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading && auctions.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              Filtreler
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search">Arama</Label>
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="search"
                    placeholder="Ürün ara..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="category">Kategori</Label>
                <select
                  id="category"
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {CATEGORY_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <Label htmlFor="status">Durum</Label>
                <select
                  id="status"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  {STATUS_OPTIONS.map(option => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={showFeatured}
                    onChange={(e) => setShowFeatured(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">Sadece öne çıkanlar</span>
                </label>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-800">{error}</p>
          </CardContent>
        </Card>
      )}

      {/* Auctions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredAuctions.map((auction) => (
          <Card key={auction._id} className="hover:shadow-lg transition-shadow">
            <div className="relative">
              <AuctionImage
                auction={auction}
                alt={auction.name}
                fill
                className="aspect-square overflow-hidden rounded-t-lg"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
              />
              
              {auction.featured && (
                <Badge className="absolute top-2 left-2 bg-yellow-500">
                  <Star className="w-3 h-3 mr-1" />
                  Öne Çıkan
                </Badge>
              )}

              <Badge className={`absolute top-2 right-2 ${getStatusColor(auction.status)}`}>
                {getStatusLabel(auction.status)}
              </Badge>
            </div>

            <CardHeader className="pb-2">
              <CardTitle className="text-lg line-clamp-2">
                <Link href={`/auctions/${auction.id}`} className="hover:text-blue-600">
                  {auction.name}
                </Link>
              </CardTitle>
              <CardDescription className="line-clamp-2">
                {auction.description}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-3">
              {/* Current Bid */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Mevcut Teklif:</span>
                <span className="font-bold text-lg text-green-600">
                  {formatCurrency(auction.currentBid || auction.startingBid)}
                </span>
              </div>

              {/* Time Remaining */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  Kalan Süre:
                </span>
                <span className="font-medium text-red-600">
                  {formatTimeRemaining(auction.endTime)}
                </span>
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-600">
                <div className="flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {auction.viewCount || 0}
                </div>
                <div className="flex items-center">
                  <Gavel className="w-4 h-4 mr-1" />
                  {auction.bidHistory?.length || 0} teklif
                </div>
                <div className="flex items-center">
                  <Users className="w-4 h-4 mr-1" />
                  {auction.seller?.name || 'Bilinmiyor'}
                </div>
              </div>

              {/* Action Button */}
              <Link href={`/auctions/${auction.id}`}>
                <Button className="w-full" variant={auction.status === 'active' ? 'default' : 'outline'}>
                  {auction.status === 'active' ? 'Teklif Ver' : 'Detayları Gör'}
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Empty State */}
      {filteredAuctions.length === 0 && !loading && (
        <Card>
          <CardContent className="text-center py-12">
            <Gavel className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Açık artırma bulunamadı
            </h3>
            <p className="text-gray-600">
              Arama kriterlerinizi değiştirmeyi deneyin.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Loading More */}
      {loading && auctions.length > 0 && (
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
        </div>
      )}
    </div>
  );
}
