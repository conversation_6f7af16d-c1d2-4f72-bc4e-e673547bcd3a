"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  AlertCircle,
  CheckCircle,
  Gavel,
  Upload,
  Calendar,
  DollarSign,
  ArrowLeft
} from 'lucide-react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import ImageUpload from '@/components/ui/image-upload';

interface EditAuctionProps {
  auction: any;
}

const CATEGORY_OPTIONS = [
  { value: 'electronics', label: 'Elektronik' },
  { value: 'fashion', label: 'Moda' },
  { value: 'home', label: '<PERSON><PERSON> & Yaşam' },
  { value: 'sports', label: 'Spor' },
  { value: 'books', label: 'Kitap' },
  { value: 'toys', label: 'Oyuncak' },
  { value: 'automotive', label: 'Otomotiv' },
  { value: 'other', label: 'Diğer' }
];

const CONDITION_OPTIONS = [
  { value: 'new', label: 'Yeni' },
  { value: 'like-new', label: 'Sıfır Ayarında' },
  { value: 'good', label: 'İyi' },
  { value: 'fair', label: 'Orta' },
  { value: 'poor', label: 'Kötü' }
];

export default function EditAuction({ auction }: EditAuctionProps) {
  const { user } = useUser();
  const router = useRouter();
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: auction.name || '',
    description: auction.description || '',
    startingBid: auction.startingBid?.toString() || '',
    bidIncrementAmount: auction.bidIncrementAmount?.toString() || '1',
    reservePrice: auction.reservePrice?.toString() || '',
    buyNowPrice: auction.buyNowPrice?.toString() || '',
    startTime: auction.startTime ? new Date(auction.startTime).toISOString().slice(0, 16) : '',
    endTime: auction.endTime ? new Date(auction.endTime).toISOString().slice(0, 16) : '',
    category: auction.category || 'electronics',
    condition: auction.condition || 'good',
    shippingCost: auction.shippingInfo?.shippingCost?.toString() || '0',
    freeShippingThreshold: auction.shippingInfo?.freeShippingThreshold?.toString() || '',
    estimatedDelivery: auction.shippingInfo?.estimatedDelivery?.toString() || '3',
    tags: auction.tags?.join(', ') || '',
    notes: auction.notes || ''
  });

  // Convert auction images to the format expected by ImageUpload
  const [images, setImages] = useState<any[]>(() => {
    const auctionImages = [];
    
    // Add main image if exists
    if (auction.image?.asset) {
      auctionImages.push({
        _id: auction.image.asset._id,
        url: auction.image.asset.url,
        filename: 'Main Image',
        size: 0,
        mimeType: 'image/jpeg'
      });
    }
    
    // Add additional images
    if (auction.images && Array.isArray(auction.images)) {
      auction.images.forEach((img: any, index: number) => {
        if (img.asset && img.asset._id !== auction.image?.asset?._id) {
          auctionImages.push({
            _id: img.asset._id,
            url: img.asset.url,
            filename: `Image ${index + 1}`,
            size: 0,
            mimeType: 'image/jpeg'
          });
        }
      });
    }
    
    return auctionImages;
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      setError('Giriş yapmanız gerekiyor');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Validation
      if (!formData.name || !formData.startingBid || !formData.startTime || !formData.endTime) {
        setError('Zorunlu alanları doldurun');
        return;
      }

      if (new Date(formData.startTime) >= new Date(formData.endTime)) {
        setError('Bitiş tarihi başlangıç tarihinden sonra olmalıdır');
        return;
      }

      // Check if auction has started (can't edit certain fields after start)
      const hasStarted = new Date() >= new Date(auction.startTime);
      
      // Prepare data
      const updateData = {
        name: formData.name,
        description: formData.description,
        category: formData.category,
        condition: formData.condition,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
        notes: formData.notes,
        images: images.map(img => ({
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: img._id
          }
        })),
        image: images.length > 0 ? {
          _type: 'image',
          asset: {
            _type: 'reference',
            _ref: images[0]._id
          }
        } : undefined
      };

      // Only allow certain fields to be updated if auction hasn't started
      if (!hasStarted) {
        Object.assign(updateData, {
          startingBid: parseFloat(formData.startingBid),
          bidIncrementAmount: parseFloat(formData.bidIncrementAmount),
          reservePrice: formData.reservePrice ? parseFloat(formData.reservePrice) : undefined,
          buyNowPrice: formData.buyNowPrice ? parseFloat(formData.buyNowPrice) : undefined,
          startTime: formData.startTime,
          endTime: formData.endTime,
          shippingInfo: {
            shippingCost: parseFloat(formData.shippingCost),
            freeShippingThreshold: formData.freeShippingThreshold ? parseFloat(formData.freeShippingThreshold) : undefined,
            estimatedDelivery: parseInt(formData.estimatedDelivery)
          }
        });
      }

      const response = await fetch(`/api/auctions/${auction.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess('Açık artırma başarıyla güncellendi!');
        
        // Redirect to auction detail after 2 seconds
        setTimeout(() => {
          router.push(`/auctions/${auction.id}`);
        }, 2000);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Açık artırma güncellenirken hata oluştu');
      console.error('Update auction error:', err);
    } finally {
      setLoading(false);
    }
  };

  const hasStarted = new Date() >= new Date(auction.startTime);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <Link 
            href={`/auctions/${auction.id}`}
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Açık Artırmaya Dön
          </Link>
          
          <h1 className="text-3xl font-bold mb-2 flex items-center">
            <Gavel className="w-8 h-8 mr-3" />
            Açık Artırma Düzenle
          </h1>
          <p className="text-gray-600">
            {hasStarted ? 
              'Açık artırma başladığı için sadece bazı alanları düzenleyebilirsiniz' :
              'Açık artırma bilgilerini düzenleyin'
            }
          </p>
        </div>

        {/* Warning for started auctions */}
        {hasStarted && (
          <Card className="border-yellow-200 bg-yellow-50 mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-yellow-600 mr-2" />
                <p className="text-yellow-800">
                  Bu açık artırma başlamış durumda. Fiyat ve zaman bilgileri değiştirilemez.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error/Success Messages */}
        {error && (
          <Card className="border-red-200 bg-red-50 mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {success && (
          <Card className="border-green-200 bg-green-50 mb-6">
            <CardContent className="pt-6">
              <div className="flex items-center">
                <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
                <p className="text-green-800">{success}</p>
              </div>
            </CardContent>
          </Card>
        )}

        <form onSubmit={handleSubmit}>
          <Card>
            <CardHeader>
              <CardTitle>Ürün Bilgileri</CardTitle>
              <CardDescription>
                Açık artırma detaylarını güncelleyin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Basic Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="name">Ürün Adı *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Ürün adını girin"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <Label htmlFor="description">Açıklama</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Ürün açıklaması"
                    rows={4}
                  />
                </div>

                {/* Image Upload */}
                <div className="md:col-span-2">
                  <ImageUpload
                    label="Ürün Resimleri"
                    description="Ürününüzün fotoğraflarını yükleyin. İlk resim ana resim olarak kullanılacaktır."
                    maxFiles={5}
                    onImagesChange={setImages}
                    initialImages={images}
                    disabled={loading}
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategori</Label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    {CATEGORY_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <Label htmlFor="condition">Durum</Label>
                  <select
                    id="condition"
                    name="condition"
                    value={formData.condition}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    {CONDITION_OPTIONS.map(option => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex items-center justify-end space-x-4 pt-6">
                <Link href={`/auctions/${auction.id}`}>
                  <Button type="button" variant="outline">
                    İptal
                  </Button>
                </Link>
                <Button type="submit" disabled={loading}>
                  {loading ? 'Güncelleniyor...' : 'Güncelle'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </form>
      </div>
    </div>
  );
}
