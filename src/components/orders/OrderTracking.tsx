'use client';

import { useState } from 'react';
import { Package, Truck, MapPin, Clock, CheckCircle, XCircle, RotateCcw } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Order, OrderStatus, OrderStatusUpdate } from '@/store/order-store';
import { formatDistanceToNow } from 'date-fns';

// Safe date formatting function
const formatSafeDate = (date: Date | string) => {
  try {
    const validDate = date instanceof Date ? date : new Date(date);
    if (isNaN(validDate.getTime())) {
      return 'Unknown date';
    }
    return formatDistanceToNow(validDate, { addSuffix: true });
  } catch (error) {
    console.warn('Invalid date in order tracking:', date);
    return 'Unknown date';
  }
};

interface OrderTrackingProps {
  order?: Order;
  trackingNumber?: string;
  onTrackingSearch?: (trackingNumber: string) => void;
}

export default function OrderTracking({ order, trackingNumber, onTrackingSearch }: OrderTrackingProps) {
  const [searchTracking, setSearchTracking] = useState(trackingNumber || '');

  const getStatusIcon = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case OrderStatus.CONFIRMED:
        return <CheckCircle className="h-5 w-5 text-blue-500" />;
      case OrderStatus.PROCESSING:
        return <Package className="h-5 w-5 text-orange-500" />;
      case OrderStatus.SHIPPED:
        return <Truck className="h-5 w-5 text-purple-500" />;
      case OrderStatus.OUT_FOR_DELIVERY:
        return <MapPin className="h-5 w-5 text-indigo-500" />;
      case OrderStatus.DELIVERED:
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case OrderStatus.CANCELLED:
        return <XCircle className="h-5 w-5 text-red-500" />;
      case OrderStatus.RETURNED:
        return <RotateCcw className="h-5 w-5 text-gray-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case OrderStatus.CONFIRMED:
        return 'bg-blue-100 text-blue-800';
      case OrderStatus.PROCESSING:
        return 'bg-orange-100 text-orange-800';
      case OrderStatus.SHIPPED:
        return 'bg-purple-100 text-purple-800';
      case OrderStatus.OUT_FOR_DELIVERY:
        return 'bg-indigo-100 text-indigo-800';
      case OrderStatus.DELIVERED:
        return 'bg-green-100 text-green-800';
      case OrderStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      case OrderStatus.RETURNED:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: OrderStatus) => {
    switch (status) {
      case OrderStatus.PENDING:
        return 'Order Pending';
      case OrderStatus.CONFIRMED:
        return 'Order Confirmed';
      case OrderStatus.PROCESSING:
        return 'Processing';
      case OrderStatus.SHIPPED:
        return 'Shipped';
      case OrderStatus.OUT_FOR_DELIVERY:
        return 'Out for Delivery';
      case OrderStatus.DELIVERED:
        return 'Delivered';
      case OrderStatus.CANCELLED:
        return 'Cancelled';
      case OrderStatus.RETURNED:
        return 'Returned';
      default:
        return 'Unknown';
    }
  };

  const handleTrackingSearch = () => {
    if (onTrackingSearch && searchTracking.trim()) {
      onTrackingSearch(searchTracking.trim());
    }
  };

  if (!order && !trackingNumber) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="h-5 w-5 mr-2" />
            Track Your Order
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex space-x-2">
              <Input
                placeholder="Enter tracking number or order ID"
                value={searchTracking}
                onChange={(e) => setSearchTracking(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleTrackingSearch()}
              />
              <Button onClick={handleTrackingSearch}>
                Track
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              Enter your tracking number or order ID to see the latest updates on your shipment.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!order) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <Package className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Order Not Found</h3>
          <p className="text-muted-foreground">
            We couldn't find an order with tracking number: {trackingNumber}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Order {order.orderNumber}
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                Placed on {order.createdAt.toLocaleDateString()}
              </p>
            </div>
            <Badge className={getStatusColor(order.status)}>
              {getStatusLabel(order.status)}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="font-medium mb-2">Shipping Address</h4>
              <div className="text-sm text-muted-foreground">
                <p>{order.shippingAddress.name}</p>
                <p>{order.shippingAddress.street}</p>
                <p>{order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}</p>
                <p>{order.shippingAddress.country}</p>
              </div>
            </div>
            
            {order.trackingNumber && (
              <div>
                <h4 className="font-medium mb-2">Tracking Number</h4>
                <p className="text-sm font-mono bg-muted p-2 rounded">
                  {order.trackingNumber}
                </p>
              </div>
            )}
            
            {order.estimatedDelivery && (
              <div>
                <h4 className="font-medium mb-2">Estimated Delivery</h4>
                <p className="text-sm text-muted-foreground">
                  {order.estimatedDelivery.toLocaleDateString()}
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardHeader>
          <CardTitle>Order Items</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {order.items.map((item) => (
              <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                <img
                  src={item.image}
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium">{item.name}</h4>
                  <p className="text-sm text-muted-foreground">
                    Quantity: {item.quantity}
                  </p>
                  {item.variant && (
                    <p className="text-sm text-muted-foreground">
                      {item.variant.size && `Size: ${item.variant.size}`}
                      {item.variant.color && ` • Color: ${item.variant.color}`}
                    </p>
                  )}
                </div>
                <div className="text-right">
                  <p className="font-medium">${item.price.toFixed(2)}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tracking Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Tracking History</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {order.statusHistory.map((update, index) => (
              <div key={index} className="flex items-start space-x-4">
                <div className="flex-shrink-0 mt-1">
                  {getStatusIcon(update.status)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{getStatusLabel(update.status)}</h4>
                    <span className="text-sm text-muted-foreground">
                      {formatSafeDate(update.timestamp)}
                    </span>
                  </div>
                  {update.note && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {update.note}
                    </p>
                  )}
                  {update.location && (
                    <p className="text-xs text-muted-foreground mt-1 flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {update.location}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    {update.timestamp.toLocaleString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
