'use client';

import { useState, useEffect } from 'react';
import { X, Filter, ChevronDown, ChevronUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ProductFilters } from '@/types';

interface ProductFiltersProps {
  filters: ProductFilters;
  onFiltersChange: (filters: Partial<ProductFilters>) => void;
  onClearFilters: () => void;
  className?: string;
}

export default function ProductFiltersComponent({
  filters,
  onFiltersChange,
  onClearFilters,
  className
}: ProductFiltersProps) {
  const [priceRange, setPriceRange] = useState([filters.minPrice || 0, filters.maxPrice || 1000]);
  const [categories, setCategories] = useState<string[]>(['All']);
  const [brands, setBrands] = useState<string[]>(['All']);
  const [loading, setLoading] = useState(true);
  const [expandedSections, setExpandedSections] = useState({
    category: true,
    brand: true,
    price: true,
    features: true,
    rating: true
  });

  // Fetch categories and brands on component mount
  useEffect(() => {
    const fetchCategoriesAndBrands = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/products/categories');
        const result = await response.json();

        if (result.success) {
          setCategories(result.data.categories || ['All']);
          setBrands(result.data.brands || ['All']);
        }
      } catch (error) {
        console.error('Failed to fetch categories and brands:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCategoriesAndBrands();
  }, []);

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const handlePriceChange = (value: number[]) => {
    setPriceRange(value);
    onFiltersChange({
      minPrice: value[0],
      maxPrice: value[1]
    });
  };

  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'sortBy') return false; // sortBy is not considered an active filter
    if (value === undefined || value === 'All' || value === 'newest') return false;
    if (key === 'inStock' || key === 'onSale') return value === true;
    return true;
  }).length;

  const ratings = [5, 4, 3, 2, 1];

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </span>
          {activeFiltersCount > 0 && (
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">{activeFiltersCount}</Badge>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-xs h-auto p-1"
              >
                Clear All
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Category Filter */}
        <Collapsible open={expandedSections.category} onOpenChange={() => toggleSection('category')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Category</span>
              {expandedSections.category ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3">
            <Select value={filters.category} onValueChange={(value) => onFiltersChange({ category: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CollapsibleContent>
        </Collapsible>

        {/* Brand Filter */}
        <Collapsible open={expandedSections.brand} onOpenChange={() => toggleSection('brand')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Brand</span>
              {expandedSections.brand ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3">
            <Select value={filters.brand} onValueChange={(value) => onFiltersChange({ brand: value })}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {brands.map((brand) => (
                  <SelectItem key={brand} value={brand}>
                    {brand}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CollapsibleContent>
        </Collapsible>

        {/* Price Range */}
        <Collapsible open={expandedSections.price} onOpenChange={() => toggleSection('price')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Price Range</span>
              {expandedSections.price ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-4">
            <div className="px-2">
              <Slider
                value={priceRange}
                onValueChange={handlePriceChange}
                max={1000}
                min={0}
                step={10}
                className="w-full"
              />
            </div>
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>${priceRange[0]}</span>
              <span>${priceRange[1]}</span>
            </div>
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label htmlFor="minPrice" className="text-xs">Min</Label>
                <input
                  id="minPrice"
                  type="number"
                  value={priceRange[0]}
                  onChange={(e) => handlePriceChange([Number(e.target.value), priceRange[1]])}
                  className="w-full px-2 py-1 text-xs border rounded"
                />
              </div>
              <div>
                <Label htmlFor="maxPrice" className="text-xs">Max</Label>
                <input
                  id="maxPrice"
                  type="number"
                  value={priceRange[1]}
                  onChange={(e) => handlePriceChange([priceRange[0], Number(e.target.value)])}
                  className="w-full px-2 py-1 text-xs border rounded"
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Rating Filter */}
        <Collapsible open={expandedSections.rating} onOpenChange={() => toggleSection('rating')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Rating</span>
              {expandedSections.rating ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-2">
            {ratings.map((rating) => (
              <div key={rating} className="flex items-center space-x-2">
                <Checkbox
                  id={`rating-${rating}`}
                  checked={filters.rating === rating}
                  onCheckedChange={(checked) => 
                    onFiltersChange({ rating: checked ? rating : undefined })
                  }
                />
                <Label htmlFor={`rating-${rating}`} className="text-sm flex items-center">
                  {rating}+ ⭐
                </Label>
              </div>
            ))}
          </CollapsibleContent>
        </Collapsible>

        {/* Quick Filters */}
        <Collapsible open={expandedSections.features} onOpenChange={() => toggleSection('features')}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between p-0 h-auto">
              <span className="font-medium">Features</span>
              {expandedSections.features ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </CollapsibleTrigger>
          <CollapsibleContent className="mt-3 space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="inStock"
                checked={filters.inStock || false}
                onCheckedChange={(checked) => 
                  onFiltersChange({ inStock: checked || undefined })
                }
              />
              <Label htmlFor="inStock" className="text-sm">In Stock Only</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="onSale"
                checked={filters.onSale || false}
                onCheckedChange={(checked) => 
                  onFiltersChange({ onSale: checked || undefined })
                }
              />
              <Label htmlFor="onSale" className="text-sm">On Sale</Label>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
