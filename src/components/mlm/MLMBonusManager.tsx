"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Gift, 
  Award, 
  TrendingUp,
  CheckCircle,
  AlertCircle,
  Clock,
  DollarSign,
  Users,
  Target,
  Zap
} from 'lucide-react';

interface Bonus {
  _id: string;
  bonusType: string;
  amount: number;
  paymentType: string;
  status: string;
  criteria: any;
  achievedCriteria: any;
  description: string;
  createdAt: string;
  paidAt?: string;
  expiryDate?: string;
}

interface MLMBonusManagerProps {
  userId: string;
  isAdmin?: boolean;
}

const BONUS_TYPE_NAMES = {
  welcome_bonus: "<PERSON>ş G<PERSON> Bonusu",
  activity_bonus: "Aktivite Bonusu",
  volume_bonus: "Ciro Bonusu",
  leadership_bonus: "Liderlik Bonusu",
  level_upgrade_bonus: "<PERSON><PERSON><PERSON> Yükseltme Bonusu",
  team_performance_bonus: "Takım Performans Bonusu",
  fast_start_bonus: "Hızlı Başlangıç Bonusu",
  binary_bonus: "Binary Bonus",
  manual_bonus: "Manuel Bonus"
};

const BONUS_TYPE_ICONS = {
  welcome_bonus: Gift,
  activity_bonus: Users,
  volume_bonus: TrendingUp,
  leadership_bonus: Award,
  level_upgrade_bonus: Target,
  team_performance_bonus: Users,
  fast_start_bonus: Zap,
  binary_bonus: TrendingUp,
  manual_bonus: DollarSign
};

const STATUS_COLORS = {
  pending: "bg-yellow-100 text-yellow-800",
  approved: "bg-blue-100 text-blue-800",
  paid: "bg-green-100 text-green-800",
  cancelled: "bg-red-100 text-red-800",
  rejected: "bg-gray-100 text-gray-800"
};

const STATUS_NAMES = {
  pending: "Bekliyor",
  approved: "Onaylandı",
  paid: "Ödendi",
  cancelled: "İptal Edildi",
  rejected: "Reddedildi"
};

export default function MLMBonusManager({ userId, isAdmin = false }: MLMBonusManagerProps) {
  const [bonuses, setBonuses] = useState<Bonus[]>([]);
  const [eligibleBonuses, setEligibleBonuses] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Manuel bonus form
  const [manualBonusAmount, setManualBonusAmount] = useState('');
  const [manualBonusDescription, setManualBonusDescription] = useState('');
  const [targetUserId, setTargetUserId] = useState(userId);

  useEffect(() => {
    fetchBonuses();
    if (isAdmin) {
      checkEligibleBonuses();
    }
  }, [userId]);

  const fetchBonuses = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mlm/bonuses?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setBonuses(data.data.bonuses);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Bonuslar yüklenirken hata oluştu');
      console.error('Bonuses fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const checkEligibleBonuses = async () => {
    try {
      const response = await fetch('/api/mlm/bonuses', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ targetUserId: userId }),
      });

      const data = await response.json();
      
      if (data.success) {
        setEligibleBonuses(data.eligibleBonuses);
      }
    } catch (err) {
      console.error('Eligible bonuses check error:', err);
    }
  };

  const giveBonus = async (bonusType: string, manualAmount?: number, description?: string) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/mlm/bonuses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId,
          bonusType,
          manualAmount,
          description
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(`Bonus başarıyla verildi: ${data.data.amount} TL`);
        fetchBonuses();
        checkEligibleBonuses();
        
        // Form temizle
        setManualBonusAmount('');
        setManualBonusDescription('');
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Bonus verilirken hata oluştu');
      console.error('Give bonus error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleManualBonus = () => {
    const amount = parseFloat(manualBonusAmount);
    if (isNaN(amount) || amount <= 0) {
      setError('Geçerli bir tutar girin');
      return;
    }

    if (!manualBonusDescription.trim()) {
      setError('Bonus açıklaması gerekli');
      return;
    }

    giveBonus('manual_bonus', amount, manualBonusDescription);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Özet istatistikler
  const totalAmount = bonuses.reduce((sum, b) => sum + b.amount, 0);
  const paidAmount = bonuses.filter(b => b.status === 'paid').reduce((sum, b) => sum + b.amount, 0);
  const pendingAmount = bonuses.filter(b => b.status === 'pending' || b.status === 'approved').reduce((sum, b) => sum + b.amount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Award className="w-5 h-5 mr-2" />
            Bonus Yönetimi
          </CardTitle>
          <CardDescription>
            MLM bonuslarınızı görüntüleyin ve yönetin
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Error/Success Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <p className="text-green-800">{success}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Bonus</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {bonuses.length} bonus
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ödenen Bonus</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(paidAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {bonuses.filter(b => b.status === 'paid').length} ödeme
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Bonus</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(pendingAmount)}</div>
            <p className="text-xs text-muted-foreground">
              {bonuses.filter(b => b.status === 'pending' || b.status === 'approved').length} bekliyor
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="bonuses" className="space-y-4">
        <TabsList>
          <TabsTrigger value="bonuses">Bonus Geçmişi</TabsTrigger>
          {isAdmin && <TabsTrigger value="eligible">Uygun Bonuslar</TabsTrigger>}
          {isAdmin && <TabsTrigger value="manual">Manuel Bonus</TabsTrigger>}
        </TabsList>

        <TabsContent value="bonuses">
          <Card>
            <CardHeader>
              <CardTitle>Bonus Geçmişi</CardTitle>
              <CardDescription>
                Aldığınız tüm bonusların detaylı listesi
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                  <span className="ml-2">Yükleniyor...</span>
                </div>
              ) : bonuses.length === 0 ? (
                <div className="text-center py-8">
                  <Gift className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">Henüz bonus kazancınız bulunmuyor</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {bonuses.map((bonus) => {
                    const IconComponent = BONUS_TYPE_ICONS[bonus.bonusType as keyof typeof BONUS_TYPE_ICONS] || Gift;
                    
                    return (
                      <div key={bonus._id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <IconComponent className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <p className="font-medium">
                              {BONUS_TYPE_NAMES[bonus.bonusType as keyof typeof BONUS_TYPE_NAMES] || bonus.bonusType}
                            </p>
                            <p className="text-sm text-gray-600">{bonus.description}</p>
                            <p className="text-xs text-gray-500">
                              {formatDate(bonus.createdAt)}
                              {bonus.paidAt && ` • Ödendi: ${formatDate(bonus.paidAt)}`}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg">{formatCurrency(bonus.amount)}</p>
                          <Badge className={STATUS_COLORS[bonus.status as keyof typeof STATUS_COLORS]}>
                            {STATUS_NAMES[bonus.status as keyof typeof STATUS_NAMES]}
                          </Badge>
                          <p className="text-xs text-gray-500 mt-1">
                            {bonus.paymentType === 'cash' ? 'Nakit' : 'Hediye Çeki'}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {isAdmin && (
          <TabsContent value="eligible">
            <Card>
              <CardHeader>
                <CardTitle>Uygun Bonuslar</CardTitle>
                <CardDescription>
                  Kullanıcının hak ettiği ancak henüz verilmemiş bonuslar
                </CardDescription>
              </CardHeader>
              <CardContent>
                {eligibleBonuses.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                    <p className="text-gray-500">Şu anda uygun bonus bulunmuyor</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {eligibleBonuses.map((bonus, index) => {
                      const IconComponent = BONUS_TYPE_ICONS[bonus.bonusType as keyof typeof BONUS_TYPE_ICONS] || Gift;
                      
                      return (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                              <IconComponent className="w-5 h-5 text-green-600" />
                            </div>
                            <div>
                              <p className="font-medium">
                                {BONUS_TYPE_NAMES[bonus.bonusType as keyof typeof BONUS_TYPE_NAMES] || bonus.bonusType}
                              </p>
                              <p className="text-sm text-gray-600">{bonus.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <p className="font-bold text-lg">{formatCurrency(bonus.amount)}</p>
                              <Badge className="bg-green-100 text-green-800">Uygun</Badge>
                            </div>
                            <Button
                              onClick={() => giveBonus(bonus.bonusType)}
                              disabled={loading}
                              size="sm"
                            >
                              Bonus Ver
                            </Button>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {isAdmin && (
          <TabsContent value="manual">
            <Card>
              <CardHeader>
                <CardTitle>Manuel Bonus Ver</CardTitle>
                <CardDescription>
                  Özel durumlar için manuel bonus verebilirsiniz
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="targetUser">Hedef Kullanıcı ID</Label>
                  <Input
                    id="targetUser"
                    value={targetUserId}
                    onChange={(e) => setTargetUserId(e.target.value)}
                    placeholder="Kullanıcı ID'sini girin"
                  />
                </div>

                <div>
                  <Label htmlFor="amount">Bonus Tutarı (TL)</Label>
                  <Input
                    id="amount"
                    type="number"
                    value={manualBonusAmount}
                    onChange={(e) => setManualBonusAmount(e.target.value)}
                    placeholder="0"
                    min="1"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Açıklama</Label>
                  <Input
                    id="description"
                    value={manualBonusDescription}
                    onChange={(e) => setManualBonusDescription(e.target.value)}
                    placeholder="Bonus açıklaması"
                  />
                </div>

                <Button
                  onClick={handleManualBonus}
                  disabled={loading || !manualBonusAmount || !manualBonusDescription.trim()}
                  className="w-full"
                >
                  {loading ? 'Bonus Veriliyor...' : 'Manuel Bonus Ver'}
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
