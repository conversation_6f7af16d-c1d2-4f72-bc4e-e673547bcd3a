"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Crown, 
  Target,
  Users,
  DollarSign,
  CheckCircle,
  AlertCircle,
  Clock,
  ArrowRight,
  Zap
} from 'lucide-react';

interface LevelUpgradeData {
  canUpgrade: boolean;
  currentLevel: {
    level: string;
    title: string;
    data: any;
  };
  nextLevel: {
    level: string;
    title: string;
    data: any;
  };
  requirements: {
    requiredReferrals: number;
    requiredSalesVolume: number;
    timeLimit: number;
    hasEnoughReferrals: boolean;
    hasEnoughSales: boolean;
    withinTimeLimit: boolean;
  };
  currentStats: {
    directReferrals: number;
    totalSales: number;
    daysSinceMembership: number;
  };
  upgradeFee: number;
  canUpgradeByPayment: boolean;
  isMaxLevel?: boolean;
}

interface MLMLevelUpgradeProps {
  userId: string;
}

const MLM_LEVEL_COLORS = {
  bronze: "bg-amber-100 text-amber-800 border-amber-200",
  silver: "bg-gray-100 text-gray-800 border-gray-200", 
  gold: "bg-yellow-100 text-yellow-800 border-yellow-200",
  diamond: "bg-blue-100 text-blue-800 border-blue-200",
  platinum: "bg-purple-100 text-purple-800 border-purple-200",
  sapphire: "bg-indigo-100 text-indigo-800 border-indigo-200"
};

export default function MLMLevelUpgrade({ userId }: MLMLevelUpgradeProps) {
  const [upgradeData, setUpgradeData] = useState<LevelUpgradeData | null>(null);
  const [loading, setLoading] = useState(false);
  const [upgrading, setUpgrading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    fetchUpgradeData();
  }, [userId]);

  const fetchUpgradeData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/mlm/level-upgrade?userId=${userId}`);
      const data = await response.json();

      if (data.success) {
        setUpgradeData(data);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Seviye bilgileri yüklenirken hata oluştu');
      console.error('Level upgrade data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (method: 'requirements' | 'payment') => {
    try {
      setUpgrading(true);
      setError(null);
      setSuccess(null);

      const response = await fetch('/api/mlm/level-upgrade', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: userId,
          upgradeMethod: method,
          paymentMethod: 'wallet'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(data.message);
        fetchUpgradeData(); // Verileri yenile
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Seviye yükseltme sırasında hata oluştu');
      console.error('Level upgrade error:', err);
    } finally {
      setUpgrading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const calculateProgress = (current: number, required: number) => {
    return Math.min((current / required) * 100, 100);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2">Seviye bilgileri yükleniyor...</span>
        </CardContent>
      </Card>
    );
  }

  if (error && !upgradeData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="w-16 h-16 mx-auto text-red-400 mb-4" />
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchUpgradeData} className="mt-4">
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!upgradeData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <Crown className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">Seviye bilgisi bulunamadı</p>
        </CardContent>
      </Card>
    );
  }

  if (upgradeData.isMaxLevel) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Crown className="w-5 h-5 mr-2 text-yellow-500" />
            Seviye Yükseltme
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <Crown className="w-10 h-10 text-yellow-600" />
          </div>
          <h3 className="text-xl font-bold mb-2">Tebrikler!</h3>
          <p className="text-gray-600 mb-4">
            Zaten en yüksek seviye olan <strong>{upgradeData.currentLevel.title}</strong> seviyesindesiniz.
          </p>
          <Badge className={MLM_LEVEL_COLORS[upgradeData.currentLevel.level as keyof typeof MLM_LEVEL_COLORS]}>
            {upgradeData.currentLevel.title}
          </Badge>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Seviye Yükseltme
          </CardTitle>
          <CardDescription>
            Bir sonraki seviyeye yükseltme şartlarınızı kontrol edin
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Error/Success Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <p className="text-green-800">{success}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Current vs Next Level */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Current Level */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Mevcut Seviyeniz</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <Badge className={`text-lg px-4 py-2 ${MLM_LEVEL_COLORS[upgradeData.currentLevel.level as keyof typeof MLM_LEVEL_COLORS]}`}>
                {upgradeData.currentLevel.title}
              </Badge>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Doğrudan Komisyon:</span>
                  <span className="font-medium">%{upgradeData.currentLevel.data.directCommissionRate}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Hediye Çeki:</span>
                  <span className="font-medium">{formatCurrency(upgradeData.currentLevel.data.giftVoucherAmount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Aylık Bonus:</span>
                  <span className="font-medium">{formatCurrency(upgradeData.currentLevel.data.monthlyWalletBonus)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Level */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <ArrowRight className="w-4 h-4 mr-2" />
              Bir Sonraki Seviye
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <Badge className={`text-lg px-4 py-2 ${MLM_LEVEL_COLORS[upgradeData.nextLevel.level as keyof typeof MLM_LEVEL_COLORS]}`}>
                {upgradeData.nextLevel.title}
              </Badge>
              <div className="mt-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Doğrudan Komisyon:</span>
                  <span className="font-medium text-green-600">%{upgradeData.nextLevel.data.directCommissionRate}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Hediye Çeki:</span>
                  <span className="font-medium text-green-600">{formatCurrency(upgradeData.nextLevel.data.giftVoucherAmount)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Aylık Bonus:</span>
                  <span className="font-medium text-green-600">{formatCurrency(upgradeData.nextLevel.data.monthlyWalletBonus)}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Requirements Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Yükseltme Şartları</CardTitle>
          <CardDescription>
            Aşağıdaki şartları sağlayarak otomatik yükseltme hakkı kazanabilirsiniz
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Referrals Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Users className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">Doğrudan Referanslar</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm">
                  {upgradeData.currentStats.directReferrals} / {upgradeData.requirements.requiredReferrals}
                </span>
                {upgradeData.requirements.hasEnoughReferrals && (
                  <CheckCircle className="w-4 h-4 ml-2 text-green-500" />
                )}
              </div>
            </div>
            <Progress 
              value={calculateProgress(upgradeData.currentStats.directReferrals, upgradeData.requirements.requiredReferrals)}
              className="h-2"
            />
          </div>

          {/* Sales Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">Satış Hacmi</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm">
                  {formatCurrency(upgradeData.currentStats.totalSales)} / {formatCurrency(upgradeData.requirements.requiredSalesVolume)}
                </span>
                {upgradeData.requirements.hasEnoughSales && (
                  <CheckCircle className="w-4 h-4 ml-2 text-green-500" />
                )}
              </div>
            </div>
            <Progress 
              value={calculateProgress(upgradeData.currentStats.totalSales, upgradeData.requirements.requiredSalesVolume)}
              className="h-2"
            />
          </div>

          {/* Time Progress */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2" />
                <span className="text-sm font-medium">Zaman Sınırı</span>
              </div>
              <div className="flex items-center">
                <span className="text-sm">
                  {upgradeData.currentStats.daysSinceMembership} / {upgradeData.requirements.timeLimit} gün
                </span>
                {upgradeData.requirements.withinTimeLimit && (
                  <CheckCircle className="w-4 h-4 ml-2 text-green-500" />
                )}
              </div>
            </div>
            <Progress 
              value={calculateProgress(upgradeData.currentStats.daysSinceMembership, upgradeData.requirements.timeLimit)}
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>

      {/* Upgrade Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Automatic Upgrade */}
        <Card className={upgradeData.canUpgrade ? "border-green-200 bg-green-50" : "border-gray-200"}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="w-5 h-5 mr-2" />
              Otomatik Yükseltme
            </CardTitle>
            <CardDescription>
              Şartları sağlayarak ücretsiz yükseltme
            </CardDescription>
          </CardHeader>
          <CardContent>
            {upgradeData.canUpgrade ? (
              <div className="text-center">
                <CheckCircle className="w-12 h-12 mx-auto text-green-500 mb-4" />
                <p className="text-green-800 mb-4">
                  Tebrikler! Tüm şartları sağladınız.
                </p>
                <Button
                  onClick={() => handleUpgrade('requirements')}
                  disabled={upgrading}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  {upgrading ? 'Yükseltiliyor...' : 'Ücretsiz Yükselt'}
                </Button>
              </div>
            ) : (
              <div className="text-center">
                <AlertCircle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">
                  Henüz tüm şartları sağlamadınız.
                </p>
                <Button disabled className="w-full">
                  Şartlar Sağlanmadı
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Upgrade */}
        <Card className={upgradeData.canUpgradeByPayment ? "border-blue-200 bg-blue-50" : "border-gray-200"}>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Zap className="w-5 h-5 mr-2" />
              Ödeme ile Yükseltme
            </CardTitle>
            <CardDescription>
              Ödeme yaparak hemen yükseltme
            </CardDescription>
          </CardHeader>
          <CardContent>
            {upgradeData.canUpgradeByPayment ? (
              <div className="text-center">
                <DollarSign className="w-12 h-12 mx-auto text-blue-500 mb-4" />
                <p className="text-blue-800 mb-2">
                  Yükseltme Ücreti
                </p>
                <p className="text-2xl font-bold text-blue-900 mb-4">
                  {formatCurrency(upgradeData.upgradeFee)}
                </p>
                <Button
                  onClick={() => handleUpgrade('payment')}
                  disabled={upgrading}
                  className="w-full"
                >
                  {upgrading ? 'Yükseltiliyor...' : 'Ödeme ile Yükselt'}
                </Button>
              </div>
            ) : (
              <div className="text-center">
                <AlertCircle className="w-12 h-12 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-600 mb-4">
                  Ödeme ile yükseltme mevcut değil.
                </p>
                <Button disabled className="w-full">
                  Mevcut Değil
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
