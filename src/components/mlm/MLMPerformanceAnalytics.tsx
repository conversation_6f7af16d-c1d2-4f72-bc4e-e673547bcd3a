"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  PieChart,
  Calendar,
  Target,
  Award,
  Users,
  DollarSign,
  Activity
} from 'lucide-react';

interface PerformanceData {
  period: string;
  startDate: string;
  endDate: string;
  commissions: {
    total: number;
    count: number;
    byType: Record<string, number>;
  };
  newMemberships: {
    total: number;
    revenue: number;
    byLevel: Record<string, number>;
  };
  teamSales: {
    total: number;
    count: number;
  };
}

interface MLMPerformanceAnalyticsProps {
  userId: string;
}

const PERIOD_OPTIONS = [
  { value: 'week', label: 'Bu Hafta' },
  { value: 'month', label: 'Bu Ay' },
  { value: 'quarter', label: '<PERSON>u <PERSON><PERSON>rek' },
  { value: 'year', label: 'Bu Yıl' }
];

const COMMISSION_TYPE_NAMES = {
  direct_referral: 'Doğrudan Referans',
  second_level: '2. Seviye',
  third_level: '3. Seviye',
  fourth_level: '4. Seviye',
  sales_commission: 'Satış Komisyonu',
  team_bonus: 'Takım Bonusu',
  level_upgrade_bonus: 'Seviye Yükseltme',
  activity_bonus: 'Aktivite Bonusu',
  leadership_bonus: 'Liderlik Bonusu'
};

const MLM_LEVEL_NAMES = {
  bronze: 'Bronz',
  silver: 'Gümüş',
  gold: 'Altın',
  diamond: 'Elmas',
  platinum: 'Pırlanta',
  sapphire: 'Safir'
};

export default function MLMPerformanceAnalytics({ userId }: MLMPerformanceAnalyticsProps) {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('month');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchPerformanceData();
  }, [userId, selectedPeriod]);

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/mlm/binary-tree', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: userId,
          period: selectedPeriod
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPerformanceData(data.analytics);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Performans verileri yüklenirken hata oluştu');
      console.error('Performance data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  const calculateGrowthRate = (current: number, previous: number) => {
    if (previous === 0) return current > 0 ? 100 : 0;
    return ((current - previous) / previous) * 100;
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2">Performans verileri yükleniyor...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchPerformanceData} className="mt-4">
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!performanceData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <BarChart3 className="w-16 h-16 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">Performans verisi bulunamadı</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selection */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2" />
                Performans Analizi
              </CardTitle>
              <CardDescription>
                {formatDate(performanceData.startDate)} - {formatDate(performanceData.endDate)}
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              {PERIOD_OPTIONS.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedPeriod(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Komisyon</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(performanceData.commissions.total)}</div>
            <p className="text-xs text-muted-foreground">
              {performanceData.commissions.count} işlem
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Yeni Üyeler</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceData.newMemberships.total}</div>
            <p className="text-xs text-muted-foreground">
              {formatCurrency(performanceData.newMemberships.revenue)} gelir
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Takım Satışları</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(performanceData.teamSales.total)}</div>
            <p className="text-xs text-muted-foreground">
              {performanceData.teamSales.count} sipariş
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ortalama İşlem</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatCurrency(
                performanceData.teamSales.count > 0 
                  ? performanceData.teamSales.total / performanceData.teamSales.count 
                  : 0
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              işlem başına
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="commissions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="commissions">Komisyon Analizi</TabsTrigger>
          <TabsTrigger value="memberships">Üyelik Analizi</TabsTrigger>
          <TabsTrigger value="trends">Trend Analizi</TabsTrigger>
        </TabsList>

        <TabsContent value="commissions">
          <Card>
            <CardHeader>
              <CardTitle>Komisyon Türlerine Göre Dağılım</CardTitle>
              <CardDescription>
                Farklı komisyon türlerinden elde edilen gelirler
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.keys(performanceData.commissions.byType).length === 0 ? (
                <div className="text-center py-8">
                  <PieChart className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">Bu dönemde komisyon kazancı bulunmuyor</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(performanceData.commissions.byType).map(([type, amount]) => (
                    <div key={type} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">
                          {COMMISSION_TYPE_NAMES[type as keyof typeof COMMISSION_TYPE_NAMES] || type}
                        </p>
                        <p className="text-sm text-gray-600">
                          {((amount / performanceData.commissions.total) * 100).toFixed(1)}% toplam komisyondan
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-bold">{formatCurrency(amount)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="memberships">
          <Card>
            <CardHeader>
              <CardTitle>Yeni Üyelik Dağılımı</CardTitle>
              <CardDescription>
                Seviyelerine göre yeni üye kayıtları
              </CardDescription>
            </CardHeader>
            <CardContent>
              {Object.keys(performanceData.newMemberships.byLevel).length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">Bu dönemde yeni üye kaydı bulunmuyor</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {Object.entries(performanceData.newMemberships.byLevel).map(([level, count]) => (
                    <div key={level} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center">
                        <Badge className="mr-3">
                          {MLM_LEVEL_NAMES[level as keyof typeof MLM_LEVEL_NAMES] || level}
                        </Badge>
                        <div>
                          <p className="font-medium">{count} yeni üye</p>
                          <p className="text-sm text-gray-600">
                            {((count / performanceData.newMemberships.total) * 100).toFixed(1)}% toplam üyelerden
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends">
          <Card>
            <CardHeader>
              <CardTitle>Trend Analizi</CardTitle>
              <CardDescription>
                Performans trendleri ve öngörüler
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Komisyon Trendi</h4>
                  <div className="flex items-center">
                    <TrendingUp className="w-5 h-5 text-green-500 mr-2" />
                    <span className="text-sm">
                      Ortalama günlük komisyon: {formatCurrency(performanceData.commissions.total / 30)}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Target className="w-5 h-5 text-blue-500 mr-2" />
                    <span className="text-sm">
                      İşlem başına ortalama: {formatCurrency(
                        performanceData.commissions.count > 0
                          ? performanceData.commissions.total / performanceData.commissions.count
                          : 0
                      )}
                    </span>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-semibold">Büyüme Potansiyeli</h4>
                  <div className="flex items-center">
                    <Award className="w-5 h-5 text-purple-500 mr-2" />
                    <span className="text-sm">
                      Aktif üye oranı: %{(
                        (performanceData.newMemberships.total / Math.max(performanceData.newMemberships.total, 1)) * 100
                      ).toFixed(1)}
                    </span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="w-5 h-5 text-orange-500 mr-2" />
                    <span className="text-sm">
                      Aylık büyüme hedefi: {formatCurrency(performanceData.commissions.total * 1.2)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
