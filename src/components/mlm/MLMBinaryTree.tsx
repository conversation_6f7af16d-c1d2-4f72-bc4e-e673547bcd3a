"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  TrendingUp, 
  Crown, 
  ChevronDown,
  ChevronRight,
  User,
  UserPlus,
  Target
} from 'lucide-react';

interface TreeNode {
  _id: string;
  name: string;
  email: string;
  mlmLevel: string;
  sponsorCode: string;
  totalCommissionEarned: number;
  directReferrals: number;
  totalTeamMembers: number;
  totalSales: number;
  membershipStartDate: string;
  isActive: boolean;
  leftChild?: TreeNode;
  rightChild?: TreeNode;
  depth?: number;
}

interface MLMBinaryTreeProps {
  userId: string;
}

const MLM_LEVEL_COLORS = {
  bronze: "bg-amber-100 text-amber-800",
  silver: "bg-gray-100 text-gray-800", 
  gold: "bg-yellow-100 text-yellow-800",
  diamond: "bg-blue-100 text-blue-800",
  platinum: "bg-purple-100 text-purple-800",
  sapphire: "bg-indigo-100 text-indigo-800"
};

const MLM_LEVEL_NAMES = {
  bronze: "Bronz",
  silver: "Gümüş",
  gold: "Altın", 
  diamond: "Elmas",
  platinum: "Pırlanta",
  sapphire: "Safir"
};

const TreeNodeComponent: React.FC<{ 
  node: TreeNode | null; 
  isRoot?: boolean; 
  onNodeClick?: (node: TreeNode) => void;
  expandedNodes: Set<string>;
  onToggleExpand: (nodeId: string) => void;
}> = ({ 
  node, 
  isRoot = false, 
  onNodeClick, 
  expandedNodes, 
  onToggleExpand 
}) => {
  if (!node) {
    return (
      <div className="flex flex-col items-center">
        <div className="w-32 h-20 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
          <UserPlus className="w-6 h-6 text-gray-400" />
          <span className="text-xs text-gray-500 ml-1">Boş Pozisyon</span>
        </div>
      </div>
    );
  }

  const hasChildren = node.leftChild || node.rightChild;
  const isExpanded = expandedNodes.has(node._id);

  return (
    <div className="flex flex-col items-center">
      {/* Node Card */}
      <div 
        className={`relative w-40 p-3 border-2 rounded-lg cursor-pointer transition-all hover:shadow-lg ${
          isRoot ? 'border-blue-500 bg-blue-50' : 'border-gray-200 bg-white'
        }`}
        onClick={() => onNodeClick?.(node)}
      >
        {/* Expand/Collapse Button */}
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggleExpand(node._id);
            }}
            className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center hover:bg-blue-600"
          >
            {isExpanded ? <ChevronDown className="w-3 h-3" /> : <ChevronRight className="w-3 h-3" />}
          </button>
        )}

        {/* User Info */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-1">
            <User className="w-4 h-4 mr-1" />
            <span className="text-xs font-medium truncate">{node.name}</span>
          </div>
          
          <Badge className={`text-xs ${MLM_LEVEL_COLORS[node.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}`}>
            {MLM_LEVEL_NAMES[node.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
          </Badge>
          
          <div className="mt-2 space-y-1">
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Takım:</span>
              <span className="font-medium">{node.totalTeamMembers}</span>
            </div>
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Satış:</span>
              <span className="font-medium">₺{node.totalSales.toLocaleString()}</span>
            </div>
            <div className="flex items-center justify-center">
              <div className={`w-2 h-2 rounded-full ${node.isActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
              <span className="text-xs ml-1">{node.isActive ? 'Aktif' : 'Pasif'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="mt-4">
          {/* Connection Line */}
          <div className="w-px h-4 bg-gray-300 mx-auto"></div>
          
          {/* Horizontal Line */}
          <div className="flex items-center">
            <div className="w-20 h-px bg-gray-300"></div>
            <div className="w-2 h-2 bg-gray-300 rounded-full"></div>
            <div className="w-20 h-px bg-gray-300"></div>
          </div>
          
          {/* Child Nodes */}
          <div className="flex justify-center space-x-8 mt-4">
            {/* Left Child */}
            <div className="flex flex-col items-center">
              <div className="text-xs text-gray-500 mb-2">Sol</div>
              <TreeNodeComponent 
                node={node.leftChild || null} 
                onNodeClick={onNodeClick}
                expandedNodes={expandedNodes}
                onToggleExpand={onToggleExpand}
              />
            </div>
            
            {/* Right Child */}
            <div className="flex flex-col items-center">
              <div className="text-xs text-gray-500 mb-2">Sağ</div>
              <TreeNodeComponent 
                node={node.rightChild || null} 
                onNodeClick={onNodeClick}
                expandedNodes={expandedNodes}
                onToggleExpand={onToggleExpand}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default function MLMBinaryTree({ userId }: MLMBinaryTreeProps) {
  const [treeData, setTreeData] = useState<TreeNode | null>(null);
  const [selectedNode, setSelectedNode] = useState<TreeNode | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchTreeData();
  }, [userId]);

  const fetchTreeData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/mlm/binary-tree?userId=${userId}`);
      const data = await response.json();
      
      if (data.success) {
        setTreeData(data.tree);
        // Root node'u otomatik genişlet
        if (data.tree) {
          setExpandedNodes(new Set([data.tree._id]));
        }
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Ağaç verisi yüklenirken hata oluştu');
      console.error('Tree data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleNodeClick = (node: TreeNode) => {
    setSelectedNode(node);
  };

  const handleToggleExpand = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const expandAll = () => {
    const getAllNodeIds = (node: TreeNode | null): string[] => {
      if (!node) return [];
      const ids = [node._id];
      if (node.leftChild) ids.push(...getAllNodeIds(node.leftChild));
      if (node.rightChild) ids.push(...getAllNodeIds(node.rightChild));
      return ids;
    };

    if (treeData) {
      setExpandedNodes(new Set(getAllNodeIds(treeData)));
    }
  };

  const collapseAll = () => {
    setExpandedNodes(new Set(treeData ? [treeData._id] : []));
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <span className="ml-2">Ağaç yükleniyor...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-red-600">{error}</p>
          <Button onClick={fetchTreeData} className="mt-4">
            Tekrar Dene
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Users className="w-5 h-5 mr-2" />
                Binary Ağaç Yapısı
              </CardTitle>
              <CardDescription>
                Takımınızın hiyerarşik yapısını görüntüleyin
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button variant="outline" size="sm" onClick={collapseAll}>
                Tümünü Kapat
              </Button>
              <Button variant="outline" size="sm" onClick={expandAll}>
                Tümünü Aç
              </Button>
              <Button size="sm" onClick={fetchTreeData}>
                Yenile
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Tree Visualization */}
        <div className="lg:col-span-3">
          <Card>
            <CardContent className="p-6">
              {treeData ? (
                <div className="overflow-auto">
                  <TreeNodeComponent 
                    node={treeData} 
                    isRoot={true}
                    onNodeClick={handleNodeClick}
                    expandedNodes={expandedNodes}
                    onToggleExpand={handleToggleExpand}
                  />
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                  <p className="text-gray-500">Henüz takım üyeniz bulunmuyor</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Selected Node Details */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Üye Detayları</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedNode ? (
                <div className="space-y-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-2">
                      <User className="w-8 h-8 text-gray-600" />
                    </div>
                    <h3 className="font-semibold">{selectedNode.name}</h3>
                    <p className="text-sm text-gray-600">{selectedNode.email}</p>
                    <Badge className={MLM_LEVEL_COLORS[selectedNode.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}>
                      {MLM_LEVEL_NAMES[selectedNode.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
                    </Badge>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Sponsor Kodu:</span>
                      <span className="text-sm font-medium">{selectedNode.sponsorCode}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Doğrudan Referans:</span>
                      <span className="text-sm font-medium">{selectedNode.directReferrals}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Toplam Takım:</span>
                      <span className="text-sm font-medium">{selectedNode.totalTeamMembers}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Toplam Satış:</span>
                      <span className="text-sm font-medium">₺{selectedNode.totalSales.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Toplam Komisyon:</span>
                      <span className="text-sm font-medium">₺{selectedNode.totalCommissionEarned.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Üyelik Tarihi:</span>
                      <span className="text-sm font-medium">
                        {new Date(selectedNode.membershipStartDate).toLocaleDateString('tr-TR')}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Durum:</span>
                      <Badge variant={selectedNode.isActive ? "default" : "secondary"}>
                        {selectedNode.isActive ? "Aktif" : "Pasif"}
                      </Badge>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Target className="w-12 h-12 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-500">
                    Detayları görmek için bir üye seçin
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
