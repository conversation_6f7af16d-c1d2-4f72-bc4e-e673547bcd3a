"use client";

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Users,
  TrendingUp,
  Gift,
  Wallet,
  Crown,
  Target,
  Copy,
  Share2,
  Award,
  GitBranch
} from 'lucide-react';
import MLMBinaryTree from './MLMBinaryTree';
import MLMPerformanceAnalytics from './MLMPerformanceAnalytics';
import MLMBonusManager from './MLMBonusManager';
import MLMLevelUpgrade from './MLMLevelUpgrade';

interface MLMUser {
  _id: string;
  name: string;
  email: string;
  mlmLevel: string;
  sponsorCode: string;
  sponsor?: {
    _id: string;
    name: string;
    email: string;
    sponsorCode: string;
  };
  membershipStartDate: string;
  isActive: boolean;
  totalCommissionEarned: number;
  monthlyCommissionEarned: number;
  totalSales: number;
  totalTeamSales: number;
  directReferrals: number;
  totalTeamMembers: number;
  giftVoucherBalance: number;
  walletBalance: number;
}

interface Commission {
  _id: string;
  commissionType: string;
  amount: number;
  percentage: number;
  level: number;
  status: string;
  createdAt: string;
  source?: {
    name: string;
    email: string;
  };
}

interface GiftVoucher {
  _id: string;
  code: string;
  amount: number;
  remainingAmount: number;
  isActive: boolean;
  isUsed: boolean;
  expiryDate: string;
  sourceType: string;
  createdAt: string;
}

const MLM_LEVEL_COLORS = {
  bronze: "bg-amber-100 text-amber-800",
  silver: "bg-gray-100 text-gray-800", 
  gold: "bg-yellow-100 text-yellow-800",
  diamond: "bg-blue-100 text-blue-800",
  platinum: "bg-purple-100 text-purple-800",
  sapphire: "bg-indigo-100 text-indigo-800"
};

const MLM_LEVEL_NAMES = {
  bronze: "Bronz",
  silver: "Gümüş",
  gold: "Altın", 
  diamond: "Elmas",
  platinum: "Pırlanta",
  sapphire: "Safir"
};

export default function MLMDashboard() {
  const { user, isLoaded } = useUser();
  const [mlmUser, setMlmUser] = useState<MLMUser | null>(null);
  const [commissions, setCommissions] = useState<Commission[]>([]);
  const [giftVouchers, setGiftVouchers] = useState<GiftVoucher[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isLoaded && user) {
      fetchMLMData();
    }
  }, [isLoaded, user]);

  const fetchMLMData = async () => {
    try {
      setLoading(true);
      
      // MLM kullanıcı bilgilerini getir
      const mlmResponse = await fetch('/api/mlm/register');
      const mlmData = await mlmResponse.json();
      
      if (mlmData.success) {
        setMlmUser(mlmData.user);
        
        // Komisyonları getir
        const commissionsResponse = await fetch(`/api/mlm/calculate-commission?userId=${mlmData.user._id}&type=received`);
        const commissionsData = await commissionsResponse.json();
        
        if (commissionsData.success) {
          setCommissions(commissionsData.data.commissions);
        }
        
        // Hediye çeklerini getir
        const vouchersResponse = await fetch(`/api/mlm/gift-vouchers?ownerId=${mlmData.user._id}`);
        const vouchersData = await vouchersResponse.json();
        
        if (vouchersData.success) {
          setGiftVouchers(vouchersData.data.giftVouchers);
        }
      } else {
        setError(mlmData.message);
      }
    } catch (err) {
      setError('Veriler yüklenirken hata oluştu');
      console.error('MLM data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // Toast notification eklenebilir
  };

  const shareReferralLink = () => {
    const referralLink = `${window.location.origin}/register?sponsor=${mlmUser?.sponsorCode}`;
    copyToClipboard(referralLink);
  };

  if (!isLoaded || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle className="text-red-600">Hata</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{error}</p>
            <Button onClick={fetchMLMData} className="mt-4">
              Tekrar Dene
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!mlmUser) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card className="max-w-md mx-auto">
          <CardHeader>
            <CardTitle>MLM Üyeliği</CardTitle>
            <CardDescription>
              Henüz MLM üyesi değilsiniz. Üye olmak için kayıt olun.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full">
              MLM Üyeliği Başlat
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">MLM Dashboard</h1>
            <p className="text-gray-600">Hoş geldiniz, {mlmUser.name}</p>
          </div>
          <Badge className={MLM_LEVEL_COLORS[mlmUser.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}>
            <Crown className="w-4 h-4 mr-1" />
            {MLM_LEVEL_NAMES[mlmUser.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
          </Badge>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kazanç</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{(mlmUser.totalCommissionEarned || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Bu ay: ₺{(mlmUser.monthlyCommissionEarned || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Takım Üyeleri</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mlmUser.totalTeamMembers || 0}</div>
            <p className="text-xs text-muted-foreground">
              Doğrudan: {mlmUser.directReferrals || 0}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cüzdan Bakiyesi</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{(mlmUser.walletBalance || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Hediye Çeki: ₺{(mlmUser.giftVoucherBalance || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Takım Satışları</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{(mlmUser.totalTeamSales || 0).toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Kişisel: ₺{(mlmUser.totalSales || 0).toLocaleString()}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Referral Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Share2 className="w-5 h-5 mr-2" />
            Referans Linkiniz
          </CardTitle>
          <CardDescription>
            Bu linki paylaşarak yeni üyeler davet edin ve komisyon kazanın
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <code className="flex-1 p-2 bg-gray-100 rounded text-sm">
              {`${window.location.origin}/register?sponsor=${mlmUser.sponsorCode}`}
            </code>
            <Button onClick={shareReferralLink} size="sm">
              <Copy className="w-4 h-4 mr-1" />
              Kopyala
            </Button>
          </div>
          <div className="mt-2">
            <p className="text-sm text-gray-600">
              Sponsor Kodunuz: <strong>{mlmUser.sponsorCode}</strong>
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="commissions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="commissions">Komisyonlar</TabsTrigger>
          <TabsTrigger value="vouchers">Hediye Çekleri</TabsTrigger>
          <TabsTrigger value="bonuses">Bonuslar</TabsTrigger>
          <TabsTrigger value="upgrade">Seviye Yükseltme</TabsTrigger>
          <TabsTrigger value="team">Takımım</TabsTrigger>
          <TabsTrigger value="tree">Binary Ağaç</TabsTrigger>
          <TabsTrigger value="analytics">Performans</TabsTrigger>
        </TabsList>

        <TabsContent value="commissions">
          <Card>
            <CardHeader>
              <CardTitle>Komisyon Geçmişi</CardTitle>
              <CardDescription>
                Aldığınız komisyonların detaylı listesi
              </CardDescription>
            </CardHeader>
            <CardContent>
              {commissions.length === 0 ? (
                <p className="text-center text-gray-500 py-8">
                  Henüz komisyon kazancınız bulunmuyor
                </p>
              ) : (
                <div className="space-y-4">
                  {commissions.slice(0, 10).map((commission) => (
                    <div key={commission._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">₺{commission.amount.toLocaleString()}</p>
                        <p className="text-sm text-gray-600">
                          {commission.commissionType} - %{commission.percentage}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(commission.createdAt).toLocaleDateString('tr-TR')}
                        </p>
                      </div>
                      <Badge variant={commission.status === 'paid' ? 'default' : 'secondary'}>
                        {commission.status === 'paid' ? 'Ödendi' : 'Bekliyor'}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="vouchers">
          <Card>
            <CardHeader>
              <CardTitle>Hediye Çeklerim</CardTitle>
              <CardDescription>
                Aktif ve kullanılmış hediye çekleriniz
              </CardDescription>
            </CardHeader>
            <CardContent>
              {giftVouchers.length === 0 ? (
                <p className="text-center text-gray-500 py-8">
                  Henüz hediye çekiniz bulunmuyor
                </p>
              ) : (
                <div className="space-y-4">
                  {giftVouchers.slice(0, 10).map((voucher) => (
                    <div key={voucher._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <p className="font-medium">{voucher.code}</p>
                        <p className="text-sm text-gray-600">
                          ₺{voucher.remainingAmount.toLocaleString()} / ₺{voucher.amount.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-500">
                          Son kullanma: {new Date(voucher.expiryDate).toLocaleDateString('tr-TR')}
                        </p>
                      </div>
                      <Badge variant={voucher.isUsed ? 'secondary' : 'default'}>
                        {voucher.isUsed ? 'Kullanıldı' : 'Aktif'}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bonuses">
          <MLMBonusManager userId={mlmUser._id} />
        </TabsContent>

        <TabsContent value="upgrade">
          <MLMLevelUpgrade userId={mlmUser._id} />
        </TabsContent>

        <TabsContent value="team">
          <Card>
            <CardHeader>
              <CardTitle>Takım Bilgileri</CardTitle>
              <CardDescription>
                Alt ağacınızdaki üyeler ve performans
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">
                  Takım görünümü yakında eklenecek
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tree">
          <MLMBinaryTree userId={mlmUser._id} />
        </TabsContent>

        <TabsContent value="analytics">
          <MLMPerformanceAnalytics userId={mlmUser._id} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
