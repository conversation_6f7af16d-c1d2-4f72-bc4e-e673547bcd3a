'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Wallet,
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  DollarSign,
  Users
} from 'lucide-react';

interface WalletTransaction {
  _id: string;
  user: {
    name: string;
    email: string;
  };
  type: string;
  amount: number;
  description: string;
  status: string;
  _createdAt: string;
}

interface TopUpRequest {
  _id: string;
  user: {
    name: string;
    email: string;
  };
  amount: number;
  status: string;
  createdAt: string;
  completedAt?: string;
}

export default function WalletManagement() {
  const [transactions, setTransactions] = useState<WalletTransaction[]>([]);
  const [topUpRequests, setTopUpRequests] = useState<TopUpRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalTransactions: 0,
    pendingWithdrawals: 0,
    totalWalletBalance: 0,
    monthlyVolume: 0
  });

  useEffect(() => {
    fetchTransactions();
    fetchTopUpRequests();
    fetchStats();
  }, []);

  const fetchTransactions = async () => {
    try {
      const response = await fetch('/api/admin/wallet/transactions');
      if (response.ok) {
        const data = await response.json();
        setTransactions(data.transactions || []);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    }
  };

  const fetchTopUpRequests = async () => {
    try {
      const response = await fetch('/api/admin/wallet/topup-requests');
      if (response.ok) {
        const data = await response.json();
        setTopUpRequests(data.requests || []);
      }
    } catch (error) {
      console.error('Error fetching top-up requests:', error);
    }
  };

  const fetchStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/wallet/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats || {
          totalTransactions: 0,
          pendingWithdrawals: 0,
          totalWalletBalance: 0,
          monthlyVolume: 0
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveWithdrawal = async (transactionId: string) => {
    if (!confirm('Bu para çekme talebini onaylamak istediğinizden emin misiniz?')) {
      return;
    }

    setActionLoading(transactionId);
    try {
      const response = await fetch('/api/admin/wallet/approve-withdrawal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ transactionId })
      });

      if (response.ok) {
        await fetchTransactions();
        await fetchStats();
      } else {
        const error = await response.json();
        alert(error.message || 'Onaylama başarısız');
      }
    } catch (error) {
      console.error('Error approving withdrawal:', error);
      alert('Onaylama sırasında hata oluştu');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectWithdrawal = async (transactionId: string) => {
    const reason = prompt('Red nedeni (opsiyonel):');
    
    setActionLoading(transactionId);
    try {
      const response = await fetch('/api/admin/wallet/reject-withdrawal', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ transactionId, reason })
      });

      if (response.ok) {
        await fetchTransactions();
        await fetchStats();
      } else {
        const error = await response.json();
        alert(error.message || 'Reddetme başarısız');
      }
    } catch (error) {
      console.error('Error rejecting withdrawal:', error);
      alert('Reddetme sırasında hata oluştu');
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
      case 'withdrawal':
        return <ArrowUpRight className="w-4 h-4 text-red-600" />;
      default:
        return <CreditCard className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'pending':
        return 'Bekliyor';
      case 'failed':
        return 'Başarısız';
      default:
        return status;
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'deposit':
        return 'Para Yükleme';
      case 'withdrawal':
        return 'Para Çekme';
      case 'commission':
        return 'Komisyon';
      case 'purchase':
        return 'Satın Alma';
      case 'refund':
        return 'İade';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cüzdan Yönetimi</h2>
          <p className="text-gray-600">Kullanıcı cüzdan işlemlerini yönetin</p>
        </div>
        <Button onClick={() => { fetchTransactions(); fetchTopUpRequests(); fetchStats(); }} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam İşlem</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTransactions}</div>
            <p className="text-xs text-muted-foreground">
              Tüm cüzdan işlemleri
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Çekimler</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingWithdrawals}</div>
            <p className="text-xs text-muted-foreground">
              Onay bekleyen
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Bakiye</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalWalletBalance)}</div>
            <p className="text-xs text-muted-foreground">
              Tüm kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Hacim</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.monthlyVolume)}</div>
            <p className="text-xs text-muted-foreground">
              Bu ay
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Transactions List */}
      <Card>
        <CardHeader>
          <CardTitle>Son İşlemler</CardTitle>
          <CardDescription>
            Kullanıcı cüzdan işlemlerinin listesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-8">
              <Wallet className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Henüz işlem bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <h3 className="font-medium">{getTypeText(transaction.type)}</h3>
                      <p className="text-sm text-gray-600">{transaction.description}</p>
                      <p className="text-xs text-gray-500">
                        {transaction.user.name} ({transaction.user.email})
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(transaction._createdAt)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <div className="font-medium">
                        {formatCurrency(transaction.amount)}
                      </div>
                      <Badge className={getStatusColor(transaction.status)}>
                        {getStatusText(transaction.status)}
                      </Badge>
                    </div>
                    
                    {transaction.type === 'withdrawal' && transaction.status === 'pending' && (
                      <div className="flex flex-col space-y-1">
                        <Button
                          size="sm"
                          onClick={() => handleApproveWithdrawal(transaction._id)}
                          disabled={actionLoading === transaction._id}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Onayla
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRejectWithdrawal(transaction._id)}
                          disabled={actionLoading === transaction._id}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="w-4 h-4 mr-1" />
                          Reddet
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
