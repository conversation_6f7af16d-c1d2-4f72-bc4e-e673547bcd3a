"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import ImageUpload from '@/components/ui/image-upload';
import {
  Gift,
  Users,
  Trophy,
  Clock,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Plus
} from 'lucide-react';

interface Giveaway {
  _id: string;
  title: string;
  description: string;
  ticketPrice: number;
  totalTickets: number;
  ticketsSold: number;
  status: string;
  startDate: string;
  endDate: string;
  drawDate: string;
  prizes: Array<{
    rank: number;
    title: string;
    description: string;
    value: number;
  }>;
  winningNumbers?: string[];
  winners?: any[];
}

interface GiveawayManagementProps {
  onDrawComplete?: () => void;
}

export default function GiveawayManagement({ onDrawComplete }: GiveawayManagementProps = {}) {
  const [giveaways, setGiveaways] = useState<Giveaway[]>([]);
  const [loading, setLoading] = useState(true);
  const [drawLoading, setDrawLoading] = useState<string | null>(null);
  const [drawResult, setDrawResult] = useState<any>(null);
  const [drawError, setDrawError] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createLoading, setCreateLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    ticketPrice: '',
    maxTickets: '',
    numbersPerCard: '3',
    ticketDigitLength: '3',
    startDate: '',
    endDate: '',
    prizeDescription: ''
  });

  const [images, setImages] = useState<any[]>([]);

  useEffect(() => {
    fetchGiveaways();
  }, []);

  const fetchGiveaways = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/giveaways/all");
      if (!response.ok) throw new Error('Network response was not ok');
      const data = await response.json();
      setGiveaways(data.giveaways || []);
    } catch (error) {
      console.error('Error fetching giveaways:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateGiveaway = async (e: React.FormEvent) => {
    e.preventDefault();
    setCreateLoading(true);

    try {
      const response = await fetch('/api/admin/giveaways', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: formData.name,
          description: formData.description,
          ticketPrice: parseFloat(formData.ticketPrice),
          totalTickets: parseInt(formData.maxTickets),
          numbersPerCard: parseInt(formData.numbersPerCard),
          ticketDigitLength: parseInt(formData.ticketDigitLength),
          startDate: formData.startDate,
          endDate: formData.endDate,
          prizes: [
            {
              name: 'Ana Ödül',
              description: formData.prizeDescription,
              value: parseFloat(formData.ticketPrice) * parseInt(formData.maxTickets) * 0.5
            }
          ],
          images: images.map(img => ({
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: img._id
            }
          })),
          image: images.length > 0 ? {
            _type: 'image',
            asset: {
              _type: 'reference',
              _ref: images[0]._id
            }
          } : undefined
        })
      });

      if (response.ok) {
        setShowCreateForm(false);
        setFormData({
          name: '',
          description: '',
          ticketPrice: '',
          maxTickets: '',
          numbersPerCard: '3',
          ticketDigitLength: '3',
          startDate: '',
          endDate: '',
          prizeDescription: ''
        });
        setImages([]);
        await fetchGiveaways();
      } else {
        const error = await response.json();
        alert(error.message || 'Çekiliş oluşturulamadı');
      }
    } catch (error) {
      console.error('Error creating giveaway:', error);
      alert('Çekiliş oluşturulurken hata oluştu');
    } finally {
      setCreateLoading(false);
    }
  };

  const handleDraw = async (giveaway: Giveaway) => {
    setDrawLoading(giveaway._id);
    setDrawError(null);
    setDrawResult(null);

    try {
      if (giveaway.ticketsSold < giveaway.totalTickets) {
        setDrawError("Tüm biletler satılmadan çekiliş yapılamaz.");
        setDrawLoading(null);
        return;
      }

      const winnerCount = giveaway.prizes?.length || 1;
      const response = await fetch("/api/giveaways/drawWinners", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          giveawayId: giveaway._id,
          winnerCount,
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setDrawResult(data);
        setDrawError(null);

        // Kazanan bilgilerini göster
        const winnersInfo = data.winners?.map((w: any, idx: number) => {
          const displayName = w.displayName || (w.userEmail ? w.userEmail.split('@')[0] : (w.userName || 'Kazanan'));
          return `${idx + 1}. ${displayName} (Bilet: ${w.ticketNumber}) - ${w.prize}`;
        }).join('\n') || 'Kazanan bilgisi bulunamadı';

        const winningNumbers = data.winningNumbers?.join(', ') || 'Kazanan numara yok';

        alert(`${data.giveawayTitle || giveaway.title} - Çekiliş Tamamlandı!\n\nKazanan Numaralar: ${winningNumbers}\n\nKazananlar:\n${winnersInfo}`);

        await fetchGiveaways();
        onDrawComplete?.();
      } else {
        setDrawError(data.message || data.error || "Çekiliş yapılamadı.");
      }
    } catch (error) {
      console.error('Draw error:', error);
      setDrawError("Çekiliş sırasında hata oluştu.");
    } finally {
      setDrawLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'completed':
        return 'Tamamlandı';
      case 'inactive':
        return 'Pasif';
      default:
        return status;
    }
  };

  const canDraw = (giveaway: Giveaway) => {
    return giveaway.status === 'active' && 
           giveaway.ticketsSold >= giveaway.totalTickets &&
           !giveaway.winners?.length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Çekiliş Yönetimi</h2>
          <p className="text-gray-600">Çekilişleri yönetin ve kazananları belirleyin</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={fetchGiveaways} disabled={loading} variant="outline">
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Yeni Çekiliş
          </Button>
        </div>
      </div>

      {/* Create Form */}
      {showCreateForm && (
        <Card>
          <CardHeader>
            <CardTitle>Yeni Çekiliş Oluştur</CardTitle>
            <CardDescription>Yeni bir çekiliş oluşturun</CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCreateGiveaway} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Çekiliş Adı *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="ticketPrice">Bilet Fiyatı (₺) *</Label>
                  <Input
                    id="ticketPrice"
                    type="number"
                    step="0.01"
                    value={formData.ticketPrice}
                    onChange={(e) => setFormData(prev => ({ ...prev, ticketPrice: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="description">Açıklama *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="maxTickets">Maksimum Bilet Sayısı *</Label>
                  <Input
                    id="maxTickets"
                    type="number"
                    value={formData.maxTickets}
                    onChange={(e) => setFormData(prev => ({ ...prev, maxTickets: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="numbersPerCard">Kart Başına Numara</Label>
                  <Input
                    id="numbersPerCard"
                    type="number"
                    value={formData.numbersPerCard}
                    onChange={(e) => setFormData(prev => ({ ...prev, numbersPerCard: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="ticketDigitLength">Numara Uzunluğu</Label>
                  <Input
                    id="ticketDigitLength"
                    type="number"
                    value={formData.ticketDigitLength}
                    onChange={(e) => setFormData(prev => ({ ...prev, ticketDigitLength: e.target.value }))}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Başlangıç Tarihi *</Label>
                  <Input
                    id="startDate"
                    type="datetime-local"
                    value={formData.startDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">Bitiş Tarihi *</Label>
                  <Input
                    id="endDate"
                    type="datetime-local"
                    value={formData.endDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="prizeDescription">Ödül Açıklaması *</Label>
                <Textarea
                  id="prizeDescription"
                  value={formData.prizeDescription}
                  onChange={(e) => setFormData(prev => ({ ...prev, prizeDescription: e.target.value }))}
                  required
                />
              </div>

              {/* Image Upload */}
              <div>
                <ImageUpload
                  label="Çekiliş Resimleri"
                  description="Çekilişinizin fotoğraflarını yükleyin. İlk resim ana resim olarak kullanılacaktır."
                  maxFiles={3}
                  onImagesChange={setImages}
                  initialImages={images}
                  disabled={createLoading}
                />
              </div>

              <div className="flex items-center justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowCreateForm(false)}>
                  İptal
                </Button>
                <Button type="submit" disabled={createLoading}>
                  {createLoading ? 'Oluşturuluyor...' : 'Çekiliş Oluştur'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Error/Success Messages */}
      {drawError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
            <span className="text-red-800">{drawError}</span>
          </div>
        </div>
      )}

      {drawResult && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
            <span className="text-green-800 font-semibold">
              {drawResult.giveawayTitle} - Çekiliş Başarıyla Tamamlandı!
            </span>
          </div>
          <div className="text-green-700 space-y-2">
            <p><strong>Kazanan Numaralar:</strong> {drawResult.winningNumbers?.join(", ")}</p>
            <div>
              <strong>Kazananlar:</strong>
              <div className="mt-1 space-y-1">
                {drawResult.winners?.map((w: any, idx: number) => (
                  <div key={idx} className="bg-white p-2 rounded border">
                    <span className="font-medium">{idx + 1}. {w.displayName || w.userName || 'Kazanan'}</span>
                    <span className="text-sm text-gray-600 ml-2">
                      (Bilet: {w.ticketNumber}) - {w.prize}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Giveaways List */}
      {loading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      ) : giveaways.length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Gift className="w-16 h-16 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500">Henüz çekiliş bulunmuyor</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 gap-6">
          {giveaways.map((giveaway) => (
            <Card key={giveaway._id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{giveaway.title}</CardTitle>
                    <CardDescription>{giveaway.description}</CardDescription>
                  </div>
                  <Badge className={getStatusColor(giveaway.status)}>
                    {getStatusText(giveaway.status)}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                  {/* Bilet Bilgileri */}
                  <div className="flex items-center space-x-2">
                    <Users className="w-4 h-4 text-blue-600" />
                    <div>
                      <p className="text-sm text-gray-600">Bilet Satışı</p>
                      <p className="font-semibold">{giveaway.ticketsSold}/{giveaway.totalTickets}</p>
                    </div>
                  </div>

                  {/* Bilet Fiyatı */}
                  <div className="flex items-center space-x-2">
                    <Gift className="w-4 h-4 text-green-600" />
                    <div>
                      <p className="text-sm text-gray-600">Bilet Fiyatı</p>
                      <p className="font-semibold">{formatCurrency(giveaway.ticketPrice)}</p>
                    </div>
                  </div>

                  {/* Ödül Sayısı */}
                  <div className="flex items-center space-x-2">
                    <Trophy className="w-4 h-4 text-yellow-600" />
                    <div>
                      <p className="text-sm text-gray-600">Ödül Sayısı</p>
                      <p className="font-semibold">{giveaway.prizes?.length || 0}</p>
                    </div>
                  </div>

                  {/* Bitiş Tarihi */}
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-purple-600" />
                    <div>
                      <p className="text-sm text-gray-600">Bitiş Tarihi</p>
                      <p className="font-semibold text-xs">{formatDate(giveaway.endDate)}</p>
                    </div>
                  </div>
                </div>

                {/* Çekiliş Butonu */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-600">
                    {giveaway.ticketsSold < giveaway.totalTickets ? (
                      <span className="text-yellow-600">Tüm biletler satılmadı</span>
                    ) : giveaway.status === 'completed' ? (
                      <span className="text-green-600">Çekiliş tamamlandı</span>
                    ) : (
                      <span className="text-blue-600">Çekiliş yapılabilir</span>
                    )}
                  </div>
                  
                  {canDraw(giveaway) && (
                    <Button
                      onClick={() => handleDraw(giveaway)}
                      disabled={drawLoading === giveaway._id}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      {drawLoading === giveaway._id ? (
                        <>
                          <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                          Çekiliş Yapılıyor...
                        </>
                      ) : (
                        <>
                          <Trophy className="w-4 h-4 mr-2" />
                          Çekilişi Yap
                        </>
                      )}
                    </Button>
                  )}

                  {giveaway.status === 'completed' && (
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Kazananları göster
                        const winnersInfo = giveaway.winners?.map((w: any, idx: number) => {
                          const displayName = w.user?.email ? w.user.email.split('@')[0] : (w.user?.name || 'Kazanan');
                          return `${idx + 1}. ${displayName} (Bilet: ${w.ticketNumber || 'N/A'}) - ${w.prize || 'Ödül'}`;
                        }).join('\n') || 'Kazanan bilgisi bulunamadı';

                        const winningNumbers = giveaway.winningNumbers?.join(', ') || 'Kazanan numara yok';

                        alert(`${giveaway.title} - Çekiliş Sonuçları\n\nKazanan Numaralar: ${winningNumbers}\n\nKazananlar:\n${winnersInfo}`);
                      }}
                    >
                      <Trophy className="w-4 h-4 mr-2" />
                      Kazananları Gör
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
