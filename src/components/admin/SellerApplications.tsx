"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Store,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Eye,
  User,
  Phone,
  Mail,
  Building
} from 'lucide-react';

interface SellerApplication {
  _id: string;
  businessType: string;
  businessName: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  status: string;
  submittedAt: string;
  applicant: {
    name: string;
    email: string;
    clerkId: string;
  };
}

export default function SellerApplications() {
  const [applications, setApplications] = useState<SellerApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalApplications: 0,
    pendingApplications: 0,
    approvedApplications: 0,
    rejectedApplications: 0
  });

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/seller-applications');
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
        
        // Calculate stats
        const totalApplications = data.applications?.length || 0;
        const pendingApplications = data.applications?.filter((a: SellerApplication) => a.status === 'pending').length || 0;
        const approvedApplications = data.applications?.filter((a: SellerApplication) => a.status === 'approved').length || 0;
        const rejectedApplications = data.applications?.filter((a: SellerApplication) => a.status === 'rejected').length || 0;
        
        setStats({
          totalApplications,
          pendingApplications,
          approvedApplications,
          rejectedApplications
        });
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveApplication = async (applicationId: string) => {
    if (!confirm('Bu başvuruyu onaylamak istediğinizden emin misiniz?')) {
      return;
    }

    setActionLoading(applicationId);
    try {
      const response = await fetch('/api/seller-applications/approve', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ applicationId })
      });
      
      if (response.ok) {
        await fetchApplications();
      } else {
        const error = await response.json();
        alert(error.message || 'Başvuru onaylanırken hata oluştu');
      }
    } catch (error) {
      console.error('Error approving application:', error);
      alert('Başvuru onaylanırken hata oluştu');
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectApplication = async (applicationId: string) => {
    const reason = prompt('Red nedeni (opsiyonel):');
    
    setActionLoading(applicationId);
    try {
      const response = await fetch('/api/seller-applications/reject', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ applicationId, reason })
      });
      
      if (response.ok) {
        await fetchApplications();
      } else {
        const error = await response.json();
        alert(error.message || 'Başvuru reddedilirken hata oluştu');
      }
    } catch (error) {
      console.error('Error rejecting application:', error);
      alert('Başvuru reddedilirken hata oluştu');
    } finally {
      setActionLoading(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Bekliyor';
      case 'approved':
        return 'Onaylandı';
      case 'rejected':
        return 'Reddedildi';
      default:
        return status;
    }
  };

  const getBusinessTypeText = (type: string) => {
    switch (type) {
      case 'individual':
        return 'Bireysel';
      case 'company':
        return 'Şirket';
      case 'cooperative':
        return 'Kooperatif';
      default:
        return type;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Satıcı Başvuruları</h2>
          <p className="text-gray-600">Satıcı başvurularını görüntüleyin ve yönetin</p>
        </div>
        <Button onClick={fetchApplications} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Başvuru</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalApplications}</div>
            <p className="text-xs text-muted-foreground">
              Tüm başvurular
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingApplications}</div>
            <p className="text-xs text-muted-foreground">
              İnceleme bekliyor
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Onaylanan</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvedApplications}</div>
            <p className="text-xs text-muted-foreground">
              Onaylanmış başvurular
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reddedilen</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.rejectedApplications}</div>
            <p className="text-xs text-muted-foreground">
              Reddedilmiş başvurular
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Applications List */}
      <Card>
        <CardHeader>
          <CardTitle>Başvuru Listesi</CardTitle>
          <CardDescription>
            Satıcı başvurularının listesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : applications.length === 0 ? (
            <div className="text-center py-8">
              <Store className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Henüz başvuru bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {applications.map((application) => (
                <div key={application._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div>
                        <h3 className="font-medium">{application.businessName}</h3>
                        <p className="text-sm text-gray-600">{getBusinessTypeText(application.businessType)}</p>
                      </div>
                      <Badge className={getStatusColor(application.status)}>
                        {getStatusText(application.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center">
                        <User className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{application.contactPerson}</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{application.contactEmail}</span>
                      </div>
                      <div className="flex items-center">
                        <Phone className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{application.contactPhone}</span>
                      </div>
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1 text-gray-400" />
                        <span>{formatDate(application.submittedAt)}</span>
                      </div>
                    </div>
                    
                    {application.applicant && (
                      <div className="mt-2 text-xs text-gray-500">
                        <span>Başvuran: {application.applicant.name} ({application.applicant.email})</span>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-1" />
                      Detay
                    </Button>
                    
                    {application.status === 'pending' && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleApproveApplication(application._id)}
                          disabled={actionLoading === application._id}
                          className="text-green-600 border-green-600 hover:bg-green-50"
                        >
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Onayla
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRejectApplication(application._id)}
                          disabled={actionLoading === application._id}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <XCircle className="w-4 h-4 mr-1" />
                          Reddet
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
