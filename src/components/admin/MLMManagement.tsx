"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users,
  TrendingUp,
  DollarSign,
  Award,
  RefreshCw,
  UserCheck,
  UserX,
  Crown,
  Target
} from 'lucide-react';

interface MLMUser {
  _id: string;
  name: string;
  email: string;
  clerkId: string;
  mlmLevel: string;
  sponsorCode: string;
  sponsor?: {
    name: string;
    email: string;
  };
  isAdminApproved: boolean;
  isActive: boolean;
  totalCommissionEarned: number;
  monthlyCommissionEarned: number;
  totalSales: number;
  directReferrals: number;
  totalTeamMembers: number;
  walletBalance: number;
  giftVoucherBalance: number;
  membershipStartDate: string;
}

const MLM_LEVEL_COLORS = {
  bronze: "bg-amber-100 text-amber-800",
  silver: "bg-gray-100 text-gray-800", 
  gold: "bg-yellow-100 text-yellow-800",
  diamond: "bg-blue-100 text-blue-800",
  platinum: "bg-purple-100 text-purple-800",
  sapphire: "bg-indigo-100 text-indigo-800"
};

const MLM_LEVEL_NAMES = {
  bronze: "Bronz",
  silver: "Gümüş",
  gold: "Altın", 
  diamond: "Elmas",
  platinum: "Pırlanta",
  sapphire: "Safir"
};

export default function MLMManagement() {
  const [users, setUsers] = useState<MLMUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalUsers: 0,
    approvedUsers: 0,
    totalCommissions: 0,
    monthlyCommissions: 0
  });

  useEffect(() => {
    fetchMLMUsers();
  }, []);

  const fetchMLMUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/mlm/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
        
        // Calculate stats
        const totalUsers = data.users?.length || 0;
        const approvedUsers = data.users?.filter((u: MLMUser) => u.isAdminApproved).length || 0;
        const totalCommissions = data.users?.reduce((sum: number, u: MLMUser) => sum + (u.totalCommissionEarned || 0), 0) || 0;
        const monthlyCommissions = data.users?.reduce((sum: number, u: MLMUser) => sum + (u.monthlyCommissionEarned || 0), 0) || 0;
        
        setStats({
          totalUsers,
          approvedUsers,
          totalCommissions,
          monthlyCommissions
        });
      }
    } catch (error) {
      console.error('Error fetching MLM users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApproveUser = async (userId: string) => {
    setActionLoading(userId);
    try {
      const response = await fetch('/api/admin/mlm/approve-user', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      
      if (response.ok) {
        await fetchMLMUsers();
      }
    } catch (error) {
      console.error('Error approving user:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleRejectUser = async (userId: string) => {
    setActionLoading(userId);
    try {
      const response = await fetch('/api/admin/mlm/reject-user', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId })
      });
      
      if (response.ok) {
        await fetchMLMUsers();
      }
    } catch (error) {
      console.error('Error rejecting user:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">MLM Yönetimi</h2>
          <p className="text-gray-600">MLM kullanıcılarını ve komisyonları yönetin</p>
        </div>
        <Button onClick={fetchMLMUsers} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kullanıcı</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              MLM sistemindeki kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Onaylı Kullanıcı</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvedUsers}</div>
            <p className="text-xs text-muted-foreground">
              Admin onayı almış kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Komisyon</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalCommissions)}</div>
            <p className="text-xs text-muted-foreground">
              Ödenen toplam komisyon
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aylık Komisyon</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.monthlyCommissions)}</div>
            <p className="text-xs text-muted-foreground">
              Bu ay ödenen komisyon
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Users Management */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">Tüm Kullanıcılar</TabsTrigger>
          <TabsTrigger value="pending">Onay Bekleyenler</TabsTrigger>
          <TabsTrigger value="approved">Onaylı Kullanıcılar</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <Card>
            <CardHeader>
              <CardTitle>Tüm MLM Kullanıcıları</CardTitle>
              <CardDescription>
                Sistemdeki tüm MLM kullanıcılarının listesi
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              ) : users.length === 0 ? (
                <p className="text-center text-gray-500 py-8">Henüz MLM kullanıcısı bulunmuyor</p>
              ) : (
                <div className="space-y-4">
                  {users.map((user) => (
                    <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                          <Badge className={MLM_LEVEL_COLORS[user.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}>
                            <Crown className="w-3 h-3 mr-1" />
                            {MLM_LEVEL_NAMES[user.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
                          </Badge>
                          <Badge variant={user.isAdminApproved ? "default" : "secondary"}>
                            {user.isAdminApproved ? "Onaylı" : "Bekliyor"}
                          </Badge>
                        </div>
                        <div className="mt-2 grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Komisyon:</span>
                            <span className="ml-1 font-medium">{formatCurrency(user.totalCommissionEarned || 0)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Takım:</span>
                            <span className="ml-1 font-medium">{user.totalTeamMembers || 0}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Direkt:</span>
                            <span className="ml-1 font-medium">{user.directReferrals || 0}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Üyelik:</span>
                            <span className="ml-1 font-medium">{formatDate(user.membershipStartDate)}</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {!user.isAdminApproved && (
                          <>
                            <Button
                              size="sm"
                              onClick={() => handleApproveUser(user._id)}
                              disabled={actionLoading === user._id}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <UserCheck className="w-4 h-4 mr-1" />
                              Onayla
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleRejectUser(user._id)}
                              disabled={actionLoading === user._id}
                              className="text-red-600 border-red-600 hover:bg-red-50"
                            >
                              <UserX className="w-4 h-4 mr-1" />
                              Reddet
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="pending">
          <Card>
            <CardHeader>
              <CardTitle>Onay Bekleyen Kullanıcılar</CardTitle>
              <CardDescription>
                Admin onayı bekleyen MLM kullanıcıları
              </CardDescription>
            </CardHeader>
            <CardContent>
              {users.filter(u => !u.isAdminApproved).length === 0 ? (
                <p className="text-center text-gray-500 py-8">Onay bekleyen kullanıcı bulunmuyor</p>
              ) : (
                <div className="space-y-4">
                  {users.filter(u => !u.isAdminApproved).map((user) => (
                    <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg bg-yellow-50">
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-xs text-gray-500">
                          Üyelik: {formatDate(user.membershipStartDate)}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          onClick={() => handleApproveUser(user._id)}
                          disabled={actionLoading === user._id}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <UserCheck className="w-4 h-4 mr-1" />
                          Onayla
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRejectUser(user._id)}
                          disabled={actionLoading === user._id}
                          className="text-red-600 border-red-600 hover:bg-red-50"
                        >
                          <UserX className="w-4 h-4 mr-1" />
                          Reddet
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="approved">
          <Card>
            <CardHeader>
              <CardTitle>Onaylı Kullanıcılar</CardTitle>
              <CardDescription>
                Admin onayı almış aktif MLM kullanıcıları
              </CardDescription>
            </CardHeader>
            <CardContent>
              {users.filter(u => u.isAdminApproved).length === 0 ? (
                <p className="text-center text-gray-500 py-8">Onaylı kullanıcı bulunmuyor</p>
              ) : (
                <div className="space-y-4">
                  {users.filter(u => u.isAdminApproved).map((user) => (
                    <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                          <Badge className={MLM_LEVEL_COLORS[user.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}>
                            <Crown className="w-3 h-3 mr-1" />
                            {MLM_LEVEL_NAMES[user.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
                          </Badge>
                        </div>
                        <div className="mt-2 grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Toplam Komisyon:</span>
                            <span className="ml-1 font-medium">{formatCurrency(user.totalCommissionEarned || 0)}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Takım Üyeleri:</span>
                            <span className="ml-1 font-medium">{user.totalTeamMembers || 0}</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Cüzdan:</span>
                            <span className="ml-1 font-medium">{formatCurrency(user.walletBalance || 0)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
