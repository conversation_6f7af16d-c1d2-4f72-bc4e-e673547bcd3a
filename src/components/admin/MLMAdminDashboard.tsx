"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  TrendingUp, 
  DollarSign,
  Award,
  Settings,
  UserPlus,
  Gift,
  BarChart3,
  AlertCircle,
  CheckCircle,
  Crown,
  Search,
  Filter,
  Download,
  Mail
} from 'lucide-react';

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalCommissions: number;
  totalBonuses: number;
  monthlyGrowth: number;
  levelDistribution: Record<string, number>;
}

interface MLMUser {
  _id: string;
  name: string;
  email: string;
  mlmLevel: string;
  sponsorCode: string;
  walletBalance: number;
  totalCommissionEarned: number;
  directReferrals: number;
  totalTeamMembers: number;
  isActive: boolean;
  membershipStartDate: string;
}

const MLM_LEVEL_COLORS = {
  bronze: "bg-amber-100 text-amber-800",
  silver: "bg-gray-100 text-gray-800", 
  gold: "bg-yellow-100 text-yellow-800",
  diamond: "bg-blue-100 text-blue-800",
  platinum: "bg-purple-100 text-purple-800",
  sapphire: "bg-indigo-100 text-indigo-800"
};

const MLM_LEVEL_NAMES = {
  bronze: "Bronz",
  silver: "Gümüş",
  gold: "Altın", 
  diamond: "Elmas",
  platinum: "Pırlanta",
  sapphire: "Safir"
};

export default function MLMAdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [users, setUsers] = useState<MLMUser[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('all');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Manual bonus form
  const [bonusUserId, setBonusUserId] = useState('');
  const [bonusAmount, setBonusAmount] = useState('');
  const [bonusDescription, setBonusDescription] = useState('');

  useEffect(() => {
    fetchAdminData();
  }, []);

  const fetchAdminData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/mlm/stats');
      const data = await response.json();

      if (data.success) {
        setStats(data.data.stats);
        setUsers(data.data.users);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Admin verileri yüklenirken hata oluştu');
      console.error('Admin data fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleGiveBonus = async () => {
    if (!bonusUserId || !bonusAmount || !bonusDescription) {
      setError('Tüm alanları doldurun');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/mlm/bonuses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          targetUserId: bonusUserId,
          bonusType: 'manual_bonus',
          manualAmount: parseFloat(bonusAmount),
          description: bonusDescription
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(`Bonus başarıyla verildi: ₺${bonusAmount}`);
        setBonusUserId('');
        setBonusAmount('');
        setBonusDescription('');
        fetchAdminData(); // Refresh data
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Bonus verilirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const sendMonthlyReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/mlm/bulk-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emailType: 'monthly_report',
          targetLevel: 'all'
        }),
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(`Aylık raporlar gönderildi: ${data.data.successCount} başarılı, ${data.data.failureCount} başarısız`);
      } else {
        setError(data.message);
      }
    } catch (err) {
      setError('Raporlar gönderilirken hata oluştu');
    } finally {
      setLoading(false);
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.sponsorCode.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLevel = selectedLevel === 'all' || user.mlmLevel === selectedLevel;
    return matchesSearch && matchesLevel;
  });

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  if (loading && !stats) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">MLM Admin Dashboard</h1>
            <p className="text-gray-600">MLM sistemini yönetin ve izleyin</p>
          </div>
          <div className="flex space-x-2">
            <Button onClick={sendMonthlyReports} disabled={loading}>
              <Mail className="w-4 h-4 mr-2" />
              Aylık Rapor Gönder
            </Button>
            <Button onClick={fetchAdminData} disabled={loading}>
              Yenile
            </Button>
          </div>
        </div>
      </div>

      {/* Error/Success Messages */}
      {error && (
        <Card className="border-red-200 bg-red-50 mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <p className="text-red-800">{error}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {success && (
        <Card className="border-green-200 bg-green-50 mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center">
              <CheckCircle className="w-5 h-5 text-green-600 mr-2" />
              <p className="text-green-800">{success}</p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalUsers}</div>
              <p className="text-xs text-muted-foreground">
                Aktif: {stats.activeUsers}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Komisyon</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalCommissions)}</div>
              <p className="text-xs text-muted-foreground">
                Bu ay: +%{stats.monthlyGrowth}
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Toplam Bonus</CardTitle>
              <Gift className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(stats.totalBonuses)}</div>
              <p className="text-xs text-muted-foreground">
                Ödenen bonuslar
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Aylık Büyüme</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">%{stats.monthlyGrowth}</div>
              <p className="text-xs text-muted-foreground">
                Yeni üye artışı
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">En Yüksek Seviye</CardTitle>
              <Crown className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.levelDistribution.sapphire}</div>
              <p className="text-xs text-muted-foreground">
                Safir seviyesi
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Tabs */}
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">Kullanıcı Yönetimi</TabsTrigger>
          <TabsTrigger value="bonuses">Bonus Yönetimi</TabsTrigger>
          <TabsTrigger value="analytics">Analitik</TabsTrigger>
          <TabsTrigger value="settings">Ayarlar</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Kullanıcı Yönetimi</CardTitle>
                  <CardDescription>
                    MLM üyelerini görüntüleyin ve yönetin
                  </CardDescription>
                </div>
                <div className="flex space-x-2">
                  <Button size="sm">
                    <Download className="w-4 h-4 mr-2" />
                    Export
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex space-x-4 mb-6">
                <div className="flex-1">
                  <Label htmlFor="search">Arama</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="search"
                      placeholder="İsim, email veya sponsor kodu..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="level">Seviye</Label>
                  <select
                    id="level"
                    value={selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md"
                  >
                    <option value="all">Tüm Seviyeler</option>
                    <option value="bronze">Bronz</option>
                    <option value="silver">Gümüş</option>
                    <option value="gold">Altın</option>
                    <option value="diamond">Elmas</option>
                    <option value="platinum">Pırlanta</option>
                    <option value="sapphire">Safir</option>
                  </select>
                </div>
              </div>

              {/* Users Table */}
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <Users className="w-5 h-5 text-gray-600" />
                      </div>
                      <div>
                        <p className="font-medium">{user.name}</p>
                        <p className="text-sm text-gray-600">{user.email}</p>
                        <p className="text-xs text-gray-500">Kod: {user.sponsorCode}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge className={MLM_LEVEL_COLORS[user.mlmLevel as keyof typeof MLM_LEVEL_COLORS]}>
                        {MLM_LEVEL_NAMES[user.mlmLevel as keyof typeof MLM_LEVEL_NAMES]}
                      </Badge>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(user.walletBalance)}</p>
                        <p className="text-sm text-gray-600">{user.directReferrals} referans</p>
                      </div>
                      <Badge variant={user.isActive ? "default" : "secondary"}>
                        {user.isActive ? "Aktif" : "Pasif"}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bonuses">
          <Card>
            <CardHeader>
              <CardTitle>Manuel Bonus Yönetimi</CardTitle>
              <CardDescription>
                Kullanıcılara manuel bonus verin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="bonusUserId">Kullanıcı ID</Label>
                <Input
                  id="bonusUserId"
                  value={bonusUserId}
                  onChange={(e) => setBonusUserId(e.target.value)}
                  placeholder="Kullanıcı ID'sini girin"
                />
              </div>

              <div>
                <Label htmlFor="bonusAmount">Bonus Tutarı (TL)</Label>
                <Input
                  id="bonusAmount"
                  type="number"
                  value={bonusAmount}
                  onChange={(e) => setBonusAmount(e.target.value)}
                  placeholder="0"
                  min="1"
                />
              </div>

              <div>
                <Label htmlFor="bonusDescription">Açıklama</Label>
                <Input
                  id="bonusDescription"
                  value={bonusDescription}
                  onChange={(e) => setBonusDescription(e.target.value)}
                  placeholder="Bonus açıklaması"
                />
              </div>

              <Button
                onClick={handleGiveBonus}
                disabled={loading || !bonusUserId || !bonusAmount || !bonusDescription}
                className="w-full"
              >
                {loading ? 'Bonus Veriliyor...' : 'Manuel Bonus Ver'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>Seviye Dağılımı</CardTitle>
              <CardDescription>
                MLM seviyelerine göre üye dağılımı
              </CardDescription>
            </CardHeader>
            <CardContent>
              {stats && (
                <div className="space-y-4">
                  {Object.entries(stats.levelDistribution).map(([level, count]) => (
                    <div key={level} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center space-x-3">
                        <Badge className={MLM_LEVEL_COLORS[level as keyof typeof MLM_LEVEL_COLORS]}>
                          {MLM_LEVEL_NAMES[level as keyof typeof MLM_LEVEL_NAMES]}
                        </Badge>
                        <span>{count} üye</span>
                      </div>
                      <div className="text-sm text-gray-600">
                        %{((count / stats.totalUsers) * 100).toFixed(1)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Sistem Ayarları</CardTitle>
              <CardDescription>
                MLM sistem ayarlarını yönetin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded">
                  <div>
                    <h4 className="font-medium">Email Bildirimleri</h4>
                    <p className="text-sm text-gray-600">Otomatik email gönderimini yönet</p>
                  </div>
                  <Button variant="outline">Ayarla</Button>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded">
                  <div>
                    <h4 className="font-medium">Komisyon Oranları</h4>
                    <p className="text-sm text-gray-600">MLM komisyon oranlarını düzenle</p>
                  </div>
                  <Button variant="outline">Düzenle</Button>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded">
                  <div>
                    <h4 className="font-medium">Seviye Şartları</h4>
                    <p className="text-sm text-gray-600">Seviye yükseltme şartlarını ayarla</p>
                  </div>
                  <Button variant="outline">Ayarla</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
