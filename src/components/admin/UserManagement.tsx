"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Users,
  UserCheck,
  UserX,
  Shield,
  RefreshCw,
  Search,
  Eye,
  Edit,
  Ban,
  CheckCircle,
  Crown,
  Store
} from 'lucide-react';

interface User {
  _id: string;
  name: string;
  email: string;
  clerkId: string;
  isAdmin: boolean;
  isAdminApproved: boolean;
  isSeller: boolean;
  walletBalance: number;
  giftVoucherBalance: number;
  mlmLevel: string;
  directReferrals: number;
  totalTeamMembers: number;
  totalCommissionEarned: number;
  joinDate: string;
  lastLoginDate?: string;
}

export default function UserManagement() {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState({
    totalUsers: 0,
    approvedUsers: 0,
    adminUsers: 0,
    sellerUsers: 0
  });

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    // Filter users based on search term
    const filtered = users.filter(user =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredUsers(filtered);
  }, [users, searchTerm]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
        
        // Calculate stats
        const totalUsers = data.users?.length || 0;
        const approvedUsers = data.users?.filter((u: User) => u.isAdminApproved).length || 0;
        const adminUsers = data.users?.filter((u: User) => u.isAdmin).length || 0;
        const sellerUsers = data.users?.filter((u: User) => u.isSeller).length || 0;
        
        setStats({
          totalUsers,
          approvedUsers,
          adminUsers,
          sellerUsers
        });
      }
    } catch (error) {
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleApproval = async (userId: string, isApproved: boolean) => {
    setActionLoading(userId);
    try {
      const response = await fetch('/api/admin/users/toggle-approval', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, isApproved: !isApproved })
      });
      
      if (response.ok) {
        await fetchUsers();
      }
    } catch (error) {
      console.error('Error toggling user approval:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleAdmin = async (userId: string, isAdmin: boolean) => {
    if (!confirm(`Bu kullanıcıyı ${isAdmin ? 'admin olmaktan çıkar' : 'admin yap'}mak istediğinizden emin misiniz?`)) {
      return;
    }

    setActionLoading(userId);
    try {
      const response = await fetch('/api/admin/users/toggle-admin', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, isAdmin: !isAdmin })
      });
      
      if (response.ok) {
        await fetchUsers();
      }
    } catch (error) {
      console.error('Error toggling admin status:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleSeller = async (userId: string, isSeller: boolean) => {
    setActionLoading(userId);
    try {
      const response = await fetch('/api/admin/users/toggle-seller', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, isSeller: !isSeller })
      });
      
      if (response.ok) {
        await fetchUsers();
      }
    } catch (error) {
      console.error('Error toggling seller status:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Kullanıcı Yönetimi</h2>
          <p className="text-gray-600">Kullanıcıları görüntüleyin ve yönetin</p>
        </div>
        <Button onClick={fetchUsers} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kullanıcı</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              Sistemdeki tüm kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Onaylı Kullanıcı</CardTitle>
            <UserCheck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvedUsers}</div>
            <p className="text-xs text-muted-foreground">
              Admin onaylı kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Admin Kullanıcı</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.adminUsers}</div>
            <p className="text-xs text-muted-foreground">
              Admin yetkili kullanıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Satıcı Kullanıcı</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.sellerUsers}</div>
            <p className="text-xs text-muted-foreground">
              Satıcı yetkili kullanıcılar
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Kullanıcı ara (isim veya email)..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <Card>
        <CardHeader>
          <CardTitle>Kullanıcı Listesi</CardTitle>
          <CardDescription>
            Sistemdeki tüm kullanıcıların listesi ({filteredUsers.length} kullanıcı)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : filteredUsers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">
                {searchTerm ? 'Arama kriterine uygun kullanıcı bulunamadı' : 'Henüz kullanıcı bulunmuyor'}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredUsers.map((user) => (
                <div key={user._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div>
                        <h3 className="font-medium">{user.name}</h3>
                        <p className="text-sm text-gray-600">{user.email}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        {user.isAdmin && (
                          <Badge variant="destructive">
                            <Crown className="w-3 h-3 mr-1" />
                            Admin
                          </Badge>
                        )}
                        {user.isSeller && (
                          <Badge variant="secondary">
                            <Store className="w-3 h-3 mr-1" />
                            Satıcı
                          </Badge>
                        )}
                        <Badge variant={user.isAdminApproved ? "default" : "outline"}>
                          {user.isAdminApproved ? "Onaylı" : "Onay Bekliyor"}
                        </Badge>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Cüzdan:</span>
                        <span className="ml-1 font-medium">{formatCurrency(user.walletBalance || 0)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">MLM Seviye:</span>
                        <span className="ml-1 font-medium capitalize">{user.mlmLevel || 'Bronze'}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Referanslar:</span>
                        <span className="ml-1 font-medium">{user.directReferrals || 0}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Katılım:</span>
                        <span className="ml-1 font-medium">{formatDate(user.joinDate)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-1" />
                      Görüntüle
                    </Button>
                    
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleApproval(user._id, user.isAdminApproved)}
                      disabled={actionLoading === user._id}
                      className={user.isAdminApproved ? "text-red-600 border-red-600 hover:bg-red-50" : "text-green-600 border-green-600 hover:bg-green-50"}
                    >
                      {user.isAdminApproved ? (
                        <>
                          <UserX className="w-4 h-4 mr-1" />
                          Onayı Kaldır
                        </>
                      ) : (
                        <>
                          <UserCheck className="w-4 h-4 mr-1" />
                          Onayla
                        </>
                      )}
                    </Button>

                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleSeller(user._id, user.isSeller)}
                      disabled={actionLoading === user._id}
                      className={user.isSeller ? "text-red-600 border-red-600 hover:bg-red-50" : "text-blue-600 border-blue-600 hover:bg-blue-50"}
                    >
                      {user.isSeller ? (
                        <>
                          <Store className="w-4 h-4 mr-1" />
                          Satıcılığı Kaldır
                        </>
                      ) : (
                        <>
                          <Store className="w-4 h-4 mr-1" />
                          Satıcı Yap
                        </>
                      )}
                    </Button>

                    {!user.isAdmin && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleToggleAdmin(user._id, user.isAdmin)}
                        disabled={actionLoading === user._id}
                        className="text-purple-600 border-purple-600 hover:bg-purple-50"
                      >
                        <Crown className="w-4 h-4 mr-1" />
                        Admin Yap
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
