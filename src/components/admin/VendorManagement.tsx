"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Store,
  Users,
  Package,
  DollarSign,
  RefreshCw,
  Eye,
  Edit,
  Ban,
  CheckCircle
} from 'lucide-react';

interface Vendor {
  _id: string;
  name: string;
  email: string;
  isActive: boolean;
  rating: number;
  totalSales: number;
  productCount: number;
  joinDate: string;
  user: {
    name: string;
    email: string;
    clerkId: string;
  };
}

export default function VendorManagement() {
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalVendors: 0,
    activeVendors: 0,
    totalProducts: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchVendors();
  }, []);

  const fetchVendors = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/vendors');
      if (response.ok) {
        const data = await response.json();
        setVendors(data.vendors || []);
        
        // Calculate stats
        const totalVendors = data.vendors?.length || 0;
        const activeVendors = data.vendors?.filter((v: Vendor) => v.isActive).length || 0;
        const totalProducts = data.vendors?.reduce((sum: number, v: Vendor) => sum + (v.productCount || 0), 0) || 0;
        const totalRevenue = data.vendors?.reduce((sum: number, v: Vendor) => sum + (v.totalSales || 0), 0) || 0;
        
        setStats({
          totalVendors,
          activeVendors,
          totalProducts,
          totalRevenue
        });
      }
    } catch (error) {
      console.error('Error fetching vendors:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleVendorStatus = async (vendorId: string, isActive: boolean) => {
    setActionLoading(vendorId);
    try {
      const response = await fetch('/api/admin/vendors/toggle-status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ vendorId, isActive: !isActive })
      });
      
      if (response.ok) {
        await fetchVendors();
      }
    } catch (error) {
      console.error('Error toggling vendor status:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Satıcı Yönetimi</h2>
          <p className="text-gray-600">Satıcıları görüntüleyin ve yönetin</p>
        </div>
        <Button onClick={fetchVendors} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Satıcı</CardTitle>
            <Store className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalVendors}</div>
            <p className="text-xs text-muted-foreground">
              Sistemdeki tüm satıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Satıcı</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeVendors}</div>
            <p className="text-xs text-muted-foreground">
              Aktif satıcılar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Ürün</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalProducts}</div>
            <p className="text-xs text-muted-foreground">
              Satıcı ürünleri
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Satıcı satışları
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Vendors List */}
      <Card>
        <CardHeader>
          <CardTitle>Satıcı Listesi</CardTitle>
          <CardDescription>
            Sistemdeki tüm satıcıların listesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : vendors.length === 0 ? (
            <div className="text-center py-8">
              <Store className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Henüz satıcı bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {vendors.map((vendor) => (
                <div key={vendor._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div>
                        <h3 className="font-medium">{vendor.name}</h3>
                        <p className="text-sm text-gray-600">{vendor.email}</p>
                        {vendor.user && (
                          <p className="text-xs text-gray-500">
                            Kullanıcı: {vendor.user.name} ({vendor.user.email})
                          </p>
                        )}
                      </div>
                      <Badge variant={vendor.isActive ? "default" : "secondary"}>
                        {vendor.isActive ? "Aktif" : "Pasif"}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Ürün Sayısı:</span>
                        <span className="ml-1 font-medium">{vendor.productCount || 0}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Toplam Satış:</span>
                        <span className="ml-1 font-medium">{formatCurrency(vendor.totalSales || 0)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Değerlendirme:</span>
                        <span className="ml-1 font-medium">{vendor.rating || 0}/5</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Katılım:</span>
                        <span className="ml-1 font-medium">{formatDate(vendor.joinDate)}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-1" />
                      Görüntüle
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="w-4 w-4 mr-1" />
                      Düzenle
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleToggleVendorStatus(vendor._id, vendor.isActive)}
                      disabled={actionLoading === vendor._id}
                      className={vendor.isActive ? "text-red-600 border-red-600 hover:bg-red-50" : "text-green-600 border-green-600 hover:bg-green-50"}
                    >
                      {vendor.isActive ? (
                        <>
                          <Ban className="w-4 h-4 mr-1" />
                          Pasifleştir
                        </>
                      ) : (
                        <>
                          <CheckCircle className="w-4 h-4 mr-1" />
                          Aktifleştir
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
