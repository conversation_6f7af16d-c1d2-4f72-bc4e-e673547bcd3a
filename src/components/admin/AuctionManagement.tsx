"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Gavel,
  Users,
  DollarSign,
  Clock,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus
} from 'lucide-react';

interface Auction {
  _id: string;
  id: string;
  name: string;
  description: string;
  startingBid: number;
  currentBid: number;
  startTime: string;
  endTime: string;
  status: string;
  seller: {
    name: string;
    email: string;
  };
  bids: any[];
  viewCount: number;
  image?: string;
}

export default function AuctionManagement() {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalAuctions: 0,
    activeAuctions: 0,
    totalBids: 0,
    totalValue: 0
  });

  useEffect(() => {
    fetchAuctions();
  }, []);

  const fetchAuctions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/auctions');
      if (response.ok) {
        const data = await response.json();
        setAuctions(data.auctions || []);
        
        // Calculate stats
        const totalAuctions = data.auctions?.length || 0;
        const activeAuctions = data.auctions?.filter((a: Auction) => a.status === 'active').length || 0;
        const totalBids = data.auctions?.reduce((sum: number, a: Auction) => sum + (a.bids?.length || 0), 0) || 0;
        const totalValue = data.auctions?.reduce((sum: number, a: Auction) => sum + (a.currentBid || a.startingBid || 0), 0) || 0;
        
        setStats({
          totalAuctions,
          activeAuctions,
          totalBids,
          totalValue
        });
      }
    } catch (error) {
      console.error('Error fetching auctions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAuction = async (auctionId: string) => {
    if (!confirm('Bu açık artırmayı silmek istediğinizden emin misiniz?')) {
      return;
    }

    setActionLoading(auctionId);
    try {
      const response = await fetch(`/api/auctions/${auctionId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        await fetchAuctions();
      }
    } catch (error) {
      console.error('Error deleting auction:', error);
    } finally {
      setActionLoading(null);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Aktif';
      case 'completed':
        return 'Tamamlandı';
      case 'cancelled':
        return 'İptal Edildi';
      case 'pending':
        return 'Bekliyor';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Açık Artırma Yönetimi</h2>
          <p className="text-gray-600">Açık artırmaları görüntüleyin ve yönetin</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={fetchAuctions} disabled={loading} variant="outline">
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Yenile
          </Button>
          <Button onClick={() => window.open('/auctions/create', '_blank')}>
            <Plus className="w-4 h-4 mr-2" />
            Yeni Açık Artırma
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Açık Artırma</CardTitle>
            <Gavel className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAuctions}</div>
            <p className="text-xs text-muted-foreground">
              Sistemdeki tüm açık artırmalar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Aktif Açık Artırma</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeAuctions}</div>
            <p className="text-xs text-muted-foreground">
              Şu anda devam eden
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Teklif</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBids}</div>
            <p className="text-xs text-muted-foreground">
              Verilen teklif sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Değer</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              Mevcut tekliflerin toplamı
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Auctions List */}
      <Card>
        <CardHeader>
          <CardTitle>Açık Artırmalar</CardTitle>
          <CardDescription>
            Sistemdeki tüm açık artırmaların listesi
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : auctions.length === 0 ? (
            <div className="text-center py-8">
              <Gavel className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Henüz açık artırma bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {auctions.map((auction) => (
                <div key={auction._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div>
                        <h3 className="font-medium">{auction.name}</h3>
                        <p className="text-sm text-gray-600">{auction.description}</p>
                      </div>
                      <Badge className={getStatusColor(auction.status)}>
                        {getStatusText(auction.status)}
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Başlangıç:</span>
                        <span className="ml-1 font-medium">{formatCurrency(auction.startingBid)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Mevcut:</span>
                        <span className="ml-1 font-medium">{formatCurrency(auction.currentBid || auction.startingBid)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Teklif:</span>
                        <span className="ml-1 font-medium">{auction.bids?.length || 0}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Görüntülenme:</span>
                        <span className="ml-1 font-medium">{auction.viewCount || 0}</span>
                      </div>
                    </div>
                    
                    <div className="mt-2 text-xs text-gray-500">
                      <span>Başlangıç: {formatDate(auction.startTime)}</span>
                      <span className="mx-2">•</span>
                      <span>Bitiş: {formatDate(auction.endTime)}</span>
                      {auction.seller && (
                        <>
                          <span className="mx-2">•</span>
                          <span>Satıcı: {auction.seller.name}</span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="w-4 h-4 mr-1" />
                      Görüntüle
                    </Button>
                    <Button size="sm" variant="outline">
                      <Edit className="w-4 h-4 mr-1" />
                      Düzenle
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleDeleteAuction(auction._id)}
                      disabled={actionLoading === auction._id}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="w-4 h-4 mr-1" />
                      Sil
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
