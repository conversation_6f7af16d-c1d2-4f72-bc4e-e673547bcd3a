'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Activity, 
  Clock, 
  Database, 
  Zap, 
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface PerformanceMetrics {
  pageLoadTime: number;
  apiResponseTime: number;
  cacheHitRate: number;
  totalRequests: number;
  errorRate: number;
  memoryUsage: number;
}

interface CacheStats {
  totalItems: number;
  expiredItems: number;
  size: number;
}

export default function PerformanceMonitor() {
  // Disable performance monitor in production
  if (process.env.NODE_ENV === 'production') {
    return null;
  }

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    pageLoadTime: 0,
    apiResponseTime: 0,
    cacheHitRate: 0,
    totalRequests: 0,
    errorRate: 0,
    memoryUsage: 0
  });

  const [cacheStats, setCacheStats] = useState<CacheStats>({
    totalItems: 0,
    expiredItems: 0,
    size: 0
  });

  const [isVisible, setIsVisible] = useState(false);

  // Measure page load time
  useEffect(() => {
    const measurePageLoad = () => {
      if (typeof window !== 'undefined' && window.performance) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        
        setMetrics(prev => ({
          ...prev,
          pageLoadTime: loadTime
        }));
      }
    };

    // Measure after page is fully loaded
    if (document.readyState === 'complete') {
      measurePageLoad();
    } else {
      window.addEventListener('load', measurePageLoad);
      return () => window.removeEventListener('load', measurePageLoad);
    }
  }, []);

  // Fetch cache statistics
  const fetchCacheStats = async () => {
    try {
      const response = await fetch('/api/cache?action=stats');
      const result = await response.json();

      if (result.success && result.data) {
        setCacheStats(result.data);
      } else {
        // Fallback to basic stats if main API fails
        setCacheStats({
          totalItems: 0,
          expiredItems: 0,
          size: 0
        });
      }
    } catch (error) {
      console.error('Failed to fetch cache stats:', error);
      // Set default values on error
      setCacheStats({
        totalItems: 0,
        expiredItems: 0,
        size: 0
      });
    }
  };

  // Clear cache
  const clearCache = async (pattern?: string) => {
    try {
      const url = pattern ? `/api/cache?action=clear&pattern=${pattern}` : '/api/cache?action=clear';
      const response = await fetch(url);
      const result = await response.json();
      
      if (result.success) {
        fetchCacheStats(); // Refresh stats
        console.log(result.message);
      }
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  };

  // Measure memory usage (if available)
  useEffect(() => {
    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
        }));
      }
    };

    measureMemory();
    const interval = setInterval(measureMemory, 5000); // Update every 5 seconds
    
    return () => clearInterval(interval);
  }, []);

  // Fetch cache stats on mount and periodically (only when visible)
  useEffect(() => {
    if (isVisible) {
      fetchCacheStats();
      const interval = setInterval(fetchCacheStats, 30000); // Update every 30 seconds when visible

      return () => clearInterval(interval);
    }
  }, [isVisible]);

  const getPerformanceColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-600';
    if (value <= thresholds.warning) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatTime = (ms: number) => {
    if (ms < 1000) return `${Math.round(ms)}ms`;
    return `${(ms / 1000).toFixed(2)}s`;
  };

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes}B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)}KB`;
    return `${(bytes / 1024 / 1024).toFixed(1)}MB`;
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          size="sm"
          variant="outline"
          className="bg-background/80 backdrop-blur"
        >
          <Activity className="h-4 w-4 mr-2" />
          Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-80">
      <Card className="bg-background/95 backdrop-blur border-2">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center">
              <Activity className="h-4 w-4 mr-2" />
              Performance Monitor
            </CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              size="sm"
              variant="ghost"
              className="h-6 w-6 p-0"
            >
              ×
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Page Performance */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-muted-foreground">Page Performance</h4>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center justify-between">
                <span className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Load Time
                </span>
                <span className={getPerformanceColor(metrics.pageLoadTime, { good: 1000, warning: 3000 })}>
                  {formatTime(metrics.pageLoadTime)}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="flex items-center">
                  <Zap className="h-3 w-3 mr-1" />
                  Memory
                </span>
                <span className={getPerformanceColor(metrics.memoryUsage, { good: 50, warning: 100 })}>
                  {formatBytes(metrics.memoryUsage * 1024 * 1024)}
                </span>
              </div>
            </div>
          </div>

          {/* Cache Statistics */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="text-xs font-medium text-muted-foreground">Cache Statistics</h4>
              <Button
                onClick={fetchCacheStats}
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0"
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center justify-between">
                <span className="flex items-center">
                  <Database className="h-3 w-3 mr-1" />
                  Items
                </span>
                <Badge variant="secondary" className="text-xs">
                  {cacheStats.totalItems}
                </Badge>
              </div>
              
              <div className="flex items-center justify-between">
                <span>Hit Rate</span>
                <span className="text-green-600">
                  {cacheStats.totalItems > 0 ? 
                    Math.round((cacheStats.totalItems / (cacheStats.totalItems + cacheStats.expiredItems)) * 100) : 0
                  }%
                </span>
              </div>
            </div>
          </div>

          {/* Cache Actions */}
          <div className="space-y-2">
            <h4 className="text-xs font-medium text-muted-foreground">Cache Actions</h4>
            <div className="grid grid-cols-2 gap-1">
              <Button
                onClick={() => clearCache('products')}
                size="sm"
                variant="outline"
                className="text-xs h-6"
              >
                Clear Products
              </Button>
              <Button
                onClick={() => clearCache('categories')}
                size="sm"
                variant="outline"
                className="text-xs h-6"
              >
                Clear Categories
              </Button>
              <Button
                onClick={() => clearCache()}
                size="sm"
                variant="destructive"
                className="text-xs h-6 col-span-2"
              >
                Clear All Cache
              </Button>
            </div>
          </div>

          {/* Performance Tips */}
          <div className="text-xs text-muted-foreground">
            <div className="flex items-center space-x-1">
              {metrics.pageLoadTime <= 1000 ? (
                <TrendingUp className="h-3 w-3 text-green-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-red-600" />
              )}
              <span>
                {metrics.pageLoadTime <= 1000 ? 'Excellent' : 
                 metrics.pageLoadTime <= 3000 ? 'Good' : 'Needs Improvement'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
