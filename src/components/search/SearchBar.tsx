'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Search, X, Clock, TrendingUp, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSearchStore } from '@/store/search-store';
import { SanityProduct } from '@/types';
import { useSearchDebounce } from '@/hooks/useDebounce';

interface SearchBarProps {
  className?: string;
}

export default function SearchBar({ className }: SearchBarProps) {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);
  const [localQuery, setLocalQuery] = useState('');
  const [suggestions, setSuggestions] = useState<SanityProduct[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);

  const {
    searchQuery,
    recentSearches,
    setSearchQuery,
    addRecentSearch,
    clearRecentSearches
  } = useSearchStore();

  // Use advanced debounce hook for search
  const { debouncedSearchTerm, isSearching, cancelSearch } = useSearchDebounce(localQuery, 300);

  // Fetch search suggestions from Sanity API
  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!debouncedSearchTerm.trim()) {
        setSuggestions([]);
        return;
      }

      try {
        const response = await fetch(`/api/products?search=${encodeURIComponent(debouncedSearchTerm)}&limit=5`);
        const result = await response.json();

        if (result.success && result.data) {
          setSuggestions(result.data);
        } else {
          setSuggestions([]);
        }
      } catch (error) {
        console.error('Search suggestions error:', error);
        setSuggestions([]);
      }
    };

    if (debouncedSearchTerm) {
      fetchSuggestions();
    }
  }, [debouncedSearchTerm]);

  // Popular searches based on actual categories
  const popularSearches = ['iPhone', 'Tesla', 'Electronics', 'Clothing', 'Home Garden'];

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (query: string) => {
    if (query.trim()) {
      setSearchQuery(query);
      addRecentSearch(query);
      router.push(`/products?search=${encodeURIComponent(query)}`);
      setIsOpen(false);
      setLocalQuery('');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSearch(localQuery);
  };

  const handleSuggestionClick = (productName: string) => {
    handleSearch(productName);
  };

  const handleRecentSearchClick = (query: string) => {
    setLocalQuery(query);
    handleSearch(query);
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <form onSubmit={handleSubmit} className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search products, brands, categories..."
          className="pl-10 pr-4"
          value={localQuery}
          onChange={(e) => setLocalQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
        />
        {localQuery && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={() => {
              setLocalQuery('');
              cancelSearch();
              setSuggestions([]);
            }}
          >
            <X className="h-3 w-3" />
          </Button>
        )}

        {/* Search loading indicator */}
        {isSearching && (
          <div className="absolute right-8 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-3 w-3 animate-spin text-muted-foreground" />
          </div>
        )}
      </form>

      {/* Search Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-background border rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
          {/* Product Suggestions */}
          {(suggestions.length > 0 || isSearching) && (
            <div className="p-3 border-b">
              <h4 className="text-sm font-medium text-muted-foreground mb-2 flex items-center">
                Products
                {isSearching && (
                  <Loader2 className="h-3 w-3 animate-spin ml-2" />
                )}
              </h4>
              <div className="space-y-1">
                {isSearching && suggestions.length === 0 ? (
                  <div className="p-2 text-center text-sm text-muted-foreground flex items-center justify-center">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Searching...
                  </div>
                ) : (
                  suggestions.map((product) => (
                    <button
                      key={product._id}
                      className="w-full text-left p-2 hover:bg-muted rounded-md flex items-center space-x-3 transition-colors"
                      onClick={() => handleSuggestionClick(product.name)}
                    >
                      <div className="w-8 h-8 bg-muted rounded overflow-hidden flex-shrink-0">
                        <img
                          src={product.imageUrl || '/placeholder-product.jpg'}
                          alt={product.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{product.name}</p>
                        <p className="text-xs text-muted-foreground">{product.category}</p>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        ${product.price}
                      </Badge>
                    </button>
                  ))
                )}
              </div>
            </div>
          )}

          {/* Recent Searches */}
          {recentSearches.length > 0 && (
            <div className="p-3 border-b">
              <div className="flex items-center justify-between mb-2">
                <h4 className="text-sm font-medium text-muted-foreground flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  Recent Searches
                </h4>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs h-auto p-1"
                  onClick={clearRecentSearches}
                >
                  Clear
                </Button>
              </div>
              <div className="space-y-1">
                {recentSearches.map((query, index) => (
                  <button
                    key={index}
                    className="w-full text-left p-2 hover:bg-muted rounded-md text-sm"
                    onClick={() => handleRecentSearchClick(query)}
                  >
                    {query}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Popular Searches */}
          {!localQuery && (
            <div className="p-3">
              <h4 className="text-sm font-medium text-muted-foreground mb-2 flex items-center">
                <TrendingUp className="h-3 w-3 mr-1" />
                Popular Searches
              </h4>
              <div className="flex flex-wrap gap-1">
                {popularSearches.map((query) => (
                  <Badge
                    key={query}
                    variant="secondary"
                    className="cursor-pointer hover:bg-primary hover:text-primary-foreground text-xs"
                    onClick={() => handleSearch(query)}
                  >
                    {query}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* No Results */}
          {localQuery && suggestions.length === 0 && (
            <div className="p-4 text-center text-muted-foreground">
              <p className="text-sm">No products found for "{localQuery}"</p>
              <Button
                variant="link"
                size="sm"
                className="text-xs"
                onClick={() => handleSearch(localQuery)}
              >
                Search anyway
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
