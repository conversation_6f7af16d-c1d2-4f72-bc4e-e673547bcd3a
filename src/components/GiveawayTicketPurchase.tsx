'use client';

import React, { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Gift, User, CreditCard, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface GiveawayTicketPurchaseProps {
  giveawayId: string;
  ticketPrice?: number;
  maxTicketsPerUser?: number;
}

export default function GiveawayTicketPurchase({ 
  giveawayId, 
  ticketPrice = 10,
  maxTicketsPerUser = 10
}: GiveawayTicketPurchaseProps) {
  const { user, isLoaded } = useUser();
  const [numberOfTickets, setNumberOfTickets] = useState(1);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [purchasedTickets, setPurchasedTickets] = useState<string[][]>([]);

  const totalPrice = numberOfTickets * ticketPrice;

  const handlePurchase = async () => {
    if (!user) {
      setError('Bilet satın almak için giriş yapmalısınız.');
      return;
    }

    if (numberOfTickets > maxTicketsPerUser) {
      setError(`Maksimum ${maxTicketsPerUser} adet bilet alabilirsiniz.`);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/giveaways/buyTickets', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          giveawayId,
          numberOfTickets
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPurchasedTickets(data.purchasedTickets || []);
        setSuccess(true);
        setError(null);
      } else {
        setError(data.error || 'Bilet satın alınamadı.');
        setSuccess(false);
      }
    } catch (err) {
      setError('Bilet satın alınırken bir hata oluştu.');
      setSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (!isLoaded) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Yükleniyor...</span>
        </CardContent>
      </Card>
    );
  }

  if (!user) {
    return (
      <Card>
        <CardContent className="text-center p-8">
          <User size={48} className="mx-auto text-gray-400 mb-4" />
          <CardTitle className="text-lg text-gray-700 mb-2">Giriş Yapın</CardTitle>
          <CardDescription className="mb-4">
            Çekilişe katılmak için giriş yapmalısınız.
          </CardDescription>
          <Button className="w-full">
            Giriş Yap
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (success) {
    return (
      <Card>
        <CardContent className="text-center p-8">
          <Check size={48} className="mx-auto text-green-600 mb-4" />
          <CardTitle className="text-lg text-green-800 mb-2">
            Biletleriniz Başarıyla Satın Alındı!
          </CardTitle>
          <CardDescription className="text-green-700 mb-4">
            {numberOfTickets} adet kart satın aldınız.
          </CardDescription>
          
          {purchasedTickets.length > 0 && (
            <div className="bg-green-50 p-4 rounded-lg border border-green-200 mb-6">
              <h4 className="font-semibold text-gray-800 mb-3">Kart(lar)ınızdaki Numara(lar):</h4>
              <div className="flex flex-wrap gap-2 justify-center">
                {purchasedTickets.map((card, idx) => (
                  <span key={idx} className="bg-green-500 text-white px-3 py-1 rounded-full font-mono font-bold text-sm">
                    {card.join(', ')}
                  </span>
                ))}
              </div>
            </div>
          )}

          <div className="space-y-3">
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
              className="w-full"
            >
              Ana Sayfa
            </Button>
            <Button 
              onClick={() => window.location.reload()}
              className="w-full"
            >
              Tekrar Bilet Al
            </Button>
          </div>

          <p className="text-sm text-green-600 mt-4">
            Çekiliş sonuçları açıklandığında bilgilendirileceksiniz.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent className="p-6 space-y-6">
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertCircle className="w-5 h-5 text-red-600 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* Ticket Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Kaç kart almak istiyorsunuz?
          </label>
          <Input
            type="number"
            min={1}
            max={maxTicketsPerUser}
            value={numberOfTickets}
            onChange={(e) => setNumberOfTickets(Number(e.target.value))}
            className="text-center"
          />
        </div>

        {/* Price Summary */}
        <div className="bg-gray-50 p-4 rounded-lg space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Kart Fiyatı:</span>
            <span className="font-semibold">{formatCurrency(ticketPrice)}</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Kart Sayısı:</span>
            <span className="font-semibold">{numberOfTickets}</span>
          </div>
          <hr className="my-2" />
          <div className="flex items-center justify-between text-lg font-bold">
            <span>Toplam:</span>
            <span className="text-blue-600">{formatCurrency(totalPrice)}</span>
          </div>
        </div>

        {/* Purchase Button */}
        <Button
          onClick={handlePurchase}
          disabled={loading}
          className="w-full py-4 text-lg font-bold"
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              İşleniyor...
            </>
          ) : (
            <>
              <Gift className="w-5 h-5 mr-2" />
              Bilet Al - {formatCurrency(totalPrice)}
            </>
          )}
        </Button>

        {/* Info */}
        <div className="text-sm text-gray-600 bg-blue-50 p-4 rounded-lg">
          <h4 className="font-semibold mb-2">💡 Bilgilendirme:</h4>
          <ul className="space-y-1">
            <li>• Her kart, adminin belirlediği kadar numara içerir</li>
            <li>• Satın alınan kartlar ve üzerindeki numaralar size özel ve tektir</li>
            <li>• Maksimum {maxTicketsPerUser} adet kart alabilirsiniz</li>
            <li>• Cüzdan bakiyenizden ödeme yapılır</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
