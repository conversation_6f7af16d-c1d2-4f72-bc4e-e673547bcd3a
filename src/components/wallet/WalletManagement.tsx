'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Wallet,
  Plus,
  Minus,
  CreditCard,
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw
} from 'lucide-react';

interface Transaction {
  _id: string;
  type: 'deposit' | 'withdrawal' | 'commission' | 'purchase' | 'refund';
  amount: number;
  description: string;
  status: 'pending' | 'completed' | 'failed';
  createdAt: string;
}

interface WalletData {
  balance: number;
  giftVoucherBalance: number;
  totalEarned: number;
  totalSpent: number;
  pendingAmount: number;
}

export default function WalletManagement() {
  const [walletData, setWalletData] = useState<WalletData>({
    balance: 0,
    giftVoucherBalance: 0,
    totalEarned: 0,
    totalSpent: 0,
    pendingAmount: 0
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [showAddFunds, setShowAddFunds] = useState(false);
  const [showWithdraw, setShowWithdraw] = useState(false);
  const [amount, setAmount] = useState('');

  useEffect(() => {
    fetchWalletData();
    fetchTransactions();
  }, []);

  const fetchWalletData = async () => {
    try {
      const response = await fetch('/api/wallet');
      if (response.ok) {
        const data = await response.json();
        setWalletData(data.wallet);
      }
    } catch (error) {
      console.error('Error fetching wallet data:', error);
    }
  };

  const fetchTransactions = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/wallet/transactions');
      if (response.ok) {
        const data = await response.json();
        setTransactions(data.transactions || []);
      }
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFunds = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Lütfen geçerli bir miktar girin');
      return;
    }

    if (parseFloat(amount) < 10) {
      alert('Minimum yükleme tutarı 10 TL\'dir');
      return;
    }

    setActionLoading(true);
    try {
      const response = await fetch('/api/wallet/add-funds', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: parseFloat(amount) })
      });

      if (response.ok) {
        const data = await response.json();

        if (data.sessionUrl) {
          // Redirect to Stripe checkout
          window.location.href = data.sessionUrl;
        } else {
          alert('Ödeme sayfası oluşturulamadı');
        }
      } else {
        const error = await response.json();
        alert(error.error || 'Para yükleme başarısız');
      }
    } catch (error) {
      console.error('Error adding funds:', error);
      alert('Para yükleme sırasında hata oluştu');
    } finally {
      setActionLoading(false);
    }
  };

  const handleAddFundsDemo = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Lütfen geçerli bir miktar girin');
      return;
    }

    if (parseFloat(amount) < 10) {
      alert('Minimum yükleme tutarı 10 TL\'dir');
      return;
    }

    setActionLoading(true);
    try {
      const response = await fetch('/api/wallet/add-funds-simple', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: parseFloat(amount) })
      });

      if (response.ok) {
        setAmount('');
        setShowAddFunds(false);
        await fetchWalletData();
        await fetchTransactions();
        alert('Para başarıyla yüklendi! (Demo mode)');
      } else {
        const error = await response.json();
        alert(error.error || 'Para yükleme başarısız');
      }
    } catch (error) {
      console.error('Error adding funds:', error);
      alert('Para yükleme sırasında hata oluştu');
    } finally {
      setActionLoading(false);
    }
  };

  const handleWithdraw = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      alert('Lütfen geçerli bir miktar girin');
      return;
    }

    if (parseFloat(amount) > walletData.balance) {
      alert('Yetersiz bakiye');
      return;
    }

    setActionLoading(true);
    try {
      const response = await fetch('/api/wallet/withdraw', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ amount: parseFloat(amount) })
      });

      if (response.ok) {
        setAmount('');
        setShowWithdraw(false);
        await fetchWalletData();
        await fetchTransactions();
      } else {
        const error = await response.json();
        alert(error.message || 'Para çekme başarısız');
      }
    } catch (error) {
      console.error('Error withdrawing funds:', error);
      alert('Para çekme sırasında hata oluştu');
    } finally {
      setActionLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'deposit':
        return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
      case 'withdrawal':
        return <ArrowUpRight className="w-4 h-4 text-red-600" />;
      case 'commission':
        return <Plus className="w-4 h-4 text-blue-600" />;
      case 'purchase':
        return <Minus className="w-4 h-4 text-orange-600" />;
      case 'refund':
        return <Plus className="w-4 h-4 text-green-600" />;
      default:
        return <CreditCard className="w-4 h-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type: string) => {
    switch (type) {
      case 'deposit':
      case 'commission':
      case 'refund':
        return 'text-green-600';
      case 'withdrawal':
      case 'purchase':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: string) => {
    switch (type) {
      case 'deposit':
        return 'Para Yükleme';
      case 'withdrawal':
        return 'Para Çekme';
      case 'commission':
        return 'Komisyon';
      case 'purchase':
        return 'Satın Alma';
      case 'refund':
        return 'İade';
      default:
        return type;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Tamamlandı';
      case 'pending':
        return 'Bekliyor';
      case 'failed':
        return 'Başarısız';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cüzdan Yönetimi</h2>
          <p className="text-gray-600">Bakiyenizi yönetin ve işlem geçmişinizi görüntüleyin</p>
        </div>
        <Button onClick={() => { fetchWalletData(); fetchTransactions(); }} disabled={loading}>
          <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Yenile
        </Button>
      </div>

      {/* Wallet Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mevcut Bakiye</CardTitle>
            <Wallet className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{formatCurrency(walletData.balance)}</div>
            <p className="text-xs text-muted-foreground">
              Kullanılabilir bakiye
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Hediye Çeki</CardTitle>
            <CreditCard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{formatCurrency(walletData.giftVoucherBalance)}</div>
            <p className="text-xs text-muted-foreground">
              Hediye çeki bakiyesi
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Kazanç</CardTitle>
            <ArrowDownLeft className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(walletData.totalEarned)}</div>
            <p className="text-xs text-muted-foreground">
              Tüm zamanlar
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Miktar</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{formatCurrency(walletData.pendingAmount)}</div>
            <p className="text-xs text-muted-foreground">
              İşlem bekleyen
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Para Yükle</CardTitle>
            <CardDescription>Cüzdanınıza para ekleyin</CardDescription>
          </CardHeader>
          <CardContent>
            {showAddFunds ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="addAmount">Miktar (₺)</Label>
                  <Input
                    id="addAmount"
                    type="number"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.00"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Button onClick={handleAddFunds} disabled={actionLoading}>
                    {actionLoading ? 'Yükleniyor...' : 'Para Yükle (Stripe)'}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleAddFundsDemo}
                    disabled={actionLoading}
                    className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
                  >
                    {actionLoading ? 'Yükleniyor...' : 'Demo Yükle'}
                  </Button>
                  <Button variant="outline" onClick={() => setShowAddFunds(false)}>
                    İptal
                  </Button>
                </div>
              </div>
            ) : (
              <Button onClick={() => setShowAddFunds(true)} className="w-full">
                <Plus className="w-4 h-4 mr-2" />
                Para Yükle
              </Button>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Para Çek</CardTitle>
            <CardDescription>Cüzdanınızdan para çekin</CardDescription>
          </CardHeader>
          <CardContent>
            {showWithdraw ? (
              <div className="space-y-4">
                <div>
                  <Label htmlFor="withdrawAmount">Miktar (₺)</Label>
                  <Input
                    id="withdrawAmount"
                    type="number"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    placeholder="0.00"
                    max={walletData.balance}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Maksimum: {formatCurrency(walletData.balance)}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button onClick={handleWithdraw} disabled={actionLoading}>
                    {actionLoading ? 'Çekiliyor...' : 'Para Çek'}
                  </Button>
                  <Button variant="outline" onClick={() => setShowWithdraw(false)}>
                    İptal
                  </Button>
                </div>
              </div>
            ) : (
              <Button 
                onClick={() => setShowWithdraw(true)} 
                className="w-full"
                disabled={walletData.balance <= 0}
              >
                <Minus className="w-4 h-4 mr-2" />
                Para Çek
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle>İşlem Geçmişi</CardTitle>
          <CardDescription>
            Son işlemlerinizi görüntüleyin
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-8">
              <Wallet className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">Henüz işlem bulunmuyor</p>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction._id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    {getTransactionIcon(transaction.type)}
                    <div>
                      <h3 className="font-medium">{getTypeText(transaction.type)}</h3>
                      <p className="text-sm text-gray-600">{transaction.description}</p>
                      <p className="text-xs text-gray-500">{formatDate(transaction.createdAt)}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <div className={`font-medium ${getTransactionColor(transaction.type)}`}>
                        {transaction.type === 'withdrawal' || transaction.type === 'purchase' ? '-' : '+'}
                        {formatCurrency(transaction.amount)}
                      </div>
                      <Badge className={getStatusColor(transaction.status)}>
                        {getStatusText(transaction.status)}
                      </Badge>
                    </div>
                    {getStatusIcon(transaction.status)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
