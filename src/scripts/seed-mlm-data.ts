import { client } from '@/sanity/lib/client';

const MLM_LEVELS = [
  {
    level: 'bronze',
    title: 'Bronz',
    description: 'Başlangıç seviyesi - MLM yolculuğunuzun ilk adımı',
    membershipFee: 0,
    monthlyFee: 0,
    productPackageValue: 100,
    discountPercentage: 5,
    directCommissionRate: 10,
    secondLevelCommissionRate: 5,
    thirdLevelCommissionRate: 2,
    fourthLevelCommissionRate: 1,
    giftVoucherAmount: 50,
    monthlyWalletBonus: 25,
    upgradeRequirements: {
      requiredReferrals: 0,
      requiredSalesVolume: 0,
      timeLimit: 0
    },
    benefits: [
      '%10 doğrudan komisyon',
      '50 TL hediye çeki',
      '25 TL aylık bonus',
      '%5 ürün indirimi'
    ],
    isActive: true,
    order: 1
  },
  {
    level: 'silver',
    title: 'Gümüş',
    description: 'İkinci seviye - daha yüksek komisyon oranları',
    membershipFee: 500,
    monthlyFee: 50,
    productPackageValue: 300,
    discountPercentage: 10,
    directCommissionRate: 15,
    secondLevelCommissionRate: 8,
    thirdLevelCommissionRate: 4,
    fourthLevelCommissionRate: 2,
    giftVoucherAmount: 100,
    monthlyWalletBonus: 50,
    upgradeRequirements: {
      requiredReferrals: 3,
      requiredSalesVolume: 5000,
      timeLimit: 90
    },
    benefits: [
      '%15 doğrudan komisyon',
      '100 TL hediye çeki',
      '50 TL aylık bonus',
      '%10 ürün indirimi',
      '4 seviye komisyon'
    ],
    isActive: true,
    order: 2
  },
  {
    level: 'gold',
    title: 'Altın',
    description: 'Üçüncü seviye - liderlik avantajları',
    membershipFee: 1500,
    monthlyFee: 100,
    productPackageValue: 750,
    discountPercentage: 15,
    directCommissionRate: 20,
    secondLevelCommissionRate: 12,
    thirdLevelCommissionRate: 6,
    fourthLevelCommissionRate: 3,
    giftVoucherAmount: 250,
    monthlyWalletBonus: 100,
    upgradeRequirements: {
      requiredReferrals: 5,
      requiredSalesVolume: 15000,
      timeLimit: 120
    },
    benefits: [
      '%20 doğrudan komisyon',
      '250 TL hediye çeki',
      '100 TL aylık bonus',
      '%15 ürün indirimi',
      'Liderlik bonusları',
      'Özel etkinlik davetleri'
    ],
    isActive: true,
    order: 3
  },
  {
    level: 'diamond',
    title: 'Elmas',
    description: 'Dördüncü seviye - premium avantajlar',
    membershipFee: 3000,
    monthlyFee: 200,
    productPackageValue: 1500,
    discountPercentage: 20,
    directCommissionRate: 25,
    secondLevelCommissionRate: 15,
    thirdLevelCommissionRate: 8,
    fourthLevelCommissionRate: 4,
    giftVoucherAmount: 500,
    monthlyWalletBonus: 200,
    upgradeRequirements: {
      requiredReferrals: 8,
      requiredSalesVolume: 30000,
      timeLimit: 150
    },
    benefits: [
      '%25 doğrudan komisyon',
      '500 TL hediye çeki',
      '200 TL aylık bonus',
      '%20 ürün indirimi',
      'Premium destek',
      'Aylık webinar erişimi',
      'Özel mentor desteği'
    ],
    isActive: true,
    order: 4
  },
  {
    level: 'platinum',
    title: 'Pırlanta',
    description: 'Beşinci seviye - elit üyelik',
    membershipFee: 5000,
    monthlyFee: 400,
    productPackageValue: 3000,
    discountPercentage: 25,
    directCommissionRate: 30,
    secondLevelCommissionRate: 20,
    thirdLevelCommissionRate: 12,
    fourthLevelCommissionRate: 6,
    giftVoucherAmount: 1000,
    monthlyWalletBonus: 400,
    upgradeRequirements: {
      requiredReferrals: 12,
      requiredSalesVolume: 50000,
      timeLimit: 180
    },
    benefits: [
      '%30 doğrudan komisyon',
      '1000 TL hediye çeki',
      '400 TL aylık bonus',
      '%25 ürün indirimi',
      'VIP müşteri hizmetleri',
      'Yıllık şirket gezisi',
      'Kişisel başarı koçu',
      'Özel ürün lansmanları'
    ],
    isActive: true,
    order: 5
  },
  {
    level: 'sapphire',
    title: 'Safir',
    description: 'En yüksek seviye - maksimum avantajlar',
    membershipFee: 10000,
    monthlyFee: 800,
    productPackageValue: 6000,
    discountPercentage: 30,
    directCommissionRate: 35,
    secondLevelCommissionRate: 25,
    thirdLevelCommissionRate: 15,
    fourthLevelCommissionRate: 8,
    giftVoucherAmount: 2000,
    monthlyWalletBonus: 800,
    upgradeRequirements: {
      requiredReferrals: 20,
      requiredSalesVolume: 100000,
      timeLimit: 365
    },
    benefits: [
      '%35 doğrudan komisyon',
      '2000 TL hediye çeki',
      '800 TL aylık bonus',
      '%30 ürün indirimi',
      'Şirket ortaklığı fırsatları',
      'Uluslararası konferans davetleri',
      'Özel yatırım danışmanlığı',
      'Lüks hediye paketleri',
      'Yıllık lüks tatil'
    ],
    isActive: true,
    order: 6
  }
];

const TEST_USERS = [
  {
    clerkId: 'test_user_1',
    name: 'Ahmet Yılmaz',
    email: '<EMAIL>',
    phone: '+90 ************',
    mlmLevel: 'gold',
    sponsorCode: 'AHMET2024',
    walletBalance: 2500,
    totalCommissionEarned: 8500,
    monthlyCommissionEarned: 1200,
    totalSales: 25000,
    totalTeamSales: 75000,
    directReferrals: 8,
    totalTeamMembers: 25,
    membershipStartDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
    lastLevelUpgrade: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    giftVoucherBalance: 300,
    isAdminApproved: true,
    isAdmin: false
  },
  {
    clerkId: 'test_user_2',
    name: 'Fatma Demir',
    email: '<EMAIL>',
    phone: '+90 ************',
    mlmLevel: 'silver',
    sponsorCode: 'FATMA2024',
    walletBalance: 1200,
    totalCommissionEarned: 4200,
    monthlyCommissionEarned: 650,
    totalSales: 12000,
    totalTeamSales: 35000,
    directReferrals: 5,
    totalTeamMembers: 12,
    membershipStartDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000).toISOString(),
    lastLevelUpgrade: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    giftVoucherBalance: 150,
    isAdminApproved: true,
    isAdmin: false
  },
  {
    clerkId: 'test_user_3',
    name: 'Mehmet Kaya',
    email: '<EMAIL>',
    phone: '+90 ************',
    mlmLevel: 'bronze',
    sponsorCode: 'MEHMET2024',
    walletBalance: 500,
    totalCommissionEarned: 1500,
    monthlyCommissionEarned: 300,
    totalSales: 5000,
    totalTeamSales: 8000,
    directReferrals: 2,
    totalTeamMembers: 3,
    membershipStartDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    giftVoucherBalance: 50,
    isAdminApproved: true,
    isAdmin: false
  },
  {
    clerkId: 'test_user_4',
    name: 'Ayşe Özkan',
    email: '<EMAIL>',
    phone: '+90 ************',
    mlmLevel: 'diamond',
    sponsorCode: 'AYSE2024',
    walletBalance: 5000,
    totalCommissionEarned: 15000,
    monthlyCommissionEarned: 2200,
    totalSales: 45000,
    totalTeamSales: 120000,
    directReferrals: 12,
    totalTeamMembers: 45,
    membershipStartDate: new Date(Date.now() - 300 * 24 * 60 * 60 * 1000).toISOString(),
    lastLevelUpgrade: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
    isActive: true,
    giftVoucherBalance: 800,
    isAdminApproved: true,
    isAdmin: false
  }
];

export async function seedMLMData() {
  try {
    console.log('🌱 Starting MLM data seeding...');

    // MLM seviyelerini oluştur
    console.log('📊 Creating MLM levels...');
    for (const levelData of MLM_LEVELS) {
      const existingLevel = await client.fetch(
        `*[_type == "mlmLevel" && level == $level][0]`,
        { level: levelData.level }
      );

      if (!existingLevel) {
        await client.create({
          _type: 'mlmLevel',
          ...levelData
        });
        console.log(`✅ Created level: ${levelData.title}`);
      } else {
        console.log(`⏭️  Level already exists: ${levelData.title}`);
      }
    }

    // Test kullanıcılarını oluştur
    console.log('👥 Creating test users...');
    const createdUsers = [];
    
    for (const userData of TEST_USERS) {
      const existingUser = await client.fetch(
        `*[_type == "user" && clerkId == $clerkId][0]`,
        { clerkId: userData.clerkId }
      );

      if (!existingUser) {
        const createdUser = await client.create({
          _type: 'user',
          ...userData
        });
        createdUsers.push(createdUser);
        console.log(`✅ Created user: ${userData.name}`);
      } else {
        createdUsers.push(existingUser);
        console.log(`⏭️  User already exists: ${userData.name}`);
      }
    }

    // Sponsor ilişkilerini kur
    console.log('🔗 Setting up sponsor relationships...');
    if (createdUsers.length >= 2) {
      // Fatma'nın sponsoru Ahmet olsun
      await client
        .patch(createdUsers[1]._id)
        .set({
          sponsor: {
            _type: 'reference',
            _ref: createdUsers[0]._id
          }
        })
        .commit();

      // Ahmet'in sol çocuğu Fatma olsun
      await client
        .patch(createdUsers[0]._id)
        .set({
          leftChild: {
            _type: 'reference',
            _ref: createdUsers[1]._id
          }
        })
        .commit();

      // Mehmet'in sponsoru Fatma olsun
      await client
        .patch(createdUsers[2]._id)
        .set({
          sponsor: {
            _type: 'reference',
            _ref: createdUsers[1]._id
          }
        })
        .commit();

      // Fatma'nın sol çocuğu Mehmet olsun
      await client
        .patch(createdUsers[1]._id)
        .set({
          leftChild: {
            _type: 'reference',
            _ref: createdUsers[2]._id
          }
        })
        .commit();

      console.log('✅ Sponsor relationships established');
    }

    // Örnek bonuslar oluştur
    console.log('🎁 Creating sample bonuses...');
    const sampleBonuses = [
      {
        _type: 'mlmBonus',
        recipient: { _type: 'reference', _ref: createdUsers[0]._id },
        bonusType: 'welcome_bonus',
        amount: 50,
        paymentType: 'gift_voucher',
        status: 'paid',
        description: 'Hoş geldin bonusu',
        createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString(),
        paidAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000).toISOString()
      },
      {
        _type: 'mlmBonus',
        recipient: { _type: 'reference', _ref: createdUsers[0]._id },
        bonusType: 'level_upgrade_bonus',
        amount: 250,
        paymentType: 'gift_voucher',
        status: 'paid',
        description: 'Altın seviyesine yükseltme bonusu',
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        paidAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    ];

    for (const bonus of sampleBonuses) {
      await client.create(bonus);
    }

    console.log('✅ Sample bonuses created');

    console.log('🎉 MLM data seeding completed successfully!');
    return { success: true, message: 'MLM data seeded successfully' };

  } catch (error) {
    console.error('❌ Error seeding MLM data:', error);
    return { success: false, error: error.message };
  }
}

// Script'i çalıştır
if (require.main === module) {
  seedMLMData().then((result) => {
    console.log('Result:', result);
    process.exit(result.success ? 0 : 1);
  });
}
