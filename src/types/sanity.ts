// Sanity TypeScript Types
// Generated manually from schemas

export interface SanityDocument {
  _id: string;
  _type: string;
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
}

export interface SanityImageAsset {
  _type: 'image';
  asset: {
    _ref: string;
    _type: 'reference';
    url?: string;
  };
  hotspot?: {
    x: number;
    y: number;
    height: number;
    width: number;
  };
}

export interface SanitySlug {
  _type: 'slug';
  current: string;
}

export interface SanityReference {
  _ref: string;
  _type: 'reference';
}

// Product Types
export interface SanityProduct extends SanityDocument {
  _type: 'product';
  name: string;
  slug: SanitySlug;
  description?: string;
  price: number;
  stock: number;
  category: 'electronics' | 'clothing' | 'home-garden' | 'sports' | 'books' | 'health-beauty' | 'automotive' | 'other';
  images: SanityImageAsset[];
  status: 'active' | 'inactive' | 'out_of_stock';
  vendor: SanityReference;
  tags?: string[];
  specifications?: Array<{
    key: string;
    value: string;
  }>;
}

// Vendor Types
export interface SanityVendor extends SanityDocument {
  _type: 'vendor';
  name: string;
  slug: SanitySlug;
  userId: string;
  email: string;
  description?: string;
  logo?: SanityImageAsset;
  banner?: SanityImageAsset;
  status: 'pending' | 'active' | 'inactive' | 'suspended';
  contactInfo?: {
    phone?: string;
    website?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      country?: string;
    };
  };
  businessInfo?: {
    businessName?: string;
    taxId?: string;
    businessType?: 'individual' | 'llc' | 'corporation' | 'partnership';
  };
  paymentInfo?: {
    accountNumber?: string;
    routingNumber?: string;
    bankName?: string;
    paypalEmail?: string;
  };
  totalSales: number;
  commissionRate: number;
  rating: number;
  totalReviews: number;
  joinDate: string;
}

// Order Types
export interface SanityOrder extends SanityDocument {
  _type: 'order';
  orderNumber: string;
  userId: string;
  userEmail: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  items: Array<{
    product: SanityReference;
    quantity: number;
    price: number;
    totalPrice: number;
  }>;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  totalAmount: number;
  shippingAddress?: {
    firstName: string;
    lastName: string;
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
    phone?: string;
  };
  paymentMethod?: 'credit_card' | 'paypal' | 'bank_transfer' | 'wallet';
  paymentStatus: 'pending' | 'paid' | 'failed' | 'refunded';
  paymentId?: string;
  trackingNumber?: string;
  notes?: string;
  mlmProcessed: boolean;
}

// Auction Types
export interface SanityAuction extends SanityDocument {
  _type: 'auction';
  title: string;
  description?: string;
  startingPrice: number;
  currentBid: number;
  endDate: string;
  status: 'active' | 'ended' | 'cancelled';
  product: SanityReference;
  vendor: SanityReference;
}

// Bid Types
export interface SanityBid extends SanityDocument {
  _type: 'bid';
  amount: number;
  userId: string;
  auction: SanityReference;
}

// Member Types (MLM)
export interface SanityMember extends SanityDocument {
  _type: 'member';
  userId: string;
  level: number;
  walletBalance: number;
  totalEarnings: number;
  status: 'active' | 'inactive' | 'suspended';
  parent?: SanityReference;
  leftChild?: SanityReference;
  rightChild?: SanityReference;
  referralCode: string;
  joinDate: string;
}

// Commission Types
export interface SanityCommission extends SanityDocument {
  _type: 'commission';
  amount: number;
  type: 'direct' | 'binary' | 'level' | 'bonus';
  level: number;
  memberId: string;
  fromMember?: SanityReference;
  order?: SanityReference;
  status: 'pending' | 'paid' | 'cancelled';
}

// Query Result Types
export interface ProductWithVendor extends Omit<SanityProduct, 'vendor'> {
  vendor: SanityVendor;
  imageUrl?: string;
}

export interface OrderWithItems extends Omit<SanityOrder, 'items'> {
  items: Array<{
    quantity: number;
    price: number;
    totalPrice: number;
    product: SanityProduct;
  }>;
}

export interface AuctionWithDetails extends Omit<SanityAuction, 'product' | 'vendor'> {
  product: SanityProduct;
  vendor: SanityVendor;
  imageUrl?: string;
  bids?: SanityBid[];
  highestBid?: SanityBid;
}

// GROQ Query Types
export type GroqQuery = string;
export type GroqParams = Record<string, any>;

// API Response Types
export interface SanityApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
