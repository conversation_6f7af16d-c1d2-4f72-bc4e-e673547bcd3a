// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: string;
  brand: string;
  stock: number;
  rating: number;
  reviews: number;
  isOnSale?: boolean;
  salePercentage?: number;
  tags: string[];
  specifications?: Record<string, string>;
  createdAt: Date;
  updatedAt: Date;
}

// Cart Types
export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  quantity: number;
  size?: string;
  color?: string;
}

export interface Cart {
  items: CartItem[];
  total: number;
  itemCount: number;
}

// User Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  addresses: Address[];
  orders: Order[];
}

export interface Address {
  id: string;
  name: string;
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  total: number;
  status: OrderStatus;
  shippingAddress: Address;
  paymentMethod: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum OrderStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled'
}

// Filter Types
export interface ProductFilters {
  category?: string;
  brand?: string;
  minPrice?: number;
  maxPrice?: number;
  rating?: number;
  inStock?: boolean;
  onSale?: boolean;
  sortBy?: 'price-asc' | 'price-desc' | 'rating' | 'newest' | 'popular';
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Review Types
export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  verified: boolean;
  helpful: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReviewStats {
  averageRating: number;
  totalReviews: number;
  ratingDistribution: {
    5: number;
    4: number;
    3: number;
    2: number;
    1: number;
  };
}

// Sanity Backend Types
export interface SanityProduct {
  _id: string;
  name: string;
  description?: string;
  price: number;
  stock: number;
  category: string;
  status: 'active' | 'inactive' | 'out_of_stock';
  images?: string[]; // Already processed URLs from GROQ query
  imageUrl?: string;
  vendor?: {
    _id: string;
    name: string;
    userId: string;
  };
  _createdAt: string;
  _updatedAt?: string;
}

export interface Auction {
  _id: string;
  title: string;
  description?: string;
  startingPrice: number;
  currentBid: number;
  endDate: string;
  status: 'active' | 'ended' | 'cancelled';
  imageUrl?: string;
  product: {
    _id: string;
    name: string;
    price: number;
    description?: string;
  };
  vendor: {
    _id: string;
    name: string;
    userId: string;
  };
  bids?: Bid[];
  highestBid?: Bid;
  createdAt: string;
}

export interface Bid {
  _id: string;
  amount: number;
  userId: string;
  auction: {
    _ref: string;
  };
  createdAt: string;
}

export interface Member {
  _id: string;
  userId: string;
  level: number;
  walletBalance: number;
  totalEarnings: number;
  status: 'active' | 'inactive' | 'suspended';
  parent?: {
    _id: string;
    userId: string;
    level: number;
    status: string;
  };
  leftChild?: {
    _id: string;
    userId: string;
    level: number;
    status: string;
    walletBalance: number;
  };
  rightChild?: {
    _id: string;
    userId: string;
    level: number;
    status: string;
    walletBalance: number;
  };
  directReferrals?: Member[];
  createdAt: string;
}

export interface SanityOrder {
  _id: string;
  orderNumber: string;
  userId: string;
  status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  totalAmount: number;
  items: OrderItem[];
  shippingAddress?: Address;
  paymentMethod?: string;
  createdAt: string;
  updatedAt: string;
}

export interface OrderItem {
  quantity: number;
  price: number;
  product: {
    _id: string;
    name: string;
    imageUrl?: string;
  };
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Commission {
  _id: string;
  amount: number;
  type: 'direct' | 'binary' | 'level' | 'bonus';
  level: number;
  memberId: string;
  fromMember?: {
    _id: string;
    userId: string;
  };
  order?: {
    _id: string;
    orderNumber: string;
    totalAmount: number;
  };
  status: 'pending' | 'paid' | 'cancelled';
  createdAt: string;
}

export interface Vendor {
  _id: string;
  name: string;
  userId: string;
  description?: string;
  status: 'active' | 'inactive' | 'pending';
  products?: SanityProduct[];
  totalSales: number;
  createdAt: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Dashboard stats
export interface DashboardStats {
  totalProducts: number;
  activeAuctions: number;
  totalMembers: number;
  totalOrders: number;
  recentOrders: SanityOrder[];
}

// Re-export Sanity types
export * from './sanity';
