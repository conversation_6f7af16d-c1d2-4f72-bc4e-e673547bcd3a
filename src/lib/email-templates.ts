export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export const emailTemplates = {
  welcomeBonus: (data: {
    userName: string;
    bonusAmount: number;
    sponsorName?: string;
  }): EmailTemplate => ({
    subject: '🎉 MLM Sistemine Hoş Geldiniz - Hoş Geldin Bonusunuz Hazır!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Hoş Geldin Bonusu</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .bonus-card { background: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; border-left: 5px solid #4CAF50; }
          .amount { font-size: 2em; font-weight: bold; color: #4CAF50; }
          .button { display: inline-block; background: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 0.9em; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Hoş Geldiniz ${data.userName}!</h1>
            <p>MLM ailemize katıldığınız için teşekkür ederiz</p>
          </div>
          <div class="content">
            <div class="bonus-card">
              <h2>🎁 Hoş Geldin Bonusunuz</h2>
              <div class="amount">₺${data.bonusAmount}</div>
              <p>Hediye çeki olarak hesabınıza yatırıldı</p>
            </div>
            
            <h3>Sırada Ne Var?</h3>
            <ul>
              <li>✅ Ürünlerimizi keşfedin ve alışveriş yapın</li>
              <li>✅ Arkadaşlarınızı davet edin ve komisyon kazanın</li>
              <li>✅ Seviyenizi yükseltin ve daha fazla avantaj elde edin</li>
              <li>✅ MLM dashboard'unuzdan performansınızı takip edin</li>
            </ul>

            ${data.sponsorName ? `
              <p><strong>Sponsor:</strong> ${data.sponsorName} size rehberlik edecek ve sorularınızı yanıtlayacak.</p>
            ` : ''}

            <div style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/mlm" class="button">MLM Dashboard'a Git</a>
            </div>
          </div>
          <div class="footer">
            <p>Bu email otomatik olarak gönderilmiştir. Lütfen yanıtlamayın.</p>
            <p>© 2024 E-Ticaret MLM Sistemi. Tüm hakları saklıdır.</p>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Hoş Geldiniz ${data.userName}!
      
      MLM ailemize katıldığınız için teşekkür ederiz.
      
      Hoş Geldin Bonusunuz: ₺${data.bonusAmount}
      Bu tutar hediye çeki olarak hesabınıza yatırıldı.
      
      Sırada Ne Var?
      - Ürünlerimizi keşfedin ve alışveriş yapın
      - Arkadaşlarınızı davet edin ve komisyon kazanın
      - Seviyenizi yükseltin ve daha fazla avantaj elde edin
      - MLM dashboard'unuzdan performansınızı takip edin
      
      ${data.sponsorName ? `Sponsor: ${data.sponsorName} size rehberlik edecek.` : ''}
      
      MLM Dashboard: ${process.env.NEXT_PUBLIC_APP_URL}/mlm
    `
  }),

  commissionEarned: (data: {
    userName: string;
    commissionAmount: number;
    commissionType: string;
    sourceUserName: string;
    totalEarnings: number;
  }): EmailTemplate => ({
    subject: '💰 Yeni Komisyon Kazancınız Var!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Komisyon Kazancı</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .commission-card { background: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; border-left: 5px solid #FF6B6B; }
          .amount { font-size: 2em; font-weight: bold; color: #FF6B6B; }
          .stats { display: flex; justify-content: space-around; margin: 20px 0; }
          .stat { text-align: center; }
          .stat-value { font-size: 1.5em; font-weight: bold; color: #4CAF50; }
          .button { display: inline-block; background: #FF6B6B; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💰 Tebrikler ${data.userName}!</h1>
            <p>Yeni bir komisyon kazancınız var</p>
          </div>
          <div class="content">
            <div class="commission-card">
              <h2>🎯 ${data.commissionType}</h2>
              <div class="amount">₺${data.commissionAmount}</div>
              <p>Kaynak: ${data.sourceUserName}</p>
            </div>
            
            <div class="stats">
              <div class="stat">
                <div class="stat-value">₺${data.totalEarnings}</div>
                <div>Toplam Kazanç</div>
              </div>
            </div>

            <p>Bu komisyon cüzdan bakiyenize eklendi. MLM dashboard'unuzdan detayları görüntüleyebilirsiniz.</p>

            <div style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/mlm" class="button">Komisyonlarımı Görüntüle</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Tebrikler ${data.userName}!
      
      Yeni Komisyon Kazancınız: ₺${data.commissionAmount}
      Komisyon Türü: ${data.commissionType}
      Kaynak: ${data.sourceUserName}
      
      Toplam Kazancınız: ₺${data.totalEarnings}
      
      Bu komisyon cüzdan bakiyenize eklendi.
      
      Detaylar: ${process.env.NEXT_PUBLIC_APP_URL}/mlm
    `
  }),

  levelUpgrade: (data: {
    userName: string;
    oldLevel: string;
    newLevel: string;
    bonusAmount: number;
    newBenefits: string[];
  }): EmailTemplate => ({
    subject: '🚀 Seviye Yükseltme Tebrikler!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Seviye Yükseltme</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .upgrade-card { background: white; padding: 20px; border-radius: 10px; text-align: center; margin: 20px 0; border-left: 5px solid #9C27B0; }
          .level-badge { display: inline-block; background: #9C27B0; color: white; padding: 10px 20px; border-radius: 20px; font-weight: bold; margin: 10px; }
          .benefits { background: white; padding: 20px; border-radius: 10px; margin: 20px 0; }
          .benefit-item { padding: 5px 0; }
          .button { display: inline-block; background: #9C27B0; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 Tebrikler ${data.userName}!</h1>
            <p>Seviyeniz başarıyla yükseltildi</p>
          </div>
          <div class="content">
            <div class="upgrade-card">
              <h2>🎖️ Seviye Yükseltme</h2>
              <div>
                <span class="level-badge">${data.oldLevel}</span>
                <span style="font-size: 1.5em;">→</span>
                <span class="level-badge">${data.newLevel}</span>
              </div>
              <p><strong>Yükseltme Bonusu:</strong> ₺${data.bonusAmount}</p>
            </div>
            
            <div class="benefits">
              <h3>🎁 Yeni Avantajlarınız</h3>
              ${data.newBenefits.map(benefit => `
                <div class="benefit-item">✅ ${benefit}</div>
              `).join('')}
            </div>

            <p>Yeni seviyenizle birlikte daha yüksek komisyon oranları ve özel avantajlardan yararlanabilirsiniz!</p>

            <div style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/mlm" class="button">Yeni Avantajlarımı Keşfet</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      Tebrikler ${data.userName}!
      
      Seviyeniz başarıyla yükseltildi:
      ${data.oldLevel} → ${data.newLevel}
      
      Yükseltme Bonusu: ₺${data.bonusAmount}
      
      Yeni Avantajlarınız:
      ${data.newBenefits.map(benefit => `- ${benefit}`).join('\n')}
      
      Detaylar: ${process.env.NEXT_PUBLIC_APP_URL}/mlm
    `
  }),

  monthlyReport: (data: {
    userName: string;
    month: string;
    totalCommissions: number;
    totalBonuses: number;
    newReferrals: number;
    teamGrowth: number;
    nextLevelProgress: number;
  }): EmailTemplate => ({
    subject: '📊 Aylık MLM Performans Raporu',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Aylık Rapor</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }
          .stats-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 20px 0; }
          .stat-card { background: white; padding: 15px; border-radius: 10px; text-align: center; }
          .stat-value { font-size: 1.8em; font-weight: bold; color: #2196F3; }
          .progress-bar { background: #e0e0e0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
          .progress-fill { background: #4CAF50; height: 20px; transition: width 0.3s ease; }
          .button { display: inline-block; background: #2196F3; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 ${data.month} Performans Raporu</h1>
            <p>Merhaba ${data.userName}, aylık performansınız hazır!</p>
          </div>
          <div class="content">
            <div class="stats-grid">
              <div class="stat-card">
                <div class="stat-value">₺${data.totalCommissions}</div>
                <div>Toplam Komisyon</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">₺${data.totalBonuses}</div>
                <div>Toplam Bonus</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">${data.newReferrals}</div>
                <div>Yeni Referans</div>
              </div>
              <div class="stat-card">
                <div class="stat-value">%${data.teamGrowth}</div>
                <div>Takım Büyümesi</div>
              </div>
            </div>

            <div style="background: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
              <h3>🎯 Sonraki Seviye İlerlemesi</h3>
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${data.nextLevelProgress}%"></div>
              </div>
              <p>%${data.nextLevelProgress} tamamlandı</p>
            </div>

            <h3>💡 Bu Ay İçin Öneriler</h3>
            <ul>
              <li>Daha fazla referans getirerek komisyon kazancınızı artırın</li>
              <li>Takımınızla düzenli iletişim kurarak motivasyonu yüksek tutun</li>
              <li>Yeni ürün lansmanlarını takip edin</li>
              <li>Seviye yükseltme şartlarınızı kontrol edin</li>
            </ul>

            <div style="text-align: center;">
              <a href="${process.env.NEXT_PUBLIC_APP_URL}/mlm" class="button">Detaylı Raporu Görüntüle</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `,
    text: `
      ${data.month} Performans Raporu
      
      Merhaba ${data.userName},
      
      Bu Ay Performansınız:
      - Toplam Komisyon: ₺${data.totalCommissions}
      - Toplam Bonus: ₺${data.totalBonuses}
      - Yeni Referans: ${data.newReferrals}
      - Takım Büyümesi: %${data.teamGrowth}
      
      Sonraki Seviye İlerlemesi: %${data.nextLevelProgress}
      
      Detaylı Rapor: ${process.env.NEXT_PUBLIC_APP_URL}/mlm
    `
  })
};
