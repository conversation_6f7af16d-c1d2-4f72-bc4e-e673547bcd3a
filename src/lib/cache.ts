// Simple in-memory cache for API responses
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class MemoryCache {
  private cache = new Map<string, CacheItem<any>>();
  private readonly defaultTTL = 5 * 60 * 1000; // 5 minutes

  set<T>(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
    };
    this.cache.set(key, item);
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    const now = Date.now();
    const isExpired = now - item.timestamp > item.ttl;

    if (isExpired) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    let validItems = 0;
    let expiredItems = 0;

    for (const [key, item] of this.cache.entries()) {
      const isExpired = now - item.timestamp > item.ttl;
      if (isExpired) {
        expiredItems++;
        this.cache.delete(key); // Clean up expired items
      } else {
        validItems++;
      }
    }

    return {
      totalItems: validItems,
      expiredItems,
      size: this.cache.size,
    };
  }

  // Generate cache key from URL and params
  generateKey(baseKey: string, params?: Record<string, any>): string {
    if (!params) return baseKey;
    
    const sortedParams = Object.keys(params)
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&');
    
    return `${baseKey}?${sortedParams}`;
  }
}

// Export singleton instance
export const apiCache = new MemoryCache();

// Cache TTL constants
export const CACHE_TTL = {
  PRODUCTS: 5 * 60 * 1000,      // 5 minutes
  CATEGORIES: 10 * 60 * 1000,   // 10 minutes
  PRODUCT_DETAIL: 3 * 60 * 1000, // 3 minutes
  SEARCH: 2 * 60 * 1000,        // 2 minutes
} as const;

// Cache key constants
export const CACHE_KEYS = {
  PRODUCTS: 'products',
  CATEGORIES: 'categories',
  PRODUCT_DETAIL: 'product-detail',
  SEARCH: 'search',
  FEATURED_PRODUCTS: 'featured-products',
} as const;

// Utility function for cached API calls
export async function withCache<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl?: number
): Promise<T> {
  // Try to get from cache first
  const cached = apiCache.get<T>(key);
  if (cached !== null) {
    console.log(`Cache HIT for key: ${key}`);
    return cached;
  }

  console.log(`Cache MISS for key: ${key}`);
  
  // Fetch fresh data
  try {
    const data = await fetcher();
    apiCache.set(key, data, ttl);
    return data;
  } catch (error) {
    console.error(`Error fetching data for key ${key}:`, error);
    throw error;
  }
}

// Invalidate cache by pattern
export function invalidateCache(pattern: string): number {
  let deletedCount = 0;
  const keys = Array.from(apiCache['cache'].keys());
  
  for (const key of keys) {
    if (key.includes(pattern)) {
      apiCache.delete(key);
      deletedCount++;
    }
  }
  
  console.log(`Invalidated ${deletedCount} cache entries matching pattern: ${pattern}`);
  return deletedCount;
}

// Preload cache with common data
export async function preloadCache() {
  try {
    console.log('Preloading cache with common data...');
    
    // Preload featured products
    const featuredKey = CACHE_KEYS.FEATURED_PRODUCTS;
    if (!apiCache.get(featuredKey)) {
      // This would be called from your data fetching functions
      console.log('Featured products cache preloaded');
    }
    
    // Preload categories
    const categoriesKey = CACHE_KEYS.CATEGORIES;
    if (!apiCache.get(categoriesKey)) {
      console.log('Categories cache preloaded');
    }
    
  } catch (error) {
    console.error('Error preloading cache:', error);
  }
}
