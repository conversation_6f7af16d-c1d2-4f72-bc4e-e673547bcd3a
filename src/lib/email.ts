// Email service for sending notifications
// In production, you would use services like SendGrid, Mailgun, or AWS SES

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface EmailData {
  to: string;
  from?: string;
  subject: string;
  html: string;
  text?: string;
}

// Email templates
export const emailTemplates = {
  orderConfirmation: (data: {
    orderNumber: string;
    customerName: string;
    items: Array<{ name: string; quantity: number; price: number }>;
    total: number;
    shippingAddress: string;
  }): EmailTemplate => ({
    subject: `Order Confirmation - ${data.orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Thank you for your order!</h1>
        <p>Hi ${data.customerName},</p>
        <p>We've received your order and are preparing it for shipment.</p>
        
        <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h2 style="margin-top: 0;">Order Details</h2>
          <p><strong>Order Number:</strong> ${data.orderNumber}</p>
          
          <h3>Items Ordered:</h3>
          <ul>
            ${data.items.map(item => `
              <li>${item.name} - Quantity: ${item.quantity} - $${item.price.toFixed(2)}</li>
            `).join('')}
          </ul>
          
          <p><strong>Total: $${data.total.toFixed(2)}</strong></p>
          
          <h3>Shipping Address:</h3>
          <p>${data.shippingAddress}</p>
        </div>
        
        <p>You'll receive another email when your order ships with tracking information.</p>
        <p>Thank you for shopping with us!</p>
      </div>
    `,
    text: `
      Thank you for your order!
      
      Hi ${data.customerName},
      
      We've received your order ${data.orderNumber} and are preparing it for shipment.
      
      Items: ${data.items.map(item => `${item.name} (${item.quantity}x) - $${item.price.toFixed(2)}`).join(', ')}
      Total: $${data.total.toFixed(2)}
      
      Shipping to: ${data.shippingAddress}
      
      You'll receive tracking information when your order ships.
    `
  }),

  orderShipped: (data: {
    orderNumber: string;
    customerName: string;
    trackingNumber: string;
    estimatedDelivery: string;
    carrierName: string;
  }): EmailTemplate => ({
    subject: `Your order ${data.orderNumber} has shipped!`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Your order is on its way!</h1>
        <p>Hi ${data.customerName},</p>
        <p>Great news! Your order ${data.orderNumber} has been shipped and is on its way to you.</p>
        
        <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #3b82f6;">
          <h2 style="margin-top: 0; color: #1e40af;">Tracking Information</h2>
          <p><strong>Tracking Number:</strong> ${data.trackingNumber}</p>
          <p><strong>Carrier:</strong> ${data.carrierName}</p>
          <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
        </div>
        
        <p>You can track your package using the tracking number above on the carrier's website.</p>
        <p>We'll send you another email when your package is delivered.</p>
      </div>
    `,
    text: `
      Your order is on its way!
      
      Hi ${data.customerName},
      
      Your order ${data.orderNumber} has shipped!
      
      Tracking Number: ${data.trackingNumber}
      Carrier: ${data.carrierName}
      Estimated Delivery: ${data.estimatedDelivery}
      
      Track your package on the carrier's website.
    `
  }),

  orderDelivered: (data: {
    orderNumber: string;
    customerName: string;
    deliveryDate: string;
  }): EmailTemplate => ({
    subject: `Your order ${data.orderNumber} has been delivered!`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #16a34a;">Order Delivered Successfully!</h1>
        <p>Hi ${data.customerName},</p>
        <p>Your order ${data.orderNumber} was delivered on ${data.deliveryDate}.</p>
        
        <div style="background: #f0fdf4; padding: 20px; margin: 20px 0; border-radius: 8px; border-left: 4px solid #16a34a;">
          <h2 style="margin-top: 0; color: #15803d;">Thank you for your purchase!</h2>
          <p>We hope you love your new items. If you have any questions or concerns, please don't hesitate to contact us.</p>
        </div>
        
        <p>Would you like to leave a review? Your feedback helps other customers and helps us improve.</p>
        <p style="text-align: center;">
          <a href="#" style="background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Leave a Review</a>
        </p>
      </div>
    `,
    text: `
      Order Delivered Successfully!
      
      Hi ${data.customerName},
      
      Your order ${data.orderNumber} was delivered on ${data.deliveryDate}.
      
      Thank you for your purchase! Please consider leaving a review.
    `
  }),

  reviewRequest: (data: {
    customerName: string;
    orderNumber: string;
    items: Array<{ name: string; id: string }>;
  }): EmailTemplate => ({
    subject: `How was your recent purchase? Leave a review`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">How was your experience?</h1>
        <p>Hi ${data.customerName},</p>
        <p>We hope you're enjoying your recent purchase from order ${data.orderNumber}!</p>
        
        <div style="background: #fef3c7; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h2 style="margin-top: 0;">Items you purchased:</h2>
          <ul>
            ${data.items.map(item => `<li>${item.name}</li>`).join('')}
          </ul>
        </div>
        
        <p>Your feedback is valuable to us and helps other customers make informed decisions.</p>
        <p style="text-align: center;">
          <a href="#" style="background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Write a Review</a>
        </p>
        
        <p style="font-size: 14px; color: #666;">
          This is a one-time email. We won't send you repeated review requests for this order.
        </p>
      </div>
    `,
    text: `
      How was your experience?
      
      Hi ${data.customerName},
      
      We hope you're enjoying your recent purchase from order ${data.orderNumber}!
      
      Items: ${data.items.map(item => item.name).join(', ')}
      
      Please consider leaving a review to help other customers.
    `
  }),

  passwordReset: (data: {
    customerName: string;
    resetLink: string;
    expiresIn: string;
  }): EmailTemplate => ({
    subject: 'Reset your password',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #333;">Reset Your Password</h1>
        <p>Hi ${data.customerName},</p>
        <p>We received a request to reset your password. Click the button below to create a new password:</p>
        
        <p style="text-align: center; margin: 30px 0;">
          <a href="${data.resetLink}" style="background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
        </p>
        
        <p>This link will expire in ${data.expiresIn}.</p>
        <p>If you didn't request this password reset, please ignore this email.</p>
        
        <p style="font-size: 14px; color: #666;">
          If the button doesn't work, copy and paste this link into your browser:<br>
          ${data.resetLink}
        </p>
      </div>
    `,
    text: `
      Reset Your Password
      
      Hi ${data.customerName},
      
      We received a request to reset your password.
      
      Click this link to reset your password: ${data.resetLink}
      
      This link expires in ${data.expiresIn}.
      
      If you didn't request this, please ignore this email.
    `
  }),

  welcomeEmail: (data: {
    customerName: string;
    email: string;
  }): EmailTemplate => ({
    subject: 'Welcome to Shoptech!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h1 style="color: #3b82f6;">Welcome to Shoptech!</h1>
        <p>Hi ${data.customerName},</p>
        <p>Welcome to Shoptech! We're excited to have you as part of our community.</p>
        
        <div style="background: #f0f9ff; padding: 20px; margin: 20px 0; border-radius: 8px;">
          <h2 style="margin-top: 0;">What's next?</h2>
          <ul>
            <li>Browse our latest products</li>
            <li>Set up your profile and preferences</li>
            <li>Join our newsletter for exclusive deals</li>
            <li>Follow us on social media</li>
          </ul>
        </div>
        
        <p>If you have any questions, our customer support team is here to help!</p>
        <p>Happy shopping!</p>
      </div>
    `,
    text: `
      Welcome to Shoptech!
      
      Hi ${data.customerName},
      
      Welcome to Shoptech! We're excited to have you join our community.
      
      Start by browsing our products and setting up your profile.
      
      If you need help, our support team is here for you.
    `
  })
};

// Mock email service (in production, replace with real email service)
export class EmailService {
  private static instance: EmailService;
  private sentEmails: Array<EmailData & { sentAt: Date }> = [];

  static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  async sendEmail(emailData: EmailData): Promise<boolean> {
    try {
      // In production, this would call your email service API
      console.log('📧 Sending email:', {
        to: emailData.to,
        subject: emailData.subject,
        timestamp: new Date().toISOString()
      });

      // Store sent email for testing/debugging
      this.sentEmails.push({
        ...emailData,
        sentAt: new Date()
      });

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100));

      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  // Get sent emails for testing
  getSentEmails(): Array<EmailData & { sentAt: Date }> {
    return this.sentEmails;
  }

  // Clear sent emails
  clearSentEmails(): void {
    this.sentEmails = [];
  }
}

// Convenience functions for common email types
export const sendOrderConfirmationEmail = async (orderData: any) => {
  const emailService = EmailService.getInstance();
  const template = emailTemplates.orderConfirmation(orderData);
  
  return emailService.sendEmail({
    to: orderData.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text
  });
};

export const sendOrderShippedEmail = async (orderData: any) => {
  const emailService = EmailService.getInstance();
  const template = emailTemplates.orderShipped(orderData);
  
  return emailService.sendEmail({
    to: orderData.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text
  });
};

export const sendOrderDeliveredEmail = async (orderData: any) => {
  const emailService = EmailService.getInstance();
  const template = emailTemplates.orderDelivered(orderData);
  
  return emailService.sendEmail({
    to: orderData.customerEmail,
    subject: template.subject,
    html: template.html,
    text: template.text
  });
};
