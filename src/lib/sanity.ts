// Re-export the official Sanity client
export { client as cdnClient } from '../sanity/lib/client';

import { createClient } from 'next-sanity';
import { apiVersion, dataset, projectId } from '../sanity/env';

// Gerçek zamanlı veriler için CDN olmayan istemci
export const liveClient = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false, // Gerçek zamanlı veriler için CDN kapalı
  token: process.env.SANITY_API_TOKEN,
});

// Yazma işlemleri için token'lı istemci
export const writeClient = createClient({
  projectId,
  dataset,
  apiVersion,
  useCdn: false,
  token: process.env.SANITY_API_TOKEN,
});

// Sanity client helper functions
export const sanityFetch = async (query: string, params = {}) => {
  return cdnClient.fetch(query, params);
};

export const sanityLiveFetch = async (query: string, params = {}) => {
  return liveClient.fetch(query, params);
};

export const sanityWrite = async (mutations: any[]) => {
  return writeClient.mutate(mutations);
};
