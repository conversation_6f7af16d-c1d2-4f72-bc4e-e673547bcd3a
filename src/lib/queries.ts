// Optimize edilmiş GROQ sorguları

// Sadece gerekli alanları çeken ürün listesi sorgusu
export const optimizedProductsQuery = `*[_type == "product" && vendor._ref == $vendorId]{
  _id, name, price, stock, category, status,
  "imageUrl": images[0].asset->url,
  "vendor": vendor->{_id, name, userId}
}[0...20]`;

// Tüm aktif ürünler
export const allActiveProductsQuery = `*[_type == "product" && status == "active"]{
  _id, name, price, stock, category,
  "imageUrl": images[0].asset->url,
  "vendor": vendor->{_id, name, userId}
}[0...50]`;

// İndekslenmiş alanlarla filtreleme - aktif açık artırmalar
export const activeAuctionsQuery = `*[_type == "auction" && status == "active" && endDate > now()]{
  _id, title, startingPrice, currentBid, endDate, status,
  "imageUrl": product->images[0].asset->url,
  "product": product->{_id, name, price},
  "highestBid": *[_type == "bid" && auction._ref == ^._id] | order(amount desc)[0]{
    _id, amount, userId, createdAt
  }
}`;

// Belirli bir açık artırma detayı
export const auctionDetailQuery = `*[_type == "auction" && _id == $auctionId][0]{
  _id, title, description, startingPrice, currentBid, endDate, status, createdAt,
  "imageUrl": product->images[0].asset->url,
  "product": product->{_id, name, price, description},
  "vendor": vendor->{_id, name, userId},
  "bids": *[_type == "bid" && auction._ref == ^._id] | order(amount desc)[0...10]{
    _id, amount, userId, createdAt
  }
}`;

// Birleşik sorgu örneği (MLM ağacı)
export const mlmTreeQuery = `*[_type == "member" && userId == $userId][0]{
  _id, userId, level, walletBalance, totalEarnings, status,
  "parent": parent->{_id, userId, level, status},
  "leftChild": leftChild->{_id, userId, level, status, walletBalance},
  "rightChild": rightChild->{_id, userId, level, status, walletBalance},
  "directReferrals": *[_type == "member" && parent._ref == ^._id]{
    _id, userId, level, status, walletBalance, createdAt
  }
}`;

// Kullanıcının siparişleri
export const userOrdersQuery = `*[_type == "order" && userId == $userId] | order(createdAt desc){
  _id, orderNumber, status, totalAmount, createdAt,
  "items": items[]{
    quantity, price,
    "product": product->{_id, name, "imageUrl": images[0].asset->url}
  }
}[0...20]`;

// Satıcının siparişleri
export const vendorOrdersQuery = `*[_type == "order" && items[].product._ref in *[_type == "product" && vendor._ref == $vendorId]._id] | order(createdAt desc){
  _id, orderNumber, status, totalAmount, createdAt, userId,
  "items": items[]{
    quantity, price,
    "product": product->{_id, name, "imageUrl": images[0].asset->url}
  }
}[0...50]`;

// Komisyon geçmişi
export const commissionHistoryQuery = `*[_type == "commission" && memberId == $memberId] | order(createdAt desc){
  _id, amount, type, level, createdAt, status,
  "fromMember": fromMember->{_id, userId},
  "order": order->{_id, orderNumber, totalAmount}
}[0...50]`;

// Dashboard istatistikleri
export const dashboardStatsQuery = `{
  "totalProducts": count(*[_type == "product"]),
  "activeAuctions": count(*[_type == "auction" && status == "active"]),
  "totalMembers": count(*[_type == "member"]),
  "totalOrders": count(*[_type == "order"]),
  "recentOrders": *[_type == "order"] | order(createdAt desc)[0...5]{
    _id, orderNumber, status, totalAmount, createdAt
  }
}`;

// Belirli bir üyenin alt ağı (MLM)
export const memberNetworkQuery = `*[_type == "member" && parent._ref == $memberId]{
  _id, userId, level, walletBalance, status, createdAt,
  "children": *[_type == "member" && parent._ref == ^._id]{
    _id, userId, level, walletBalance, status
  }
}`;
