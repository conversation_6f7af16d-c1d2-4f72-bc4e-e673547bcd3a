[{"name": "commission", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "commission"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "memberId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "member": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "member"}, "optional": true}, "fromMember": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "member"}, "optional": true}, "order": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "order"}, "optional": true}, "amount": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "type": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "direct"}, {"type": "string", "value": "binary"}, {"type": "string", "value": "level"}, {"type": "string", "value": "rank"}, {"type": "string", "value": "leadership"}, {"type": "string", "value": "special"}]}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "percentage": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "pending"}, {"type": "string", "value": "approved"}, {"type": "string", "value": "paid"}, {"type": "string", "value": "cancelled"}, {"type": "string", "value": "on_hold"}]}, "optional": true}, "paidDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "paymentMethod": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "wallet"}, {"type": "string", "value": "bank_transfer"}, {"type": "string", "value": "paypal"}, {"type": "string", "value": "check"}]}, "optional": true}, "paymentReference": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "notes": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "calculatedAt": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "period": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "bid", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "bid"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "auction": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "auction"}, "optional": true}, "userId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "userEmail": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "amount": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "active"}, {"type": "string", "value": "outbid"}, {"type": "string", "value": "winning"}, {"type": "string", "value": "won"}, {"type": "string", "value": "lost"}]}, "optional": true}, "isAutoBid": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "maxAutoBid": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bidTime": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "ipAddress": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "userAgent": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "order", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "order"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "orderNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "userId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "userEmail": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "pending"}, {"type": "string", "value": "processing"}, {"type": "string", "value": "shipped"}, {"type": "string", "value": "delivered"}, {"type": "string", "value": "cancelled"}, {"type": "string", "value": "refunded"}]}, "optional": true}, "items": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"product": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "product"}, "optional": true}, "quantity": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "totalPrice": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "subtotal": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "tax": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "shipping": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "discount": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "totalAmount": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "shippingAddress": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"firstName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "lastName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "street": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "city": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "state": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "zipCode": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "country": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "phone": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "paymentMethod": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "credit_card"}, {"type": "string", "value": "paypal"}, {"type": "string", "value": "bank_transfer"}, {"type": "string", "value": "wallet"}]}, "optional": true}, "paymentStatus": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "pending"}, {"type": "string", "value": "paid"}, {"type": "string", "value": "failed"}, {"type": "string", "value": "refunded"}]}, "optional": true}, "paymentId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "trackingNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "notes": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mlmProcessed": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, {"name": "member", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "member"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "userId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "firstName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "lastName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "level": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "walletBalance": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "totalEarnings": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "active"}, {"type": "string", "value": "inactive"}, {"type": "string", "value": "suspended"}]}, "optional": true}, "parent": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "member"}, "optional": true}, "leftChild": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "member"}, "optional": true}, "rightChild": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "member"}, "optional": true}, "leftVolume": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "rightVolume": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "personalVolume": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "rank": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "associate"}, {"type": "string", "value": "senior_associate"}, {"type": "string", "value": "team_leader"}, {"type": "string", "value": "senior_team_leader"}, {"type": "string", "value": "manager"}, {"type": "string", "value": "senior_manager"}, {"type": "string", "value": "director"}, {"type": "string", "value": "senior_director"}, {"type": "string", "value": "executive"}]}, "optional": true}, "joinDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "lastActiveDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "referralCode": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bankDetails": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"accountNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "routingNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bankName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "accountHolderName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, {"name": "auction", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "auction"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "product": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "product"}, "optional": true}, "vendor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "vendor"}, "optional": true}, "startingPrice": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "currentBid": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "startDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "endDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "scheduled"}, {"type": "string", "value": "active"}, {"type": "string", "value": "ended"}, {"type": "string", "value": "cancelled"}]}, "optional": true}, "minBidIncrement": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "reservePrice": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "buyNowPrice": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "winnerId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "winningBid": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "totalBids": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "featured": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}, {"name": "product", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "product"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "price": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "stock": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "category": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "electronics"}, {"type": "string", "value": "clothing"}, {"type": "string", "value": "home-garden"}, {"type": "string", "value": "sports"}, {"type": "string", "value": "books"}, {"type": "string", "value": "health-beauty"}, {"type": "string", "value": "automotive"}, {"type": "string", "value": "other"}]}, "optional": true}, "images": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "active"}, {"type": "string", "value": "inactive"}, {"type": "string", "value": "out_of_stock"}]}, "optional": true}, "vendor": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "vendor"}, "optional": true}, "tags": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "string"}}, "optional": true}, "specifications": {"type": "objectAttribute", "value": {"type": "array", "of": {"type": "object", "attributes": {"key": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "value": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}, "rest": {"type": "object", "attributes": {"_key": {"type": "objectAttribute", "value": {"type": "string"}}}}}}, "optional": true}}}, {"name": "vendor", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "vendor"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "slug": {"type": "objectAttribute", "value": {"type": "inline", "name": "slug"}, "optional": true}, "userId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "email": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "logo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "banner": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"asset": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"_ref": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "reference"}}, "_weak": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}, "dereferencesTo": "sanity.imageAsset"}, "optional": true}, "media": {"type": "objectAttribute", "value": {"type": "unknown"}, "optional": true}, "hotspot": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageHotspot"}, "optional": true}, "crop": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageCrop"}, "optional": true}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "image"}}}}, "optional": true}, "status": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "pending"}, {"type": "string", "value": "active"}, {"type": "string", "value": "inactive"}, {"type": "string", "value": "suspended"}]}, "optional": true}, "contactInfo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"phone": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "website": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "address": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"street": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "city": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "state": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "zipCode": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "country": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}}}, "optional": true}, "businessInfo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"businessName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "taxId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "businessType": {"type": "objectAttribute", "value": {"type": "union", "of": [{"type": "string", "value": "individual"}, {"type": "string", "value": "llc"}, {"type": "string", "value": "corporation"}, {"type": "string", "value": "partnership"}]}, "optional": true}}}, "optional": true}, "paymentInfo": {"type": "objectAttribute", "value": {"type": "object", "attributes": {"accountNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "routingNumber": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "bankName": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "paypalEmail": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, "optional": true}, "totalSales": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "commissionRate": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "rating": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "totalReviews": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "joinDate": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}, {"name": "sanity.imagePaletteSwatch", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePaletteSwatch"}}, "background": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "foreground": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "population": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.imagePalette", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imagePalette"}}, "darkMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "darkVibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "vibrant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "dominant": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "lightMuted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}, "muted": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePaletteSwatch"}, "optional": true}}}}, {"name": "sanity.imageDimensions", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageDimensions"}}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "aspectRatio": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageHotspot", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageHotspot"}}, "x": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "y": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "height": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "width": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.imageCrop", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageCrop"}}, "top": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "bottom": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "left": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "right": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "sanity.fileAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.fileAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageAsset", "type": "document", "attributes": {"_id": {"type": "objectAttribute", "value": {"type": "string"}}, "_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageAsset"}}, "_createdAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_updatedAt": {"type": "objectAttribute", "value": {"type": "string"}}, "_rev": {"type": "objectAttribute", "value": {"type": "string"}}, "originalFilename": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "label": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "title": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "description": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "altText": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "sha1hash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "extension": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "mimeType": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "size": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "assetId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "uploadId": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "path": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "metadata": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageMetadata"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.assetSourceData"}, "optional": true}}}, {"name": "sanity.imageMetadata", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.imageMetadata"}}, "location": {"type": "objectAttribute", "value": {"type": "inline", "name": "geopoint"}, "optional": true}, "dimensions": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imageDimensions"}, "optional": true}, "palette": {"type": "objectAttribute", "value": {"type": "inline", "name": "sanity.imagePalette"}, "optional": true}, "lqip": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "blurHash": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "hasAlpha": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}, "isOpaque": {"type": "objectAttribute", "value": {"type": "boolean"}, "optional": true}}}}, {"name": "geopoint", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "geopoint"}}, "lat": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "lng": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}, "alt": {"type": "objectAttribute", "value": {"type": "number"}, "optional": true}}}}, {"name": "slug", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "slug"}}, "current": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "source": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}, {"name": "sanity.assetSourceData", "type": "type", "value": {"type": "object", "attributes": {"_type": {"type": "objectAttribute", "value": {"type": "string", "value": "sanity.assetSourceData"}}, "name": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "id": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}, "url": {"type": "objectAttribute", "value": {"type": "string"}, "optional": true}}}}]