# 🛍️ Shoptech - Modern E-commerce Platform

A cutting-edge e-commerce platform built with **Next.js 15**, **React 19**, and modern web technologies. Experience lightning-fast performance with server-side rendering, seamless shopping cart functionality, and secure payment processing.

## ✨ Features

- 🚀 **Next.js 15 & React 19** - Latest features with optimal performance
- 🎨 **Modern UI/UX** - Built with TailwindCSS and ShadCN UI components
- 🛒 **Smart Shopping Cart** - Persistent cart with Zustand state management
- 🔐 **Secure Authentication** - Powered by Clerk
- 💳 **Payment Integration** - Stripe for secure transactions
- 📱 **Responsive Design** - Perfect on all devices
- 🎭 **Smooth Animations** - Enhanced with Framer Motion
- 🔍 **Advanced Filtering** - Smart product search and filtering
- 🏷️ **Hot Deals System** - Dynamic pricing and promotions
- 📊 **Real-time Inventory** - Live stock management

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: TailwindCSS, ShadCN UI
- **State Management**: Zustand
- **Authentication**: Clerk
- **Payments**: Stripe
- **Animations**: Framer Motion
- **Icons**: Lucide React
- **Deployment**: Vercel Ready

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm

### Installation

1. **Clone the repository**
```bash
git clone <your-repo-url>
cd shoptech
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. **Set up environment variables**
```bash
cp .env.local.example .env.local
```

Fill in your environment variables:
```env
# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key
CLERK_SECRET_KEY=your_clerk_secret_key

# Stripe Payment
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_SECRET_KEY=your_stripe_secret_key

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **Run the development server**
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. **Open your browser**
Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/                 # Next.js App Router
│   ├── cart/           # Shopping cart page
│   ├── products/       # Products listing
│   ├── layout.tsx      # Root layout
│   └── page.tsx        # Homepage
├── components/         # Reusable components
│   ├── layout/        # Layout components
│   ├── product/       # Product components
│   ├── cart/          # Cart components
│   └── ui/            # ShadCN UI components
├── store/             # Zustand stores
├── types/             # TypeScript definitions
├── data/              # Sample data
└── lib/               # Utility functions
```

## 🎯 Key Features Explained

### Shopping Cart
- Persistent cart state with Zustand
- Add/remove items with quantity management
- Real-time price calculations
- Local storage persistence

### Product Management
- Dynamic product filtering and sorting
- Category and brand filtering
- Price range filtering
- Stock availability tracking

### Authentication
- Secure user authentication with Clerk
- User profile management
- Protected routes

### Payment Processing
- Stripe integration for secure payments
- Multiple payment methods support
- Order confirmation and tracking

## 🚀 Deployment

### Deploy on Vercel (Recommended)

1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms

The app can be deployed on any platform that supports Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework
- [TailwindCSS](https://tailwindcss.com/) - Utility-first CSS framework
- [ShadCN UI](https://ui.shadcn.com/) - Beautiful UI components
- [Clerk](https://clerk.dev/) - Authentication platform
- [Stripe](https://stripe.com/) - Payment processing
- [Zustand](https://github.com/pmndrs/zustand) - State management

---

**Built with ❤️ using Next.js 15 & React 19**
