/**
 * ---------------------------------------------------------------------------------
 * This file has been generated by Sanity TypeGen.
 * Command: `sanity typegen generate`
 *
 * Any modifications made directly to this file will be overwritten the next time
 * the TypeScript definitions are generated. Please make changes to the Sanity
 * schema definitions and/or GROQ queries if you need to update these types.
 *
 * For more information on how to use Sanity TypeGen, visit the official documentation:
 * https://www.sanity.io/docs/sanity-typegen
 * ---------------------------------------------------------------------------------
 */

// Source: schema.json
export type Commission = {
  _id: string;
  _type: "commission";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  memberId?: string;
  member?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "member";
  };
  fromMember?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "member";
  };
  order?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "order";
  };
  amount?: number;
  type?: "direct" | "binary" | "level" | "rank" | "leadership" | "special";
  level?: number;
  percentage?: number;
  status?: "pending" | "approved" | "paid" | "cancelled" | "on_hold";
  paidDate?: string;
  paymentMethod?: "wallet" | "bank_transfer" | "paypal" | "check";
  paymentReference?: string;
  notes?: string;
  calculatedAt?: string;
  period?: string;
};

export type Bid = {
  _id: string;
  _type: "bid";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  auction?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "auction";
  };
  userId?: string;
  userEmail?: string;
  amount?: number;
  status?: "active" | "outbid" | "winning" | "won" | "lost";
  isAutoBid?: boolean;
  maxAutoBid?: number;
  bidTime?: string;
  ipAddress?: string;
  userAgent?: string;
};

export type Order = {
  _id: string;
  _type: "order";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  orderNumber?: string;
  userId?: string;
  userEmail?: string;
  status?: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "refunded";
  items?: Array<{
    product?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "product";
    };
    quantity?: number;
    price?: number;
    totalPrice?: number;
    _key: string;
  }>;
  subtotal?: number;
  tax?: number;
  shipping?: number;
  discount?: number;
  totalAmount?: number;
  shippingAddress?: {
    firstName?: string;
    lastName?: string;
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
    phone?: string;
  };
  paymentMethod?: "credit_card" | "paypal" | "bank_transfer" | "wallet";
  paymentStatus?: "pending" | "paid" | "failed" | "refunded";
  paymentId?: string;
  trackingNumber?: string;
  notes?: string;
  mlmProcessed?: boolean;
};

export type Member = {
  _id: string;
  _type: "member";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  userId?: string;
  email?: string;
  firstName?: string;
  lastName?: string;
  level?: number;
  walletBalance?: number;
  totalEarnings?: number;
  status?: "active" | "inactive" | "suspended";
  parent?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "member";
  };
  leftChild?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "member";
  };
  rightChild?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "member";
  };
  leftVolume?: number;
  rightVolume?: number;
  personalVolume?: number;
  rank?: "associate" | "senior_associate" | "team_leader" | "senior_team_leader" | "manager" | "senior_manager" | "director" | "senior_director" | "executive";
  joinDate?: string;
  lastActiveDate?: string;
  referralCode?: string;
  bankDetails?: {
    accountNumber?: string;
    routingNumber?: string;
    bankName?: string;
    accountHolderName?: string;
  };
};

export type Auction = {
  _id: string;
  _type: "auction";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  title?: string;
  slug?: Slug;
  description?: string;
  product?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "product";
  };
  vendor?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vendor";
  };
  startingPrice?: number;
  currentBid?: number;
  startDate?: string;
  endDate?: string;
  status?: "scheduled" | "active" | "ended" | "cancelled";
  minBidIncrement?: number;
  reservePrice?: number;
  buyNowPrice?: number;
  winnerId?: string;
  winningBid?: number;
  totalBids?: number;
  featured?: boolean;
};

export type Product = {
  _id: string;
  _type: "product";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  description?: string;
  price?: number;
  stock?: number;
  category?: "electronics" | "clothing" | "home-garden" | "sports" | "books" | "health-beauty" | "automotive" | "other";
  images?: Array<{
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
    _key: string;
  }>;
  status?: "active" | "inactive" | "out_of_stock";
  vendor?: {
    _ref: string;
    _type: "reference";
    _weak?: boolean;
    [internalGroqTypeReferenceTo]?: "vendor";
  };
  tags?: Array<string>;
  specifications?: Array<{
    key?: string;
    value?: string;
    _key: string;
  }>;
};

export type Vendor = {
  _id: string;
  _type: "vendor";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  name?: string;
  slug?: Slug;
  userId?: string;
  email?: string;
  description?: string;
  logo?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  banner?: {
    asset?: {
      _ref: string;
      _type: "reference";
      _weak?: boolean;
      [internalGroqTypeReferenceTo]?: "sanity.imageAsset";
    };
    media?: unknown;
    hotspot?: SanityImageHotspot;
    crop?: SanityImageCrop;
    _type: "image";
  };
  status?: "pending" | "active" | "inactive" | "suspended";
  contactInfo?: {
    phone?: string;
    website?: string;
    address?: {
      street?: string;
      city?: string;
      state?: string;
      zipCode?: string;
      country?: string;
    };
  };
  businessInfo?: {
    businessName?: string;
    taxId?: string;
    businessType?: "individual" | "llc" | "corporation" | "partnership";
  };
  paymentInfo?: {
    accountNumber?: string;
    routingNumber?: string;
    bankName?: string;
    paypalEmail?: string;
  };
  totalSales?: number;
  commissionRate?: number;
  rating?: number;
  totalReviews?: number;
  joinDate?: string;
};

export type SanityImagePaletteSwatch = {
  _type: "sanity.imagePaletteSwatch";
  background?: string;
  foreground?: string;
  population?: number;
  title?: string;
};

export type SanityImagePalette = {
  _type: "sanity.imagePalette";
  darkMuted?: SanityImagePaletteSwatch;
  lightVibrant?: SanityImagePaletteSwatch;
  darkVibrant?: SanityImagePaletteSwatch;
  vibrant?: SanityImagePaletteSwatch;
  dominant?: SanityImagePaletteSwatch;
  lightMuted?: SanityImagePaletteSwatch;
  muted?: SanityImagePaletteSwatch;
};

export type SanityImageDimensions = {
  _type: "sanity.imageDimensions";
  height?: number;
  width?: number;
  aspectRatio?: number;
};

export type SanityImageHotspot = {
  _type: "sanity.imageHotspot";
  x?: number;
  y?: number;
  height?: number;
  width?: number;
};

export type SanityImageCrop = {
  _type: "sanity.imageCrop";
  top?: number;
  bottom?: number;
  left?: number;
  right?: number;
};

export type SanityFileAsset = {
  _id: string;
  _type: "sanity.fileAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  source?: SanityAssetSourceData;
};

export type SanityImageAsset = {
  _id: string;
  _type: "sanity.imageAsset";
  _createdAt: string;
  _updatedAt: string;
  _rev: string;
  originalFilename?: string;
  label?: string;
  title?: string;
  description?: string;
  altText?: string;
  sha1hash?: string;
  extension?: string;
  mimeType?: string;
  size?: number;
  assetId?: string;
  uploadId?: string;
  path?: string;
  url?: string;
  metadata?: SanityImageMetadata;
  source?: SanityAssetSourceData;
};

export type SanityImageMetadata = {
  _type: "sanity.imageMetadata";
  location?: Geopoint;
  dimensions?: SanityImageDimensions;
  palette?: SanityImagePalette;
  lqip?: string;
  blurHash?: string;
  hasAlpha?: boolean;
  isOpaque?: boolean;
};

export type Geopoint = {
  _type: "geopoint";
  lat?: number;
  lng?: number;
  alt?: number;
};

export type Slug = {
  _type: "slug";
  current?: string;
  source?: string;
};

export type SanityAssetSourceData = {
  _type: "sanity.assetSourceData";
  name?: string;
  id?: string;
  url?: string;
};

export type AllSanitySchemaTypes = Commission | Bid | Order | Member | Auction | Product | Vendor | SanityImagePaletteSwatch | SanityImagePalette | SanityImageDimensions | SanityImageHotspot | SanityImageCrop | SanityFileAsset | SanityImageAsset | SanityImageMetadata | Geopoint | Slug | SanityAssetSourceData;
export declare const internalGroqTypeReferenceTo: unique symbol;
